//go:generate go run main.go
package main

import (
	"bytes"
	"go/ast"
	"go/format"
	"go/parser"
	"go/token"
	"os"
	"sort"
	"text/template"
)

type TableName struct {
	Name    string
	IsIdent bool
}

type TableNames []TableName

func (t TableNames) Len() int {
	return len(t)
}
func (t TableNames) Swap(i, j int) {
	t[i], t[j] = t[j], t[i]
}
func (t TableNames) Less(i, j int) bool {
	return t[i].Name < t[j].Name
}

func main() {
	models := models()
	tmpl, err := template.New("gen_table_name").Parse(TableNameTemplate)
	if err != nil {
		panic(err)
	}

	buf := new(bytes.Buffer)
	err = tmpl.Execute(buf, models)
	if err != nil {
		panic(err)
	}

	data, err := format.Source(buf.Bytes())
	if err != nil {
		panic(err)
	}
	fd, err := os.Create("../../pkg/initialize/table_name.go")
	if err != nil {
		panic(err)
	}
	defer fd.Close()

	_, err = fd.Write(data)
	if err != nil {
		panic(err)
	}
}

func models() TableNames {
	var (
		srcDir  = "../../pkg/model"
		fset    = token.NewFileSet()
		results = make(TableNames, 0)
	)

	pkgs, err := parser.ParseDir(fset, srcDir, nil, parser.ParseComments)
	if err != nil {
		panic(err)
	}

	for _, pkg := range pkgs {
		for _, file := range pkg.Files {
			ast.Inspect(file, func(n ast.Node) bool {
				funcDecl, ok := n.(*ast.FuncDecl)
				if !ok {
					return true
				}

				if funcDecl.Recv == nil || len(funcDecl.Recv.List) == 0 {
					return true
				}
				if funcDecl.Name.Name != "TableName" {
					return true
				}

				switch recv := funcDecl.Recv.List[0].Type.(type) {
				case *ast.Ident:
					results = append(results, TableName{
						Name:    recv.Name,
						IsIdent: true,
					})
				case *ast.StarExpr:
					a, ok := recv.X.(*ast.Ident)
					if !ok {
						return true
					}
					results = append(results, TableName{
						Name: a.Name,
					})
				default:
					return true
				}
				return true
			})
		}
	}
	sort.Sort(results)
	return results
}

const TableNameTemplate = `
package initialize

import (
	"tw_platform/pkg/model"
	"tw_platform/pkg/system/table_name"
)

	func NewTables() *table_name.Tables {
		return &table_name.Tables{
			{{- range .}}
			{{if .IsIdent}}{{else}}new({{end}}model.{{.Name}}{{if .IsIdent}}{}{{else}}){{end}}.TableName(): make([]model.{{.Name}}, 0),
			{{- end}}
		}
	}
`
