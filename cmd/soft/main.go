package main

/*
#include <stdint.h>

// 定义 DWORD 和 WORD 类型
typedef uint32_t DWORD;
typedef uint16_t WORD;

// 声明函数原型
DWORD VikeyFind(DWORD* pdwCount);
DWORD VikeyGetHID(WORD Index, DWORD *pdwHID);
*/
import "C"
import (
	"hash/fnv"
	"os/exec"
)

//export VikeyFind
func VikeyFind(pdwCount *C.DWORD) C.DWORD {
	*pdwCount = C.DWORD(1) // 固定返回1个加密狗
	return 0               // 返回0表示成功
}

//export VikeyGetHID
func VikeyGetHID(Index C.WORD, pdwHID *C.DWORD) C.DWORD {
	// 获取 CPU 信息并生成硬件 ID
	hardwareID := generateHardwareID()
	*pdwHID = C.DWORD(hardwareID) // 返回硬件 ID
	return 0                      // 返回0表示成功
}

// 生成硬件 ID
func generateHardwareID() uint32 {
	// 获取 CPU 信息
	cpuInfo, err := getCPUInfo()
	if err != nil {
		return 123456789 // 如果获取失败，返回默认值
	}

	// 使用 FNV-1a 哈希算法生成 32 位硬件 ID
	hash := fnv.New32a()
	hash.Write([]byte(cpuInfo))
	return hash.Sum32()
}

// 获取 CPU 信息
func getCPUInfo() (string, error) {
	// 读取 /proc/cpuinfo 文件获取 CPU 信息
	cmd := exec.Command("cat", "/proc/cpuinfo")
	output, err := cmd.Output()
	if err != nil {
		return "", err
	}

	// 提取 CPU 信息中的唯一标识符（如 Serial 或 Model）
	cpuInfo := string(output)
	return cpuInfo, nil
}

func main() {} // 必须有一个main函数，即使它是空的
