mysql:
  name: kepu_cloud
  password: kepu_cloud
  host: 127.0.0.1
  port: 3306
  db: kepu_cloud

redis:
  host: 127.0.0.1
  port: 6379
  username: xxxx
  password: password123
  db: 0 

web-server:
  listen: 0.0.0.0
  port: 8082

influxdb:
  addr: "http://************:18086"
  token: BfG_zW41Wddgbuig0GXt6TuDWpHjUgjTJGDFi9ZI6fOXeYwyWKhakTrwbRT8f4uqFQCWXbRGxs8f5GaChW5tqw==

postgresql:
  name: kepu_cloud
  password: kepu_cloud
  host: 127.0.0.1
  port: 5432
  db: kepu_cloud
mqtt:
  uri: tcp://************:1883
  name: 
  password:

jwt:
  secret: 123456
  expired_time: 7

email:
  host: 127.0.0.1
  port: 25

device-capture:
  host: ************
  port: 18888

video-rtmp:
  addr: "127.0.0.1"

video-resolution:
  resolution: "480x320"

qywx:
  corp_id: 
  secret:
  app_secret: