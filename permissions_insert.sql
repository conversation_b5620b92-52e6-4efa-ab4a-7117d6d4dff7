-- 权限表插入语句
-- 基于路由定义生成的权限数据
-- GET=1, POST=2
-- 注意：系统权限中间件目前只支持GET和POST方法，PUT/DELETE方法会返回"method error"
-- 因此本文件只包含GET和POST路由的权限定义

-- 用户管理相关权限
INSERT INTO permissions (name, path, method) VALUES ('用户-登录', '/api/v1/login', 2);
INSERT INTO permissions (name, path, method) VALUES ('用户-企业微信登录', '/api/v1/qy_login', 2);
INSERT INTO permissions (name, path, method) VALUES ('用户-创建', '/api/v1/user/create', 2);
INSERT INTO permissions (name, path, method) VALUES ('用户-列表', '/api/v1/user/list', 1);
INSERT INTO permissions (name, path, method) VALUES ('用户-设置区域', '/api/v1/user/areas', 2);
INSERT INTO permissions (name, path, method) VALUES ('用户-获取区域', '/api/v1/user/areas', 1);
INSERT INTO permissions (name, path, method) VALUES ('用户-设置部门', '/api/v1/user/departments', 2);
INSERT INTO permissions (name, path, method) VALUES ('用户-获取部门', '/api/v1/user/departments', 1);
INSERT INTO permissions (name, path, method) VALUES ('用户-设置业务类型', '/api/v1/user/business_types', 2);
INSERT INTO permissions (name, path, method) VALUES ('用户-获取业务类型', '/api/v1/user/business_types', 1);
INSERT INTO permissions (name, path, method) VALUES ('用户-更新', '/api/v1/user/update', 2);
INSERT INTO permissions (name, path, method) VALUES ('用户-修改密码', '/api/v1/user/change_password', 2);
INSERT INTO permissions (name, path, method) VALUES ('用户-重置密码', '/api/v1/user/reset_password', 2);
INSERT INTO permissions (name, path, method) VALUES ('用户-简化列表', '/api/v1/user/list_simplify', 1);
INSERT INTO permissions (name, path, method) VALUES ('用户-删除', '/api/v1/user/delete', 2);
INSERT INTO permissions (name, path, method) VALUES ('用户-按ID列表', '/api/v1/user/list_by_id', 2);
INSERT INTO permissions (name, path, method) VALUES ('用户-按菜单ID列表', '/api/v1/user/list_by_menu_id', 1);
INSERT INTO permissions (name, path, method) VALUES ('用户-企业微信获取', '/api/v1/user/qy_fetch', 2);
INSERT INTO permissions (name, path, method) VALUES ('用户-自我更新', '/api/v1/user/self_update', 2);
INSERT INTO permissions (name, path, method) VALUES ('用户-信息', '/api/v1/user/info', 1);
INSERT INTO permissions (name, path, method) VALUES ('用户-无分页列表', '/api/v1/user/list_without_page', 1);

-- 角色管理相关权限
INSERT INTO permissions (name, path, method) VALUES ('角色-创建', '/api/v1/role/create', 2);
INSERT INTO permissions (name, path, method) VALUES ('角色-列表', '/api/v1/role/list', 1);
INSERT INTO permissions (name, path, method) VALUES ('角色-更新', '/api/v1/role/update', 2);
INSERT INTO permissions (name, path, method) VALUES ('角色-删除', '/api/v1/role/delete', 2);
INSERT INTO permissions (name, path, method) VALUES ('角色-设置菜单', '/api/v1/role/menus', 2);
INSERT INTO permissions (name, path, method) VALUES ('角色-获取菜单', '/api/v1/role/menus', 1);
INSERT INTO permissions (name, path, method) VALUES ('角色-可分配列表', '/api/v1/role/allocable_list', 1);
INSERT INTO permissions (name, path, method) VALUES ('角色-权限列表', '/api/v1/role/permissions', 1);
INSERT INTO permissions (name, path, method) VALUES ('角色-设置权限', '/api/v1/role/permissions', 2);

-- 菜单管理相关权限
INSERT INTO permissions (name, path, method) VALUES ('菜单-创建', '/api/v1/menu/create', 2);
INSERT INTO permissions (name, path, method) VALUES ('菜单-树形结构', '/api/v1/menu/tree', 1);
INSERT INTO permissions (name, path, method) VALUES ('菜单-删除', '/api/v1/menu/delete', 2);
INSERT INTO permissions (name, path, method) VALUES ('菜单-详情', '/api/v1/menu/detail', 1);
INSERT INTO permissions (name, path, method) VALUES ('菜单-更新', '/api/v1/menu/update', 2);
INSERT INTO permissions (name, path, method) VALUES ('菜单-按角色列表', '/api/v1/menu/list_by_role', 1);
INSERT INTO permissions (name, path, method) VALUES ('菜单-按父ID列表', '/api/v1/menu/list_by_pid', 1);

-- 权限管理相关权限
INSERT INTO permissions (name, path, method) VALUES ('权限-创建', '/api/v1/permission/create', 2);
INSERT INTO permissions (name, path, method) VALUES ('权限-列表', '/api/v1/permission/list', 1);
INSERT INTO permissions (name, path, method) VALUES ('权限-更新', '/api/v1/permission/update', 2);
INSERT INTO permissions (name, path, method) VALUES ('权限-删除', '/api/v1/permission/delete', 2);

-- 区域管理相关权限
INSERT INTO permissions (name, path, method) VALUES ('区域-创建', '/api/v1/area/create', 2);
INSERT INTO permissions (name, path, method) VALUES ('区域-树形结构', '/api/v1/area/tree', 1);
INSERT INTO permissions (name, path, method) VALUES ('区域-仅树形结构', '/api/v1/area/tree_only', 1);
INSERT INTO permissions (name, path, method) VALUES ('区域-按业务类型树形', '/api/v1/area/tree_only_by_bt_id', 1);
INSERT INTO permissions (name, path, method) VALUES ('区域-全部', '/api/v1/area/all', 1);
INSERT INTO permissions (name, path, method) VALUES ('区域-全部含统计', '/api/v1/area/all_with_counts', 1);
INSERT INTO permissions (name, path, method) VALUES ('区域-全部含设备', '/api/v1/area/all_with_devices', 1);
INSERT INTO permissions (name, path, method) VALUES ('区域-树形含设备配置', '/api/v1/area/tree_with_device_configs', 1);
INSERT INTO permissions (name, path, method) VALUES ('区域-删除', '/api/v1/area/delete', 2);
INSERT INTO permissions (name, path, method) VALUES ('区域-更新', '/api/v1/area/update', 2);
INSERT INTO permissions (name, path, method) VALUES ('区域-PUE', '/api/v1/area/pue', 1);
INSERT INTO permissions (name, path, method) VALUES ('区域-按父ID列表', '/api/v1/area/list_by_pid', 1);
INSERT INTO permissions (name, path, method) VALUES ('区域-按父ID递归列表', '/api/v1/area/list_by_pid_recursive', 1);
INSERT INTO permissions (name, path, method) VALUES ('区域-每小时PUE', '/api/v1/area/pue_every_hour', 1);
INSERT INTO permissions (name, path, method) VALUES ('区域-树形含报警统计', '/api/v1/area/tree_with_alarm_counts', 1);
INSERT INTO permissions (name, path, method) VALUES ('区域-按父ID含报警列表', '/api/v1/area/list_with_alarmed_by_pid', 1);
INSERT INTO permissions (name, path, method) VALUES ('区域-按业务类型树形含设备', '/api/v1/area/tree_with_devices_by_btid', 1);
INSERT INTO permissions (name, path, method) VALUES ('区域-ID列表', '/api/v1/area/ids', 1);
INSERT INTO permissions (name, path, method) VALUES ('区域-实时状态', '/api/v1/area/realtime_status', 1);
INSERT INTO permissions (name, path, method) VALUES ('区域-按父ID视频列表', '/api/v1/area/video_list_by_pid_tile', 1);

-- 区域场景相关权限
INSERT INTO permissions (name, path, method) VALUES ('场景-创建', '/api/v1/area/scene/create', 2);
INSERT INTO permissions (name, path, method) VALUES ('场景-详情', '/api/v1/area/scene/detail', 1);
INSERT INTO permissions (name, path, method) VALUES ('场景-更新', '/api/v1/area/scene/update', 2);
INSERT INTO permissions (name, path, method) VALUES ('场景-默认类型', '/api/v1/area/scene/default', 2);
INSERT INTO permissions (name, path, method) VALUES ('场景-默认类型列表', '/api/v1/area/scene/default', 1);
INSERT INTO permissions (name, path, method) VALUES ('场景-删除', '/api/v1/area/scene/delete', 2);

-- 部门管理相关权限
INSERT INTO permissions (name, path, method) VALUES ('部门-创建', '/api/v1/department/create', 2);
INSERT INTO permissions (name, path, method) VALUES ('部门-全部', '/api/v1/department/all', 1);
INSERT INTO permissions (name, path, method) VALUES ('部门-全部含统计', '/api/v1/department/all_with_counts', 1);
INSERT INTO permissions (name, path, method) VALUES ('部门-树形结构', '/api/v1/department/tree', 1);
INSERT INTO permissions (name, path, method) VALUES ('部门-仅树形结构', '/api/v1/department/tree_only', 1);
INSERT INTO permissions (name, path, method) VALUES ('部门-更新', '/api/v1/department/update', 2);
INSERT INTO permissions (name, path, method) VALUES ('部门-删除', '/api/v1/department/delete', 2);

-- 业务类型管理相关权限
INSERT INTO permissions (name, path, method) VALUES ('业务-创建', '/api/v1/business_type/create', 2);
INSERT INTO permissions (name, path, method) VALUES ('业务-仅树形结构', '/api/v1/business_type/tree_only', 1);
INSERT INTO permissions (name, path, method) VALUES ('业务-列表含信息', '/api/v1/business_type/list_with_info', 1);
INSERT INTO permissions (name, path, method) VALUES ('业务-更新', '/api/v1/business_type/update', 2);
INSERT INTO permissions (name, path, method) VALUES ('业务-删除', '/api/v1/business_type/delete', 2);
INSERT INTO permissions (name, path, method) VALUES ('业务-按父ID列表', '/api/v1/business_type/list_by_pid', 1);
INSERT INTO permissions (name, path, method) VALUES ('业务-列表-存在设备', '/api/v1/business_type/list_has_device', 1);
INSERT INTO permissions (name, path, method) VALUES ('业务-用户树形结构', '/api/v1/business_type/tree_only_user', 1);

-- 设备管理相关权限
INSERT INTO permissions (name, path, method) VALUES ('设备-创建PCE-M', '/api/v1/device/create_pce_m', 2);
INSERT INTO permissions (name, path, method) VALUES ('设备-创建', '/api/v1/device/create', 2);
INSERT INTO permissions (name, path, method) VALUES ('设备-创建自定义', '/api/v1/device/create_custom', 2);
INSERT INTO permissions (name, path, method) VALUES ('设备-批量创建自定义', '/api/v1/device/batch_create_custom', 2);
INSERT INTO permissions (name, path, method) VALUES ('设备-创建OPC', '/api/v1/device/create_opc', 2);
INSERT INTO permissions (name, path, method) VALUES ('设备-删除', '/api/v1/device/delete', 2);
INSERT INTO permissions (name, path, method) VALUES ('设备-配置', '/api/v1/device/config', 1);
INSERT INTO permissions (name, path, method) VALUES ('设备-更新', '/api/v1/device/update', 2);
INSERT INTO permissions (name, path, method) VALUES ('设备-更新单元', '/api/v1/device/unit', 2);
INSERT INTO permissions (name, path, method) VALUES ('设备-更新配置', '/api/v1/device/config', 2);
INSERT INTO permissions (name, path, method) VALUES ('设备-列表', '/api/v1/device/list', 1);
INSERT INTO permissions (name, path, method) VALUES ('设备-门禁列表', '/api/v1/device/door_list', 1);
INSERT INTO permissions (name, path, method) VALUES ('设备-按业务类型列表', '/api/v1/device/list_by_business_type_id', 1);
INSERT INTO permissions (name, path, method) VALUES ('设备-历史数据', '/api/v1/device/history', 2);
INSERT INTO permissions (name, path, method) VALUES ('设备-实时数据', '/api/v1/device/realtime_data', 1);
INSERT INTO permissions (name, path, method) VALUES ('设备-3D实时数据', '/api/v1/device/realtime_data_for_3d', 2);
INSERT INTO permissions (name, path, method) VALUES ('设备-实时状态', '/api/v1/device/realtime_status', 1);
INSERT INTO permissions (name, path, method) VALUES ('设备-控制', '/api/v1/device/control', 2);
INSERT INTO permissions (name, path, method) VALUES ('设备-名称列表', '/api/v1/device/name_list', 2);
INSERT INTO permissions (name, path, method) VALUES ('设备-按区域ID列表', '/api/v1/device/list_by_area_id', 1);
INSERT INTO permissions (name, path, method) VALUES ('设备-列表含配置', '/api/v1/device/list_with_config', 1);
INSERT INTO permissions (name, path, method) VALUES ('设备-自定义列表', '/api/v1/device/custom_list', 1);
INSERT INTO permissions (name, path, method) VALUES ('设备-按端口列表', '/api/v1/device/list_by_port', 1);
INSERT INTO permissions (name, path, method) VALUES ('设备-按绑定报警列表', '/api/v1/device/list_by_binding_alarmed', 1);
INSERT INTO permissions (name, path, method) VALUES ('设备-绑定报警', '/api/v1/device/binding_alarmed', 2);
INSERT INTO permissions (name, path, method) VALUES ('设备-按区域ID递归列表', '/api/v1/device/list_by_area_id_recursive', 1);
INSERT INTO permissions (name, path, method) VALUES ('设备-详情', '/api/v1/device/detail', 1);
INSERT INTO permissions (name, path, method) VALUES ('设备-按驱动更新单元', '/api/v1/device/unit_by_driver', 2);
INSERT INTO permissions (name, path, method) VALUES ('设备-按驱动名称列表', '/api/v1/device/names_by_driver', 1);
INSERT INTO permissions (name, path, method) VALUES ('设备-关闭声光', '/api/v1/device/sound_light', 2);
INSERT INTO permissions (name, path, method) VALUES ('设备-创建资产', '/api/v1/device/create_asset', 2);
INSERT INTO permissions (name, path, method) VALUES ('设备-更新资产', '/api/v1/device/update_asset', 2);
INSERT INTO permissions (name, path, method) VALUES ('设备-报告列表', '/api/v1/device/report_list', 1);
INSERT INTO permissions (name, path, method) VALUES ('设备-设置报告信息', '/api/v1/device/report_info', 2);
INSERT INTO permissions (name, path, method) VALUES ('设备-资产详情', '/api/v1/device/detail_asset', 1);

-- 网关管理相关权限
INSERT INTO permissions (name, path, method) VALUES ('网关-创建', '/api/v1/gateway/create', 2);
INSERT INTO permissions (name, path, method) VALUES ('网关-是否接受', '/api/v1/gateway/is_accept', 1);
INSERT INTO permissions (name, path, method) VALUES ('网关-更新', '/api/v1/gateway/update', 2);
INSERT INTO permissions (name, path, method) VALUES ('网关-列表', '/api/v1/gateway/list', 1);
INSERT INTO permissions (name, path, method) VALUES ('网关-批量接受', '/api/v1/gateway/is_accept_multi', 2);
INSERT INTO permissions (name, path, method) VALUES ('网关-删除', '/api/v1/gateway/delete', 2);
INSERT INTO permissions (name, path, method) VALUES ('网关-型号列表', '/api/v1/gateway/models', 1);
INSERT INTO permissions (name, path, method) VALUES ('网关-扫描', '/api/v1/gateway/scan', 1);
INSERT INTO permissions (name, path, method) VALUES ('网关-适配器列表', '/api/v1/gateway/adapters', 1);
INSERT INTO permissions (name, path, method) VALUES ('网关-绑定报警', '/api/v1/gateway/binding_alarmed', 2);
INSERT INTO permissions (name, path, method) VALUES ('网关-绑定报警列表', '/api/v1/gateway/binding_alarmed', 1);
INSERT INTO permissions (name, path, method) VALUES ('网关-批量创建', '/api/v1/gateway/batch_create', 2);
INSERT INTO permissions (name, path, method) VALUES ('网关-统计', '/api/v1/gateway/statistic', 1);
INSERT INTO permissions (name, path, method) VALUES ('网关-替换', '/api/v1/gateway/replace', 2);

-- 网关端口相关权限
INSERT INTO permissions (name, path, method) VALUES ('端口-更新', '/api/v1/gateway/port/update', 2);
INSERT INTO permissions (name, path, method) VALUES ('端口-添加', '/api/v1/gateway/port/add', 2);
INSERT INTO permissions (name, path, method) VALUES ('端口-删除', '/api/v1/gateway/port/delete', 2);
INSERT INTO permissions (name, path, method) VALUES ('端口-列表', '/api/v1/gateway/port/list', 1);

-- OPC相关权限
INSERT INTO permissions (name, path, method) VALUES ('OPC-节点列表', '/api/v1/gateway/opc/list', 1);
INSERT INTO permissions (name, path, method) VALUES ('OPC-节点变量', '/api/v1/gateway/opc/variable', 1);

-- 驱动管理相关权限
INSERT INTO permissions (name, path, method) VALUES ('驱动-创建', '/api/v1/driver/create', 2);
INSERT INTO permissions (name, path, method) VALUES ('驱动-列表', '/api/v1/driver/list', 1);
INSERT INTO permissions (name, path, method) VALUES ('驱动-详情', '/api/v1/driver/detail', 1);
INSERT INTO permissions (name, path, method) VALUES ('驱动-删除', '/api/v1/driver/delete', 2);
INSERT INTO permissions (name, path, method) VALUES ('驱动-类型列表', '/api/v1/driver/type_list', 1);
INSERT INTO permissions (name, path, method) VALUES ('驱动-厂商列表', '/api/v1/driver/manu_facturer_list', 1);
INSERT INTO permissions (name, path, method) VALUES ('驱动-协议列表', '/api/v1/driver/agree_list', 1);

-- 视频管理相关权限
INSERT INTO permissions (name, path, method) VALUES ('视频-列表', '/api/v1/video/list', 1);
INSERT INTO permissions (name, path, method) VALUES ('视频-设置列表', '/api/v1/video/list', 2);
INSERT INTO permissions (name, path, method) VALUES ('视频-播放', '/api/v1/video/play', 2);
INSERT INTO permissions (name, path, method) VALUES ('视频-预览', '/api/v1/video/preview', 2);
INSERT INTO permissions (name, path, method) VALUES ('视频-回放', '/api/v1/video/playback', 2);
INSERT INTO permissions (name, path, method) VALUES ('视频-停止播放', '/api/v1/video/stop_playing', 2);
INSERT INTO permissions (name, path, method) VALUES ('视频-保持连接', '/api/v1/video/keep_alive', 1);
INSERT INTO permissions (name, path, method) VALUES ('视频-截图', '/api/v1/video/capture_image', 1);

-- 系统管理相关权限
INSERT INTO permissions (name, path, method) VALUES ('系统-信息', '/api/v1/system/info', 1);
INSERT INTO permissions (name, path, method) VALUES ('系统-更新信息', '/api/v1/system/info', 2);
INSERT INTO permissions (name, path, method) VALUES ('系统-SSE升级页面', '/api/v1/system/sse_upgrade_pages', 2);
INSERT INTO permissions (name, path, method) VALUES ('系统-升级页面', '/api/v1/system/upgrade_pages', 2);
INSERT INTO permissions (name, path, method) VALUES ('系统-升级Logo', '/api/v1/system/upgrade_logo', 2);
INSERT INTO permissions (name, path, method) VALUES ('系统-统计', '/api/v1/system/statistic', 1);
INSERT INTO permissions (name, path, method) VALUES ('系统-版本信息', '/api/v1/system/version', 1);
INSERT INTO permissions (name, path, method) VALUES ('系统-应用统计', '/api/v1/system/statistic_app', 1);

-- 联动管理相关权限
INSERT INTO permissions (name, path, method) VALUES ('联动-列表', '/api/v1/linkage/list', 1);
INSERT INTO permissions (name, path, method) VALUES ('联动-名称', '/api/v1/linkage/name', 1);
INSERT INTO permissions (name, path, method) VALUES ('联动-详情', '/api/v1/linkage/detail', 1);
INSERT INTO permissions (name, path, method) VALUES ('联动-创建', '/api/v1/linkage/linkage', 2);

-- 通知组管理相关权限
INSERT INTO permissions (name, path, method) VALUES ('通知组-创建', '/api/v1/notification_group/create', 2);
INSERT INTO permissions (name, path, method) VALUES ('通知组-列表', '/api/v1/notification_group/list', 1);
INSERT INTO permissions (name, path, method) VALUES ('通知组-删除', '/api/v1/notification_group/delete', 2);
INSERT INTO permissions (name, path, method) VALUES ('通知组-更新', '/api/v1/notification_group/update', 2);
INSERT INTO permissions (name, path, method) VALUES ('通知组-GPRS配置', '/api/v1/notification_group/gprs_config', 1);
INSERT INTO permissions (name, path, method) VALUES ('通知组-邮件配置', '/api/v1/notification_group/email_config', 1);
INSERT INTO permissions (name, path, method) VALUES ('通知组-更新GPRS配置', '/api/v1/notification_group/update_gprs_config', 2);
INSERT INTO permissions (name, path, method) VALUES ('通知组-更新邮件配置', '/api/v1/notification_group/update_email_config', 2);
INSERT INTO permissions (name, path, method) VALUES ('通知组-4G详情', '/api/v1/notification_group/4gdetail', 1);
INSERT INTO permissions (name, path, method) VALUES ('通知组-4G余额', '/api/v1/notification_group/4gye', 1);

-- SNMP Trap相关权限
INSERT INTO permissions (name, path, method) VALUES ('SNMP-创建', '/api/v1/snmp_trap/create', 2);
INSERT INTO permissions (name, path, method) VALUES ('SNMP-列表', '/api/v1/snmp_trap/list', 1);
INSERT INTO permissions (name, path, method) VALUES ('SNMP-删除', '/api/v1/snmp_trap/delete', 2);
INSERT INTO permissions (name, path, method) VALUES ('SNMP-更新', '/api/v1/snmp_trap/update', 2);

-- 定时任务相关权限
INSERT INTO permissions (name, path, method) VALUES ('定时任务-创建', '/api/v1/cron_task/create', 2);
INSERT INTO permissions (name, path, method) VALUES ('定时任务-列表', '/api/v1/cron_task/list', 1);
INSERT INTO permissions (name, path, method) VALUES ('定时任务-详情', '/api/v1/cron_task/detail', 1);
INSERT INTO permissions (name, path, method) VALUES ('定时任务-删除', '/api/v1/cron_task/delete', 2);
INSERT INTO permissions (name, path, method) VALUES ('定时任务-更新', '/api/v1/cron_task/update', 2);
INSERT INTO permissions (name, path, method) VALUES ('定时任务-全部', '/api/v1/cron_task/all', 1);

-- 报警组相关权限
INSERT INTO permissions (name, path, method) VALUES ('报警组-创建', '/api/v1/alarm_group/create', 2);
INSERT INTO permissions (name, path, method) VALUES ('报警组-列表', '/api/v1/alarm_group/list', 1);
INSERT INTO permissions (name, path, method) VALUES ('报警组-删除', '/api/v1/alarm_group/delete', 2);
INSERT INTO permissions (name, path, method) VALUES ('报警组-更新', '/api/v1/alarm_group/update', 2);

-- 报警相关权限
INSERT INTO permissions (name, path, method) VALUES ('报警-历史列表', '/api/v1/alarm/history_list', 1);
INSERT INTO permissions (name, path, method) VALUES ('报警-实时列表', '/api/v1/alarm/realtime_list', 1);
INSERT INTO permissions (name, path, method) VALUES ('报警-处理', '/api/v1/alarm/handle', 2);
INSERT INTO permissions (name, path, method) VALUES ('报警-设置状态', '/api/v1/alarm/status', 2);
INSERT INTO permissions (name, path, method) VALUES ('报警-时间统计', '/api/v1/alarm/total_of_time', 1);
INSERT INTO permissions (name, path, method) VALUES ('报警-导出', '/api/v1/alarm/export', 1);
INSERT INTO permissions (name, path, method) VALUES ('报警-实时WebSocket', '/api/v1/alarm/realtime_ws', 1);
INSERT INTO permissions (name, path, method) VALUES ('报警-导出列表', '/api/v1/alarm/export_list', 1);
INSERT INTO permissions (name, path, method) VALUES ('报警-订阅', '/api/v1/alarm/subscribe', 1);
INSERT INTO permissions (name, path, method) VALUES ('报警-移除', '/api/v1/alarm/remove', 2);
INSERT INTO permissions (name, path, method) VALUES ('报警-同步', '/api/v1/alarm/sync', 2);
INSERT INTO permissions (name, path, method) VALUES ('报警-申请发布', '/api/v1/alarm/apply_publish', 2);
INSERT INTO permissions (name, path, method) VALUES ('报警-创建', '/api/v1/alarm/create', 2);
INSERT INTO permissions (name, path, method) VALUES ('报警-恢复', '/api/v1/alarm/recovery', 2);

-- 资产管理相关权限
INSERT INTO permissions (name, path, method) VALUES ('机柜-创建', '/api/v1/asset/cabinet/create', 2);
INSERT INTO permissions (name, path, method) VALUES ('机柜-更新', '/api/v1/asset/cabinet/update', 2);
INSERT INTO permissions (name, path, method) VALUES ('机柜-列表', '/api/v1/asset/cabinet/list', 1);
INSERT INTO permissions (name, path, method) VALUES ('机柜-删除', '/api/v1/asset/cabinet/delete', 2);
INSERT INTO permissions (name, path, method) VALUES ('资产-创建', '/api/v1/asset/create', 2);
INSERT INTO permissions (name, path, method) VALUES ('资产-更新', '/api/v1/asset/update', 2);
INSERT INTO permissions (name, path, method) VALUES ('资产-列表', '/api/v1/asset/list', 1);
INSERT INTO permissions (name, path, method) VALUES ('资产-删除', '/api/v1/asset/delete', 2);

-- 事件任务相关权限
INSERT INTO permissions (name, path, method) VALUES ('事件任务-创建', '/api/v1/event_task/create', 2);
INSERT INTO permissions (name, path, method) VALUES ('事件任务-列表', '/api/v1/event_task/list', 1);
INSERT INTO permissions (name, path, method) VALUES ('事件任务-详情', '/api/v1/event_task/detail', 1);
INSERT INTO permissions (name, path, method) VALUES ('事件任务-删除', '/api/v1/event_task/delete', 2);
INSERT INTO permissions (name, path, method) VALUES ('事件任务-更新', '/api/v1/event_task/update', 2);

-- 虚拟变量相关权限
INSERT INTO permissions (name, path, method) VALUES ('虚拟变量-创建', '/api/v1/virtual_variable/create', 2);
INSERT INTO permissions (name, path, method) VALUES ('虚拟变量-列表', '/api/v1/virtual_variable/list', 1);
INSERT INTO permissions (name, path, method) VALUES ('虚拟变量-更新', '/api/v1/virtual_variable/update', 2);
INSERT INTO permissions (name, path, method) VALUES ('虚拟变量-更新单元', '/api/v1/virtual_variable/unit', 2);
INSERT INTO permissions (name, path, method) VALUES ('虚拟变量-删除', '/api/v1/virtual_variable/delete', 2);
INSERT INTO permissions (name, path, method) VALUES ('虚拟变量-详情', '/api/v1/virtual_variable/detail', 1);

-- 巡检相关权限
INSERT INTO permissions (name, path, method) VALUES ('巡检-创建', '/api/v1/inspection/create', 2);
INSERT INTO permissions (name, path, method) VALUES ('巡检-列表', '/api/v1/inspection/list', 1);
INSERT INTO permissions (name, path, method) VALUES ('巡检-计划列表', '/api/v1/inspection/plan_list', 1);
INSERT INTO permissions (name, path, method) VALUES ('巡检-执行', '/api/v1/inspection/execute', 2);
INSERT INTO permissions (name, path, method) VALUES ('巡检-上传', '/api/v1/inspection/upload', 2);
INSERT INTO permissions (name, path, method) VALUES ('巡检-删除文件', '/api/v1/inspection/delete_file', 2);

-- 日志相关权限
INSERT INTO permissions (name, path, method) VALUES ('日志-操作列表', '/api/v1/log/operation_list', 1);
INSERT INTO permissions (name, path, method) VALUES ('日志-报警列表', '/api/v1/log/alarm_list', 1);
INSERT INTO permissions (name, path, method) VALUES ('日志-使用情况', '/api/v1/log/usage', 1);
INSERT INTO permissions (name, path, method) VALUES ('日志-清空', '/api/v1/log/truncate', 2);

-- 备用系统相关权限
INSERT INTO permissions (name, path, method) VALUES ('备用-统计', '/api/v1/standby/stats', 1);
INSERT INTO permissions (name, path, method) VALUES ('备用-设置信息', '/api/v1/standby/info', 2);
INSERT INTO permissions (name, path, method) VALUES ('备用-获取信息', '/api/v1/standby/info', 1);
INSERT INTO permissions (name, path, method) VALUES ('备用-同步', '/api/v1/standby/sync', 1);
INSERT INTO permissions (name, path, method) VALUES ('备用-同步表', '/api/v1/standby/sync_tables', 1);
INSERT INTO permissions (name, path, method) VALUES ('备用-推送表', '/api/v1/standby/push_tables', 2);
INSERT INTO permissions (name, path, method) VALUES ('备用-获取推送', '/api/v1/standby/fetch_tables', 2);
INSERT INTO permissions (name, path, method) VALUES ('备用-设备数据', '/api/v1/standby/device_data', 1);

-- 维护相关权限
INSERT INTO permissions (name, path, method) VALUES ('维护-创建', '/api/v1/maintain/create', 2);
INSERT INTO permissions (name, path, method) VALUES ('维护-列表', '/api/v1/maintain/list', 1);
INSERT INTO permissions (name, path, method) VALUES ('维护-计划列表', '/api/v1/maintain/plans', 1);
INSERT INTO permissions (name, path, method) VALUES ('维护-执行', '/api/v1/maintain/execute', 2);

-- 媒体服务相关权限
INSERT INTO permissions (name, path, method) VALUES ('媒体-生成', '/api/v1/mediamtx/generate', 2);
INSERT INTO permissions (name, path, method) VALUES ('媒体-设置中继', '/api/v1/mediamtx/setturn', 2);

-- 联动捕获相关权限
INSERT INTO permissions (name, path, method) VALUES ('联动捕获-操作列表', '/api/v1/linkage_capture/operation_list', 1);
INSERT INTO permissions (name, path, method) VALUES ('联动捕获-实时数据', '/api/v1/linkage_capture/realtime', 1);

-- 灯光调度相关权限
INSERT INTO permissions (name, path, method) VALUES ('灯光调度-创建', '/api/v1/light_schedule/create', 2);
INSERT INTO permissions (name, path, method) VALUES ('灯光调度-更新', '/api/v1/light_schedule/update', 2);
INSERT INTO permissions (name, path, method) VALUES ('灯光调度-详情', '/api/v1/light_schedule/detail', 1);
INSERT INTO permissions (name, path, method) VALUES ('灯光调度-列表', '/api/v1/light_schedule/list', 1);
INSERT INTO permissions (name, path, method) VALUES ('灯光调度-删除', '/api/v1/light_schedule/delete', 2);
INSERT INTO permissions (name, path, method) VALUES ('灯光调度-按区域控制', '/api/v1/light_schedule/control_by_area', 2);
INSERT INTO permissions (name, path, method) VALUES ('灯光调度-统计', '/api/v1/light_schedule/statistic', 1);
INSERT INTO permissions (name, path, method) VALUES ('灯光调度-全部统计', '/api/v1/light_schedule/statistic_all', 1);
INSERT INTO permissions (name, path, method) VALUES ('灯光调度-全部控制', '/api/v1/light_schedule/control_all', 2);

-- 定时作业相关权限
INSERT INTO permissions (name, path, method) VALUES ('定时作业-创建', '/api/v1/cronjob/create', 2);
INSERT INTO permissions (name, path, method) VALUES ('定时作业-更新', '/api/v1/cronjob/update', 2);
INSERT INTO permissions (name, path, method) VALUES ('定时作业-列表', '/api/v1/cronjob/list', 1);
INSERT INTO permissions (name, path, method) VALUES ('定时作业-删除', '/api/v1/cronjob/delete', 2);

-- 工单相关权限
INSERT INTO permissions (name, path, method) VALUES ('工单-创建', '/api/v1/ticket/create', 2);
INSERT INTO permissions (name, path, method) VALUES ('工单-列表', '/api/v1/ticket/list', 1);
INSERT INTO permissions (name, path, method) VALUES ('工单-时间线', '/api/v1/ticket/timeline', 1);
INSERT INTO permissions (name, path, method) VALUES ('工单-更新', '/api/v1/ticket/update', 2);
INSERT INTO permissions (name, path, method) VALUES ('工单-详情', '/api/v1/ticket/detail', 1);
INSERT INTO permissions (name, path, method) VALUES ('工单-创建资产', '/api/v1/ticket/create_asset', 2);
INSERT INTO permissions (name, path, method) VALUES ('工单-更新资产', '/api/v1/ticket/update_asset', 2);

-- 注册相关权限
INSERT INTO permissions (name, path, method) VALUES ('注册-功能码', '/api/register/v1/funcode', 1);
INSERT INTO permissions (name, path, method) VALUES ('注册-功能权限', '/api/register/v1/function', 1);
INSERT INTO permissions (name, path, method) VALUES ('注册-设置功能权限', '/api/register/v1/function', 2);
INSERT INTO permissions (name, path, method) VALUES ('注册-注册信息', '/api/register/v1/registe', 1);
INSERT INTO permissions (name, path, method) VALUES ('注册-项目激活', '/api/register/v1/registe', 2);
INSERT INTO permissions (name, path, method) VALUES ('注册-3D权限', '/api/register/v1/3d_permission', 1);

-- 门禁管理相关权限
INSERT INTO permissions (name, path, method) VALUES ('门禁-列表', '/api/v1/door/list', 1);
INSERT INTO permissions (name, path, method) VALUES ('门禁-新增', '/api/v1/door/list', 2);
INSERT INTO permissions (name, path, method) VALUES ('门禁-操作', '/api/v1/door/operation', 2);
INSERT INTO permissions (name, path, method) VALUES ('门禁-状态', '/api/v1/door/status', 1);
INSERT INTO permissions (name, path, method) VALUES ('门禁-用户列表', '/api/v1/door/user', 1);
INSERT INTO permissions (name, path, method) VALUES ('门禁-新增用户', '/api/v1/door/user', 2);
INSERT INTO permissions (name, path, method) VALUES ('门禁-获取卡号', '/api/v1/door/card', 1);
INSERT INTO permissions (name, path, method) VALUES ('门禁-添加卡号', '/api/v1/door/card', 2);
INSERT INTO permissions (name, path, method) VALUES ('门禁-获取指纹', '/api/v1/door/fingerprint', 1);
INSERT INTO permissions (name, path, method) VALUES ('门禁-添加指纹', '/api/v1/door/fingerprint', 2);
INSERT INTO permissions (name, path, method) VALUES ('门禁-指纹添加进度', '/api/v1/door/fingerprint/addprocess', 1);
INSERT INTO permissions (name, path, method) VALUES ('门禁-指纹删除进度', '/api/v1/door/fingerprint/deleteprocess', 1);
INSERT INTO permissions (name, path, method) VALUES ('门禁-获取人脸', '/api/v1/door/face', 1);
INSERT INTO permissions (name, path, method) VALUES ('门禁-上传人脸', '/api/v1/door/face', 2);
INSERT INTO permissions (name, path, method) VALUES ('门禁-历史记录', '/api/v1/door/record', 1);
INSERT INTO permissions (name, path, method) VALUES ('门禁-开始同步信息', '/api/v1/door/syncinfo', 2);
INSERT INTO permissions (name, path, method) VALUES ('门禁-获取同步进度', '/api/v1/door/syncinfo', 1);

-- 门禁控制器相关权限
INSERT INTO permissions (name, path, method) VALUES ('门禁控制器-状态', '/api/v1/door_controller/status', 1);
INSERT INTO permissions (name, path, method) VALUES ('门禁控制器-操作', '/api/v1/door_controller/operation', 2);
INSERT INTO permissions (name, path, method) VALUES ('门禁控制器-卡片列表', '/api/v1/door_controller/card', 1);
INSERT INTO permissions (name, path, method) VALUES ('门禁控制器-新增卡片', '/api/v1/door_controller/card', 2);
INSERT INTO permissions (name, path, method) VALUES ('门禁控制器-历史记录', '/api/v1/door_controller/record', 1);
INSERT INTO permissions (name, path, method) VALUES ('门禁控制器-同步信息', '/api/v1/door_controller/syncinfo', 2);
INSERT INTO permissions (name, path, method) VALUES ('门禁控制器-同步进度', '/api/v1/door_controller/syncinfo', 1);

-- 大华门禁控制器相关权限
INSERT INTO permissions (name, path, method) VALUES ('大华门禁-状态', '/api/v1/door_controller_dahua/status', 1);
INSERT INTO permissions (name, path, method) VALUES ('大华门禁-操作', '/api/v1/door_controller_dahua/operation', 2);
INSERT INTO permissions (name, path, method) VALUES ('大华门禁-用户列表', '/api/v1/door_controller_dahua/user', 1);
INSERT INTO permissions (name, path, method) VALUES ('大华门禁-添加用户', '/api/v1/door_controller_dahua/user', 2);
INSERT INTO permissions (name, path, method) VALUES ('大华门禁-删除用户', '/api/v1/door_controller_dahua/remove_user', 2);
INSERT INTO permissions (name, path, method) VALUES ('大华门禁-添加卡片', '/api/v1/door_controller_dahua/card', 2);
INSERT INTO permissions (name, path, method) VALUES ('大华门禁-获取卡片', '/api/v1/door_controller_dahua/card', 1);
INSERT INTO permissions (name, path, method) VALUES ('大华门禁-删除卡片', '/api/v1/door_controller_dahua/remove_card', 2);
INSERT INTO permissions (name, path, method) VALUES ('大华门禁-开门记录', '/api/v1/door_controller_dahua/record', 1);
