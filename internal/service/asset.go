package service

import (
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
)

type AssetService struct {
	assetRepo *repository.AssetRepo
}

func NewAssetService(assetRepo *repository.AssetRepo) *AssetService {
	return &AssetService{
		assetRepo: assetRepo,
	}
}

func (a *AssetService) CreateCabinet(param request.CreateCabinet) error {
	var cabinet = model.Cabinet{
		CustomID:       param.CustomID,
		Name:           param.Name,
		AreaID:         param.AreaID,
		BusinessTypeID: param.BusinessTypeID,
	}
	return a.assetRepo.CreateCabinet(cabinet)
}

func (a *AssetService) UpdateCabinet(param request.UpdateCabinet) error {
	var cabinet = model.Cabinet{
		CustomID: param.CustomID,
	}
	if param.Name != nil {
		cabinet.Name = *param.Name
	}
	if param.UMax != nil {
		cabinet.UMax = param.UMax
	}
	if param.UStart != nil {
		cabinet.UStart = param.UStart
	}
	if param.UType != nil {
		cabinet.UType = param.UType
	}
	if param.Bearing != nil {
		cabinet.Bearing = param.Bearing
	}
	if param.Power != nil {
		cabinet.Power = param.Power
	}
	if param.ManuFacturer != nil {
		cabinet.ManuFacturer = param.ManuFacturer
	}
	if param.Brand != nil {
		cabinet.Brand = param.Brand
	}
	if param.MaintainPhone != nil {
		cabinet.MaintainPhone = param.MaintainPhone
	}
	if param.MaintainDuration != nil {
		cabinet.MaintainDuration = param.MaintainDuration
	}
	if param.MaintainUser != nil {
		cabinet.MaintainUser = param.MaintainUser
	}
	if param.FacturedTime != nil {
		cabinet.FacturedTime = param.FacturedTime
	}
	if param.BoughtTime != nil {
		cabinet.BoughtTime = param.BoughtTime
	}
	if param.Model != nil {
		cabinet.Model = param.Model
	}
	if param.Field != nil {
		cabinet.Field = param.Field
	}
	if param.Usage != nil {
		cabinet.Usage = param.Usage
	}
	if param.Comment != nil {
		cabinet.Comment = param.Comment
	}
	if param.AreaID != nil {
		cabinet.AreaID = *param.AreaID
	}
	if param.BusinessTypeID != nil {
		cabinet.BusinessTypeID = *param.BusinessTypeID
	}

	return a.assetRepo.UpdateCabinet(cabinet)
}

func (a *AssetService) CabinetList(param request.CabinetList) ([]model.Cabinet, error) {
	return a.assetRepo.CabinetList(param.AreaID, param.BusinessTypeID)
}

func (a *AssetService) DeleteCabinet(param request.DeleteCabinet) error {
	return a.assetRepo.DeleteCabinet(param.IDs)
}

func (a *AssetService) CreateAsset(param request.CreateAsset) error {
	var asset = model.Asset{
		CabinetID:        param.CabinetID,
		AreaID:           param.AreaID,
		BusinessTypeID:   param.BusinessTypeID,
		Name:             param.Name,
		IP:               param.IP,
		Brand:            param.Brand,
		Type:             param.Type,
		UStart:           param.UStart,
		UEnd:             param.UEnd,
		Weight:           param.Weight,
		Power:            param.Power,
		CascadeDevice:    param.CascadeDevice,
		ManuFacturer:     param.ManuFacturer,
		Number:           param.Number,
		Comment:          param.Comment,
		MaintainUser:     param.MaintainUser,
		MaintainPhone:    param.MaintainPhone,
		MaintainDuration: param.MaintainDuration,
		FacturedTime:     param.FacturedTime,
		BoughtTime:       param.BoughtTime,
		Model:            param.Model,
		Field:            param.Field,
		Usage:            param.Usage,
		ZCID:             param.ZCID,
	}
	return a.assetRepo.CreateAsset(asset)
}

func (a *AssetService) UpdateAsset(param request.UpdateAsset) error {
	var asset = model.Asset{
		ID: param.ID,
	}
	if param.CabinetID != nil {
		asset.CabinetID = *param.CabinetID
	}
	if param.IP != nil {
		asset.IP = param.IP
	}
	if param.Name != "" {
		asset.Name = param.Name
	}
	if param.Brand != nil {
		asset.Brand = param.Brand
	}
	if param.Type != nil {
		asset.Type = param.Type
	}
	if param.UStart != nil {
		asset.UStart = param.UStart
	}
	if param.UEnd != nil {
		asset.UEnd = param.UEnd
	}
	if param.Weight != nil {
		asset.Weight = param.Weight
	}
	if param.Power != nil {
		asset.Power = param.Power
	}
	if param.CascadeDevice != nil {
		asset.CascadeDevice = param.CascadeDevice
	}
	if param.ManuFacturer != nil {
		asset.ManuFacturer = param.ManuFacturer
	}
	if param.Number != nil {
		asset.Number = param.Number
	}
	if param.Comment != nil {
		asset.Comment = param.Comment
	}
	if param.MaintainUser != nil {
		asset.MaintainUser = param.MaintainUser
	}
	if param.MaintainPhone != nil {
		asset.MaintainPhone = param.MaintainPhone
	}
	if param.MaintainDuration != nil {
		asset.MaintainDuration = param.MaintainDuration
	}
	if param.FacturedTime != nil {
		asset.FacturedTime = param.FacturedTime
	}

	if param.BoughtTime != nil {
		asset.BoughtTime = param.BoughtTime
	}
	if param.Model != nil {
		asset.Model = param.Model
	}
	if param.Field != nil {
		asset.Field = param.Field
	}
	if param.Usage != nil {
		asset.Usage = param.Usage
	}
	if param.ZCID != nil {
		asset.ZCID = param.ZCID
	}
	return a.assetRepo.UpdateAsset(asset)
}

func (a *AssetService) AssetList(param request.AssetList) ([]response.AssetList, error) {
	return a.assetRepo.List(model.Asset{
		AreaID:         param.AreaID,
		BusinessTypeID: param.BusinessTypeID,
	})
}

func (a *AssetService) DeleteAsset(param request.DeleteAsset) error {
	return a.assetRepo.Delete(param.IDs)
}
