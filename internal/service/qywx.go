package service

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/system/config"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

const (
	url = "https://qyapi.weixin.qq.com"
)

type tokenType uint8

const (
	tokenTypeContact tokenType = iota + 1
	tokenTypeApp
)

type assessToken struct {
	token     string
	expire    int64
	appToken  string
	appExpire int64
}

type QywxService struct {
	userRepo         *repository.UserRepo
	roleRepo         *repository.RoleRepo
	config           *config.Config
	client           *http.Client
	token            *assessToken
	commonUserRoleID int
	l                *zap.Logger
}

func NewQywxService(
	userRepo *repository.UserRepo,
	roleRepo *repository.RoleRepo,
	config *config.Config,
	l *zap.Logger,
) *QywxService {
	client := &http.Client{
		Timeout: 5 * time.Second,
	}
	return &QywxService{
		userRepo:         userRepo,
		roleRepo:         roleRepo,
		config:           config,
		client:           client,
		l:                l.Named("qywx_service:"),
		commonUserRoleID: 20,
	}
}

func (q *QywxService) getToken(tt tokenType) (string, error) {
	var s string
	switch {
	case tt == tokenTypeContact:
		if q.token != nil && q.token.expire > time.Now().Unix() {
			return q.token.token, nil
		}
		s = q.config.Qywx.Secret
	case tt == tokenTypeApp:
		if q.token != nil && q.token.appExpire > time.Now().Unix() {
			return q.token.appToken, nil
		}
		s = q.config.Qywx.AppSecret
	}
	var respT struct {
		ErrCode int    `json:"errcode"`
		ErrMsg  string `json:"errmsg"`
		Token   string `json:"access_token"`
		Expire  int64  `json:"expires_in"`
	}
	resp, err := q.client.Get(fmt.Sprintf(
		"%s/cgi-bin/gettoken?corpid=%s&corpsecret=%s",
		url,
		q.config.Qywx.CorpID,
		s,
	))
	if err != nil {
		return "", err
	}

	defer resp.Body.Close()

	err = json.NewDecoder(resp.Body).Decode(&respT)
	if err != nil {
		return "", err
	}
	if respT.ErrCode != 0 {
		return "", fmt.Errorf("get token error, code: %d, msg: %s", respT.ErrCode, respT.ErrMsg)
	}
	q.token = &assessToken{
		token:  respT.Token,
		expire: time.Now().Unix() + respT.Expire,
	}
	return respT.Token, nil
}

func (q *QywxService) memberList() (map[string]struct{}, error) {
	token, err := q.getToken(tokenTypeContact)
	if err != nil {
		return nil, err
	}
	type reqT struct {
		Cursor string `json:"cursor"`
		Limit  int    `json:"limit"`
	}
	type respT struct {
		ErrCode    int    `json:"errcode"`
		ErrMsg     string `json:"errmsg"`
		NextCursor string `json:"next_cursor"`
		DeptUser   []struct {
			UserID     string `json:"userid"`
			Department int    `json:"department"`
		} `json:"dept_user"`
	}

	var (
		payload []byte
		result  = make(map[string]struct{})
		cursor  string
	)

	for {
		var (
			reqTmp = reqT{
				Cursor: cursor,
				Limit:  10,
			}
			respTmp = respT{}
		)
		payload, err = json.Marshal(reqTmp)
		if err != nil {
			return nil, err
		}

		req, err := http.NewRequest(
			http.MethodGet,
			fmt.Sprintf(
				"%s/cgi-bin/user/list_id?access_token=%s",
				url,
				token,
			),
			bytes.NewBuffer(payload),
		)
		if err != nil {
			return nil, err
		}

		resp, err := q.client.Do(req)
		if err != nil {
			return nil, err
		}

		err = json.NewDecoder(resp.Body).Decode(&respTmp)
		if err != nil {
			return nil, err
		}

		if respTmp.ErrCode != 0 {
			return nil, fmt.Errorf("get member id list error, code: %d, msg: %s", respTmp.ErrCode, respTmp.ErrMsg)
		}

		for i := 0; i < len(respTmp.DeptUser); i++ {
			result[respTmp.DeptUser[i].UserID] = struct{}{}
		}
		if respTmp.NextCursor == "" {
			break
		}
		cursor = respTmp.NextCursor
	}

	return result, nil
}

func (q *QywxService) QyFetch() error {
	newMap, err := q.memberList()
	if err != nil {
		return err
	}
	var (
		result = make([]string, 0, len(newMap))
		users  = make([]model.User, 0, len(newMap))
		oldMap = make(map[string]int, 0)
		ok     bool
	)

	old, err := q.userRepo.WechatList()
	if err != nil {
		return err
	}

	for _, v := range old {
		oldMap[v.WechatID] = v.ID
	}

	for k := range newMap {
		result = append(result, k)
		_, ok = oldMap[k]
		if ok {
			continue
		}
		users = append(
			users,
			model.User{
				WechatID: k,
				RoleID:   q.commonUserRoleID,
				Status:   model.QywxInit,
				Nickname: k,
				Name:     k,
			},
		)
	}

	if len(users) == 0 {
		return nil
	}

	return q.userRepo.BatchCreate(users)
}

func (q *QywxService) QyLogin(code string) (response.QyLogin, error) {
	var (
		respT struct {
			ErrCode        int    `json:"errcode"`
			ErrMsg         string `json:"errmsg"`
			UserID         string `json:"userid"`
			UserTicket     string `json:"user_ticket"`
			OpenID         string `json:"openid"`
			ExternalUserID string `json:"external_userid"`
		}
		login response.QyLogin
	)
	token, err := q.getToken(tokenTypeApp)
	if err != nil {
		return login, err
	}
	resp, err := q.client.Get(fmt.Sprintf(
		"%s/cgi-bin/auth/getuserinfo?access_token=%s&code=%s",
		url,
		token,
		code,
	))

	if err != nil {
		return login, err
	}
	defer resp.Body.Close()

	err = json.NewDecoder(resp.Body).Decode(&respT)
	if err != nil {
		return login, err
	}

	if respT.ErrCode != 0 {
		return login, fmt.Errorf("get user info error, code: %d, msg: %s", respT.ErrCode, respT.ErrMsg)
	}

	if respT.UserID == "" && (respT.OpenID == "" || respT.ExternalUserID == "") {
		q.l.Warn(
			"非企业微信用户,登录失败!",
			zap.String("code", code),
			zap.String("msg", respT.ErrMsg),
		)
		return login, fmt.Errorf("非企业微信用户,登录失败!")
	}

	user, err := q.privateInfo(respT.UserTicket)
	if err != nil {
		q.l.Warn(
			"获取用户详情失败,登录失败!",
			zap.String("code", code),
			zap.String("msg", err.Error()),
		)
		return login, errors.New("获取用户详情失败,登录失败!")
	}
	u, err := q.userRepo.FirstWithRole(model.User{WechatID: respT.UserID})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			user.WechatID = respT.UserID
			user.Status = model.QywxInit
			user.RoleID = q.commonUserRoleID
			user.Nickname = respT.UserID
			user.Name = respT.UserID
			err = q.userRepo.Create(&user)
			if err != nil {
				return login, err
			}
			role, err := q.roleRepo.First(model.Role{ID: q.commonUserRoleID})
			if err != nil {
				return login, err
			}
			u.ID = user.ID
			u.RoleName = role.Name
			u.RoleID = role.ID
			login.Status = u.Status
		} else {
			return login, err
		}
	} else {
		err = q.userRepo.Update(model.User{
			ID:     u.ID,
			Phone:  user.Phone,
			Email:  user.Email,
			Status: model.QywxCompleted,
		})
		if err != nil {
			return login, err
		}
		user.Nickname = u.Nickname
		login.Status = u.Status
		login.Email = u.Email
		login.Phone = u.Phone
	}
	token, err = NewToken(u.ID, u.RoleID, q.config.JWT)
	login.Token = token
	login.Nickname = user.Nickname
	login.RoleName = u.RoleName
	login.UserID = u.ID
	return login, err
}

func (q *QywxService) privateInfo(ticket string) (model.User, error) {
	var (
		respT struct {
			ErrCode int    `json:"errcode"`
			ErrMsg  string `json:"errmsg"`
			UserID  string `json:"userid"`
			Mobile  string `json:"mobile"`
			Email   string `json:"biz_mail"`
		}
		reqT = struct {
			UserTicket string `json:"user_ticket"`
		}{
			UserTicket: ticket,
		}
		user model.User
	)

	token, err := q.getToken(tokenTypeApp)
	if err != nil {
		return user, err
	}

	payload, err := json.Marshal(reqT)
	if err != nil {
		return user, err
	}

	req, err := http.NewRequest(
		http.MethodPost,
		fmt.Sprintf(
			"%s/cgi-bin/user/getuserdetail?access_token=%s",
			url,
			token,
		),
		bytes.NewBuffer(payload),
	)
	if err != nil {
		return user, err
	}

	resp, err := q.client.Do(req)
	if err != nil {
		return user, err
	}

	defer resp.Body.Close()

	err = json.NewDecoder(resp.Body).Decode(&respT)
	if err != nil {
		return user, err
	}

	if respT.ErrCode != 0 {
		return user, fmt.Errorf("get user detail error, code: %d, msg: %s", respT.ErrCode, respT.ErrMsg)
	}

	return model.User{
		Phone: respT.Mobile,
		Email: respT.Email,
	}, nil
}
