package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/system/config"
	"tw_platform/pkg/system/mqtt"

	jsoniter "github.com/json-iterator/go"
	"go.uber.org/zap"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type DeviceService struct {
	deviceRepo   *repository.DeviceRepo
	driverRepo   *repository.DriverRepo
	gatewayRepo  *repository.GatewayRepo
	alarmService *AlarmService
	alarmRepo    *repository.AlarmRepo
	alarmGroup   *AlarmGroupService
	areaRepo     *repository.AreaRepo
	logRepo      *repository.LogRepo
	registerRepo *repository.RegisterRepo
	btRepo       *repository.BusinessTypeRepo

	mqttModule *mqtt.MqttModule
	config     *config.Config
	l          *zap.Logger

	diTypeStr string
	diLowIdx  int8
	diHighIdx int8
	doOn      string
	doOff     string
	doType    string

	soundLightTypeID int
}

func NewDeviceService(
	deviceRepo *repository.DeviceRepo,
	driverRepo *repository.DriverRepo,
	gatewayRepo *repository.GatewayRepo,
	alarmService *AlarmService,
	alarmRepo *repository.AlarmRepo,
	alarmGroup *AlarmGroupService,
	areaRepo *repository.AreaRepo,
	logRepo *repository.LogRepo,
	registerRepo *repository.RegisterRepo,
	btRepo *repository.BusinessTypeRepo,
	mm *mqtt.MqttModule,
	config *config.Config,
	l *zap.Logger,
) *DeviceService {
	return &DeviceService{
		deviceRepo:   deviceRepo,
		driverRepo:   driverRepo,
		gatewayRepo:  gatewayRepo,
		alarmService: alarmService,
		alarmRepo:    alarmRepo,
		alarmGroup:   alarmGroup,
		areaRepo:     areaRepo,
		logRepo:      logRepo,
		registerRepo: registerRepo,
		btRepo:       btRepo,
		mqttModule:   mm,
		config:       config,
		l:            l.Named("device_service"),

		diTypeStr:        "di",
		diLowIdx:         0,
		diHighIdx:        1,
		doOn:             "1",
		doOff:            "0",
		soundLightTypeID: 16,
		doType:           "do",
	}
}

func (d *DeviceService) CreatePceMDevice(param request.CreatePceMDevice) error {
	var (
		driver  model.Driver
		conf    model.CommonDevice
		devices = make([]model.Device, 0, len(param.Devices))
		confs   = make([]model.Config, 0, len(param.Devices))
		confStr = make([]string, 0, len(param.Devices))
		err     error
	)

	err = d.createLimit(param.BusinessTypeID, len(param.Devices))
	if err != nil {
		return err
	}

	port, err := d.gatewayRepo.PortWithGateway(model.GatewayPort{
		ID: param.PortID,
	})
	if err != nil {
		return err
	}
	switch port.Type {
	case model.PortTypePceMBoard:
		conf = d.boardConfig(param.Type, port.Name)
		conf.Devtype = "DIDO设备"
	default:
		driver, err = d.driverRepo.GetByID(param.DriverID)
		if err != nil {
			return err
		}

		err = jsoniter.UnmarshalFromString(driver.Content.Config, &conf)
		if err != nil {
			return err
		}
	}

	if param.BaudRate != 0 {
		conf.Com.BaudRate = param.BaudRate
	}

	if param.TimeOut != 0 {
		conf.TimeOut = param.TimeOut
	}

	if param.Timeinterval != 0 {
		conf.TimeInterval = param.Timeinterval
	}

	for _, v := range param.Devices {
		config := conf
		config.Name = v.Name
		config.Addr = v.Addr

		devices = append(devices, model.Device{
			Name:           v.Name,
			Type:           conf.Devtype,
			Activity:       model.DeviceActivityEnable,
			PortID:         &param.PortID,
			Status:         model.DeviceStatusNormal,
			AreaID:         param.AreaID,
			BusinessTypeID: param.BusinessTypeID,
		})

		confs = append(confs, model.Config{
			Type: model.DeviceTypePceM,
			Config: model.DeviceConfig{
				CommonDevice: &config,
			},
			DriverID: &param.DriverID,
		})

		str, err := jsoniter.MarshalToString(config)
		if err != nil {
			return err
		}
		confStr = append(confStr, str)

	}
	ids, err := d.deviceRepo.Create(devices, confs, param.CreateDeviceBasic)
	if err != nil {
		return err
	}
	payload := request.MqttCreatePceDevice{
		Port:   port.Port,
		Config: confStr,
		IDs:    ids,
	}
	if param.DriverID != 0 {
		payload.Driver = driver.Content.DriverInfo.Driver
	}
	return d.mqttModule.Publish(
		fmt.Sprintf(model.TopicCreateDevice, *port.GatewayInfo.FlashID),
		payload,
	)
}

func (d *DeviceService) CreateDevice(param request.CreateDevice) error {
	var (
		devices = make([]model.Device, 0, len(param.Devices))
		conf    model.CommonDevice
		confs   = make([]model.Config, 0, len(param.Devices))
	)
	driver, err := d.driverRepo.GetByID(param.DriverID)
	if err != nil {
		return err
	}

	err = jsoniter.UnmarshalFromString(driver.Content.Config, &conf)
	if err != nil {
		return err
	}

	err = d.createLimit(param.BusinessTypeID, len(param.Devices))
	if err != nil {
		return err
	}

	port, err := d.gatewayRepo.PortWithGateway(model.GatewayPort{
		ID: param.PortID,
	})
	if err != nil {
		return err
	}

	//网络设备port为ip:port形式，无法转为int
	//也无需更新此项配置，可以忽略掉
	p, err := strconv.Atoi(port.Port)
	if err == nil {
		conf.Net.IP = *port.GatewayInfo.IP
		conf.Net.Port = p
	}

	if param.BaudRate != 0 {
		conf.Com.BaudRate = param.BaudRate
	}
	if param.Timeinterval != 0 {
		conf.TimeInterval = param.Timeinterval
	}
	if param.TimeOut != 0 {
		conf.TimeOut = param.TimeOut
	}

	for _, v := range param.Devices {
		config := conf
		config.Name = v.Name
		config.Addr = v.Addr
		devices = append(devices, model.Device{
			Name:           v.Name,
			Type:           config.Devtype,
			Activity:       model.DeviceActivityEnable,
			Status:         model.DeviceStatusNormal,
			PortID:         &param.PortID,
			AreaID:         param.AreaID,
			BusinessTypeID: param.BusinessTypeID,
		})

		confs = append(confs, model.Config{
			DriverID: &param.DriverID,
			Type:     model.DeviceTypeNetwork,
			Config: model.DeviceConfig{
				CommonDevice: &config,
			},
		})
	}

	_, err = d.deviceRepo.Create(devices, confs, param.CreateDeviceBasic)
	return err
}
func (d *DeviceService) CreateCustomDevice(param request.CreateCustomDevice) error {
	var (
		devices = make([]model.Device, 0, len(param.Devices))
		confs   = make([]model.Config, 0, len(param.Devices))
	)

	for _, v := range param.Devices {
		config := v.Config
		for index := range config.Unit {
			config.Unit[index].Activity = true
			config.Unit[index].AlarmLevel = "一般"
			config.Unit[index].FilterCnt = 10
			config.Unit[index].High = 1000
			config.Unit[index].Saveenable = true
			config.Unit[index].Savetime = 7200
		}
		devices = append(devices, model.Device{
			Name:           v.Name,
			Type:           v.Config.Devtype,
			Activity:       model.DeviceActivityEnable,
			Status:         model.DeviceStatusNormal,
			AreaID:         param.AreaID,
			BusinessTypeID: param.BusinessTypeID,
		})

		confs = append(confs, model.Config{
			Type: model.DeviceTypeCustom,
			Config: model.DeviceConfig{
				CustomDevice: config,
			},
		})
	}

	_, err := d.deviceRepo.Create(devices, confs, param.CreateDeviceBasic)
	return err
}
func (d *DeviceService) CreateOpcDevice(param request.CreateOpcDevice) error {
	var (
		devices = make([]model.Device, 0, len(param.Devices))
		confs   = make([]model.Config, 0, len(param.Devices))
	)
	for _, v := range param.Devices {
		config := v.Config
		for index := range config.Unit {
			config.Unit[index].Activity = true
			config.Unit[index].AlarmLevel = "一般"
			config.Unit[index].FilterCnt = 10
			config.Unit[index].High = 1000
			config.Unit[index].Saveenable = true
			config.Unit[index].Savetime = 7200
		}
		devices = append(devices, model.Device{
			Name:           v.Name,
			Type:           v.Config.Devtype,
			Activity:       model.DeviceActivityEnable,
			Status:         model.DeviceStatusNormal,
			AreaID:         param.AreaID,
			BusinessTypeID: param.BusinessTypeID,
			PortID:         &param.PortID,
		})

		confs = append(confs, model.Config{
			Type: model.DeviceTypeOpc,
			Config: model.DeviceConfig{
				OpcDevice: config,
			},
		})
	}

	_, err := d.deviceRepo.Create(devices, confs, param.CreateDeviceBasic)
	return err
}
func (d *DeviceService) BatchCreateCustomDevice(param request.CreateCustomDevice) ([]int, error) {
	var (
		devices = make([]model.Device, 0, len(param.Devices))
		confs   = make([]model.Config, 0, len(param.Devices))
	)

	for _, v := range param.Devices {
		config := v.Config
		for index := range config.Unit {
			config.Unit[index].Activity = true
			config.Unit[index].AlarmLevel = "一般"
			config.Unit[index].FilterCnt = 10
			config.Unit[index].High = 1000
			config.Unit[index].Saveenable = true
			config.Unit[index].Savetime = 7200
		}
		devices = append(devices, model.Device{
			Name:           v.Name,
			Type:           v.Config.Devtype,
			Activity:       model.DeviceActivityEnable,
			Status:         model.DeviceStatusNormal,
			AreaID:         param.AreaID,
			BusinessTypeID: param.BusinessTypeID,
		})

		confs = append(confs, model.Config{
			Type: model.DeviceTypeCustom,
			Config: model.DeviceConfig{
				CustomDevice: config,
			},
		})
	}

	return d.deviceRepo.Create(devices, confs, param.CreateDeviceBasic)
}
func (d *DeviceService) Delete(param request.DeleteDevice) error {
	device, err := d.deviceRepo.First(model.Device{ID: param.ID})
	if err != nil {
		return err
	}
	err = d.deviceRepo.Delete(device)
	if err != nil {
		d.l.Error("delete device failure", zap.Error(err))
		return errors.New("删除设备失败")
	}

	data, err := json.Marshal(model.DeviceDiff{
		Event:     model.DeviceDiffDel,
		DeviceIDs: []int{param.ID},
	})
	if err != nil {
		return err
	}

	err = d.deviceRepo.DeleteCache(param.ID)
	if err != nil {
		return err
	}
	err = d.deviceRepo.RedisNotify(d.deviceRepo.DeviceDiffKey, data)
	if err != nil {
		return err
	}
	if device.PortID == nil {
		return nil
	}
	gateway, err := d.gatewayRepo.FirstByPortID(*device.PortID)
	if err != nil {
		return err
	}
	if gateway.Type != model.GatewayTypePceM {
		return nil
	}
	return d.mqttModule.Publish(
		fmt.Sprintf(model.TopicDeleteDevice, *gateway.FlashID),
		request.MqttDeleteDevice{
			DeviceID: param.ID,
		},
	)
}

func (d *DeviceService) ConfigWithName(param request.DeviceDetail) (response.DeviceConfigWithName, error) {
	config, err := d.deviceRepo.ConfigWithName(model.Config{
		ID: param.ID,
	})
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return config, nil
	}
	return config, err
}

func (d *DeviceService) Config(param request.DeviceDetail) (model.Config, error) {
	config, err := d.deviceRepo.Config(model.Config{
		ID: param.ID,
	})
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return config, nil
	}
	return config, err
}

func (d *DeviceService) Update(param request.UpdateDevice) error {
	_, err := d.deviceRepo.UpdateDetail(param)
	return err
}

func (d *DeviceService) UpdateUnit(param request.UpdateDeviceUnit) error {
	var (
		updater = datatypes.JSONSet("config")
		path    string
	)
	device, err := d.deviceRepo.FirstWithConfig(model.Device{ID: param.DeviceID})
	if err != nil {
		return err
	}
	switch device.Config.Type {
	case model.DeviceTypeNetwork:
		path = "common_device"
	case model.DeviceTypeCustom:
		path = "custom_device"
	default:
		return errors.New("设备类型错误")
	}
	for key, value := range param.Fields {
		updater = updater.Set(
			fmt.Sprintf("{%s,unit, %d, %s}", path, param.UnitID, key),
			value,
		)
	}
	_, err = d.deviceRepo.UpdateConfig(param.DeviceID, updater)
	if err != nil {
		return err
	}
	// device, err := d.gatewayRepo.FirstByDevice(param.DeviceID)
	// if err != nil {
	// 	return nil
	// }

	err = d.deviceRepo.RemoveDeviceUnits(param.DeviceID)
	if err != nil {
		return err
	}
	if device.BusinessTypeID == d.soundLightTypeID {
		d.deviceRepo.DeleteDOUnitCache(param.DeviceID)
	}

	// return d.mqttModule.Publish(fmt.Sprintf(request.UpdateUnit, *device.FlashID), device)
	return nil
}

func (d *DeviceService) UpdateConfig(param request.UpdateConfig) error {
	path, err := model.GetConfigPath(param.Type)
	if err != nil {
		return err
	}

	updater := datatypes.JSONSet("config")
	//若fields下字段为json对象，需要包含该字段下未更新字段，否则会丢失未更新字段
	//或者改为使用反射，存在递归问题
	for key, value := range param.Fields {
		updater = updater.Set(
			fmt.Sprintf("{%s,  %s}", path, key),
			value,
		)
	}
	_, err = d.deviceRepo.UpdateConfig(param.ID, updater)
	return err
}

func (d *DeviceService) ReportData(param request.DeviceData) error {
	var (
		err error
	)
	status := d.gatewayRepo.Cache(param.GatewayID)
	//设备采集程序标记网关离线，则不进行报警检测,只将网关离线状态写入缓存
	if param.Status == model.DeviceStatusGatewayOffline {
		if status == 0 {
			d.gatewayRepo.SetCache(param.GatewayID, model.GatewayStatusOffline)
		}
		return d.deviceRepo.SetDataCache(param)
	}

	//针对网关缺少离线恢复问题
	if status == model.GatewayStatusOffline {
		if d.gatewayRepo.GetCacheLock(param.GatewayID) {
			d.gatewayRepo.SetCache(param.GatewayID, model.GatewayStatusOnline)
			d.alarmService.Recovery1(model.Alarm{
				GatewayID: &param.GatewayID,
			}, "")
		}
	} else if status == 0 {
		d.gatewayRepo.SetCache(param.GatewayID, model.GatewayStatusOnline)
	}

	//报警检测只针对存在报警的设备
	//其他设备只写入到redis
	if param.HasAlarmed {
		err = d.CheckData(&param)
		if err != nil {
			d.l.Error("check data failure", zap.Error(err))
			return err
		}
	}

	err = d.deviceRepo.SetDataCache(param)
	if err != nil {
		return err
	}

	return d.deviceRepo.StoreData(param)
}

func (d *DeviceService) List(param request.GetDeviceList) ([]response.DeviceList, error) {
	return d.deviceRepo.
		List(
			param.BusinessTypeID,
			param.AreaID,
			param.DepartmentID,
		)
}
func (d *DeviceService) CustomList() ([]response.DeviceList, error) {
	return d.deviceRepo.
		CustomList()
}
func (d *DeviceService) DoorList(param request.GetDeviceList) ([]response.DoorDeviceList, error) {
	if param.AreaID == 0 && param.DepartmentID == 0 {
		return nil, errors.New("请选择区域或部门")
	}
	return d.deviceRepo.
		DoorList(
			param.BusinessTypeID,
			param.AreaID,
			param.DepartmentID,
		)
}

func (d *DeviceService) ListByFirstType(param request.GetDeviceListByFirstType) ([]model.Device, error) {
	return d.deviceRepo.
		ListByFirstType(
			param.BusinessTypeID,
		)
}

func (d *DeviceService) HistoryData(param request.DeviceHistoryData) ([]response.DeviceHistoryData, error) {
	list := make([]response.DeviceHistoryData, 0)
	result, err := d.deviceRepo.HistoryData(param)
	if err != nil {
		return list, err
	}

	names, err := d.deviceRepo.UnitName([]int{param.DeviceID})
	if err != nil {
		return list, err
	}
	namesMap := make(map[string]string, len(names))

	for _, name := range names {
		namesMap[strconv.Itoa(name.UnitID)] = name.Name
	}

	for _, data := range result {
		name, ok := namesMap[data.UnitID]
		if !ok {
			name = "unknown"
		}
		data.Name = name
		list = append(list, data)
	}

	return list, nil
}

func (d *DeviceService) Control(param request.DeviceControl) error {
	device, err := d.deviceRepo.FirstWithConfig(model.Device{
		ID: param.DeviceID,
	})
	if err != nil {
		return err
	}

	if device.BusinessTypeID == d.soundLightTypeID {
		_ = d.deviceRepo.DeleteDOUnitCache(param.DeviceID)
	}
	return d.doControl(device.Config.Type, param)
}

func (d *DeviceService) doControl(t model.DeviceType, param request.DeviceControl) error {
	if t != model.DeviceTypePceM {
		payload, err := json.Marshal(param)
		if err != nil {
			return err
		}
		return d.deviceRepo.Control(payload)
	}

	gateway, err := d.gatewayRepo.FirstByDevice(param.DeviceID)
	if err != nil {
		d.l.Error("get gateway failure", zap.Error(err))
		return err
	}

	return d.mqttModule.Publish(
		fmt.Sprintf(model.TopicControlDevice, *gateway.FlashID),
		request.MqttControlDevice{
			DeviceID: param.DeviceID,
			UnitID:   param.UnitID,
			Value:    param.Value,
		},
	)
}

func (d *DeviceService) CheckData(param *request.DeviceData) error {
	var (
		device model.DeviceWithArea
		// flag           bool
		deviceInfoFunc = func(id int) (model.DeviceWithArea, error) {
			var device model.DeviceWithArea
			device, err := d.deviceRepo.DetailWithArea(id)
			return device, err
		}
		deviceStatus = model.DeviceStatusAlarmed
		binding      model.DeviceAlarmBinding
		bindingCache model.DeviceConfigUnit

		logs = make([]model.AlarmLog, 0)
	)

	defer func() {
		if len(logs) == 0 {
			return
		}
		err := d.logRepo.CreateAlarmLog(logs)
		if err != nil {
			d.l.Error("create alarm log failure", zap.Error(err))
		}
	}()

	limits, err := d.deviceRepo.AlarmLimit(param.ID)
	if err != nil {
		d.l.Warn("get alarm limit failure", zap.Error(err))
		return err
	}

	for i, unit := range param.Units {
		var (
			limitDesc  string
			limitValue int
			isSecond   bool
			isAlarmed  bool
		)
		limit, ok := limits.Units[unit.Point]
		if !ok {
			d.l.Debug("limit not found", zap.Int("device", param.ID), zap.Int("point", unit.Point))
			continue
		}
		if !limit.Activity || !limit.AlarmEnable {
			d.l.Debug("alarm not enable", zap.Int("device", param.ID), zap.Int("point", unit.Point))
			continue
		}

		value, err := strconv.Atoi(strings.Split(unit.Value, ".")[0])
		if err != nil {
			d.l.Warn("convert value failure", zap.Error(err))
			continue
		}

		isBlocked := d.deviceRepo.IsAlarmBlocked(param.ID, unit.Point)
		switch {
		//DI 单独处理
		// case limit.Type == d.diTypeStr:
		// 	if len(limit.EffectTime) != 2 {
		// 		continue
		// 	}
		// 	begin, err := time.Parse(time.TimeOnly, limit.EffectTime[d.diLowIdx])
		// 	if err != nil {
		// 		d.l.Warn("parse begin time failure", zap.Error(err), zap.Int("device", param.ID), zap.Int("point", unit.Point))
		// 		continue
		// 	}
		// 	end, err := time.Parse(time.TimeOnly, limit.EffectTime[d.diHighIdx])
		// 	if err != nil {
		// 		d.l.Warn("parse end time failure", zap.Error(err), zap.Int("device", param.ID), zap.Int("point", unit.Point))
		// 		continue
		// 	}
		// 	now, _ := time.Parse(time.TimeOnly, time.Now().Format(time.TimeOnly))

		// 	if now.After(begin) && now.Before(end) && value == limit.Action {
		// 		limitDesc = "上限报警"
		// 		limitValue = limit.Action
		// 		if value == 0 {
		// 			limitDesc = "下限报警"
		// 		}
		// 		d.l.Debug(limitDesc, zap.Int("device", param.ID), zap.Int("point", unit.Point), zap.Int("value", value))
		// 	}
		//上限报警
		case value >= limit.High && (limit.HighHigh <= limit.High || value < limit.HighHigh):
			d.l.Debug("上限报警", zap.Int("device", param.ID), zap.Int("point", unit.Point), zap.Int("value", value))
			limitDesc = limit.HighName
			limitValue = limit.High
		//下限报警
		case value <= limit.Low && (limit.LowLow >= limit.Low || value > limit.LowLow):
			d.l.Debug("下限报警", zap.Int("device", param.ID), zap.Int("point", unit.Point), zap.Int("value", value))
			limitDesc = limit.LowName
			limitValue = limit.Low
		//上上限报警
		case value >= limit.HighHigh && limit.HighHigh > limit.High:
			d.l.Debug("上上限报警", zap.Int("device", param.ID), zap.Int("point", unit.Point), zap.Int("value", value))
			limitDesc = limit.HighHighName
			limitValue = limit.HighHigh
			isSecond = true
		//下下限报警
		case value <= limit.LowLow && limit.LowLow < limit.Low:
			d.l.Debug("下下限报警", zap.Int("device", param.ID), zap.Int("point", unit.Point), zap.Int("value", value))
			limitDesc = limit.LowLowName
			limitValue = limit.LowLow
			isSecond = true
		}
		isAlarmed = d.alarmRepo.IsAlarmed(param.ID, unit.Point, isSecond)
		// 正常数值/已屏蔽报警
		if limitDesc == "" || isBlocked {
			//报警回执
			if isAlarmed && !isBlocked && !isSecond && (value >= limit.High-limit.HighHyse ||
				value <= limit.Low+limit.LowHyse) {
				d.l.Debug(
					"报警回执触发，已忽略本次报警！",
					zap.Int("id", param.ID),
					zap.Int("unit_id", unit.Point),
				)
				//保持报警状态
				param.Units[i].Status = model.DeviceStatusAlarmed
				continue
			}

			result := d.alarmRepo.RemoveAlarmed(param.ID, unit.Point)
			if result == 0 && ((limitDesc != "" && isBlocked) ||
				(limitDesc == "" && !isBlocked)) {
				continue
			}

			if limitDesc == "" && isBlocked {
				d.deviceRepo.UnBlockAlarm(param.ID, unit.Point)
				//log
				logs = append(logs, model.AlarmLog{
					DeviceID:  &param.ID,
					UnitID:    &unit.Point,
					Event:     "屏蔽测点报警解除",
					CreatedAt: time.Now(),
				})
			}

			d.alarmRepo.RemoveAlarmCounts(param.ID, unit.Point)

			// 具体信息懒加载
			if device.ID == 0 {
				device, err = deviceInfoFunc(param.ID)
				if err != nil {
					return err
				}
			}
			path, err := device.Config.GetConfigPath()
			if err != nil {
				d.l.Warn(
					"get config path failure",
					zap.Int("device id", param.ID),
					zap.Int("point", unit.Point),
					zap.Error(err),
				)
			}

			filter := model.Alarm{
				DeviceID: &param.ID,
				UnitID:   &unit.Point,
			}
			err = d.alarmService.Recovery1(filter, path)

			if err != nil {
				d.l.Warn("recovery alarm failure", zap.Error(err))
				continue
			}

			err = d.alarmService.Publish(response.AlarmListPayload{
				Type:  model.AlarmStatusRecovered,
				Path:  path,
				Alarm: filter,
			})
			if err != nil {
				d.l.Warn("publish alarm recovery failure", zap.Error(err))
			}

			// _, err = d.deviceRepo.UpdateConfig(
			// 	param.ID,
			// 	datatypes.JSONSet("config").
			// 		Set(
			// 			fmt.Sprintf("{%s, unit, %d, alarmstatus}", path, unit.Point),
			// 			model.DeviceStatusNormal,
			// 		),
			// )
			// if err != nil {
			// 	d.l.Warn("update device unit failure", zap.Error(err))
			// 	continue
			// }
			configUnit, err := device.Config.GetConfigUint(unit.Point)
			if err != nil {
				d.l.Warn(
					"get config uint failure",
					zap.Int("device id", param.ID),
					zap.Int("point", unit.Point),
					zap.Error(err),
				)
				continue
			}
			//设备测点报警恢复通知
			err = d.alarmGroup.SendAlarm(
				param.ID,
				fmt.Sprintf(
					"项目通知，时间:%s,区域为：%s, 设备为：%s, 测点为：%s,当前值为：%s，该测点报警恢复！",
					time.Now().Format("2006-01-02 15:04:05"),
					device.Area.Name,
					device.Name,
					configUnit.Name,
					unit.Value,
				),
				"recovery",
				limit.Priority,
				device.Area.Name+"+"+device.Name,
				configUnit.Name,
				configUnit.AlarmLevel,
				"该测点报警恢复！",
				unit.Value,
			)
			if err != nil {
				d.l.Warn("send alarm recovery failure", zap.Error(err))
			}
			//log
			logs = append(logs, model.AlarmLog{
				DeviceID: &param.ID,
				UnitID:   &unit.Point,
				Event:    "报警恢复",
				Message: fmt.Sprintf(
					"区域：%s, 设备：%s, 测点：%s, 当前值：%s",
					device.Area.Name,
					device.Name,
					configUnit.Name,
					unit.Value,
				),
				CreatedAt: time.Now(),
			})

			count := d.alarmRepo.DeviceAlarmCounts(param.ID)
			if count > 0 {
				continue
			}
			_, err = d.deviceRepo.
				Update(
					param.ID,
					model.Device{Status: model.DeviceStatusNormal},
				)
			if err != nil {
				d.l.Warn("update device status failure", zap.Error(err))
			}

			continue
		}

		if isBlocked {
			zap.L().Debug("alarm blocked", zap.Int("device", param.ID), zap.Int("point", unit.Point))
			continue
		}
		count := d.alarmRepo.AlarmIncrease(param.ID, unit.Point)
		if count < int64(limit.FilterCnt) {
			param.IsForceRecord = true
			d.l.Debug(
				"less than filter count",
				zap.Int("device", param.ID),
				zap.Int("point", unit.Point),
				zap.Int64("count", count),
			)
			//log
			if count == 1 {
				device, err = deviceInfoFunc(param.ID)
				if err != nil {
					d.l.Warn("get device info failure", zap.Error(err))
				}
				logs = append(logs, model.AlarmLog{
					DeviceID: &param.ID,
					UnitID:   &unit.Point,
					Event:    "未达到屏蔽次数",
					Message: fmt.Sprintf(
						"区域：%s, 设备：%s, 测点：%s, 当前值：%s，配置值：%d，屏蔽次数：%d，当前次数：%d",
						device.Area.Name,
						device.Name,
						limit.Name,
						unit.Value,
						limitValue,
						limit.FilterCnt,
						count,
					),
					CreatedAt: time.Now(),
					TX:        unit.TX,
					RX:        unit.RX,
				})
			}
			continue
		}

		param.Units[i].Status = model.DeviceStatusAlarmed
		// flag = true
		param.Status = deviceStatus
		if isAlarmed {
			continue
		}
		//单独处理设备失联报警
		//设备失联时只存在Point=0的测点数据。
		if param.Units[i].Point == 0 {
			deviceStatus = model.DeviceStatusOffline
			// break
		}

		param.IsForceRecord = true

		d.alarmRepo.SetAlarmedHash(param.ID, unit.Point, isSecond)

		d.l.Debug("send a alarm", zap.Int("device", param.ID), zap.Int("point", unit.Point))

		// 具体信息懒加载
		if device.ID == 0 {
			device, err = deviceInfoFunc(param.ID)
			if err != nil {
				return err
			}
		}

		configUnit, err := device.Config.GetConfigUint(unit.Point)
		if err != nil {
			d.l.Warn(
				"get config uint failure",
				zap.Int("device id", param.ID),
				zap.Int("point", unit.Point),
				zap.Error(err),
			)
			continue
		}
		//log
		logs = append(logs, model.AlarmLog{
			DeviceID: &param.ID,
			UnitID:   &unit.Point,
			Event:    "检测到报警",
			Message: fmt.Sprintf(
				"区域：%s, 设备：%s, 测点：%s, 当前值：%s，配置值：%d，类型：%s",
				device.Area.Name,
				device.Name,
				configUnit.Name,
				unit.Value,
				limitValue,
				limitDesc,
			),
			TX:        unit.TX,
			RX:        unit.RX,
			CreatedAt: time.Now(),
		})
		alarm := model.Alarm{
			Type:       model.AlarmTypeCommon,
			Status:     model.AlarmStatusHappened,
			DeviceID:   &param.ID,
			UnitID:     &unit.Point,
			DeviceName: &device.Name,
			UnitName:   &configUnit.Name,
			Value:      &unit.Value,
			Flag:       &configUnit.Flag,
			AreaID:     &device.Area.ID,
			AreaName:   &device.Area.Name,
			Level:      configUnit.AlarmLevel,
			Message: fmt.Sprintf(
				"项目中发生报警，时间:%s,区域为：%s, 设备为：%s, 测点为：%s，报警等级为：%s，报警信息为：%s，当前值为：%s，请及时处理！",
				time.Now().Format("2006-01-02 15:04:05"),
				device.Area.Name,
				device.Name,
				configUnit.Name,
				configUnit.AlarmLevel,
				limitDesc,
				unit.Value,
			),
		}

		path, err := device.Config.GetConfigPath()
		if err != nil {
			d.l.Warn("get config path failure", zap.Error(err))
			path = "common_device"
		}
		err = d.alarmService.Create(alarm, path)
		if err != nil {
			d.l.Warn("create alarm failure", zap.Error(err))
		}
		err = d.alarmService.Publish(response.AlarmListPayload{
			Type:  model.AlarmStatusHappened,
			Path:  path,
			Alarm: alarm,
		})
		if err != nil {
			d.l.Warn("publish alarm failure", zap.Error(err))
		}

		//发送报警通知
		err = d.alarmGroup.SendAlarm(
			param.ID,
			alarm.Message,
			"alarm",
			limit.Priority,
			device.Area.Name+"+"+device.Name,
			configUnit.Name,
			configUnit.AlarmLevel,
			"发生报警！",
			unit.Value,
		)

		if err != nil {
			d.l.Warn("send alarm failure", zap.Error(err))
		}

		//调用声光
		if binding.ID == 0 {
			binding, err = d.deviceRepo.AlarmedBinding(param.ID)
			if err != nil {
				d.l.Warn("get alarm binding failure", zap.Error(err), zap.Int("device", param.ID))
				continue
			}
		}

		bindingCache, err = d.deviceRepo.DOUnit(binding.BindingID)
		if err != nil {
			d.l.Warn("get do unit failure", zap.Error(err), zap.Int("device", param.ID))
			continue
		}

		err = d.callDOAlarm(binding, bindingCache)
		if err != nil {
			d.l.Warn("call do alarm failure", zap.Error(err), zap.Int("device", param.ID), zap.Int("point", unit.Point))
		} else {
			//log
			logs = append(logs, model.AlarmLog{
				DeviceID: &param.ID,
				UnitID:   &unit.Point,
				Event:    "调用声光报警",
				Message: fmt.Sprintf(
					"声光报警名称：%s，id：%d",
					bindingCache.Name,
					binding.BindingID,
				),
				CreatedAt: time.Now(),
			})
		}

	}
	// if flag {
	// 	param.Status = deviceStatus
	// }
	return nil
}
func (d *DeviceService) RealtimeDataFor3D(param request.RealtimeData) ([]response.DeviceDataFor3D, error) {
	data, err := d.deviceRepo.RealtimeData(param)
	if err != nil {
		return nil, err
	}
	devices, err := d.NameList(request.NameList{
		DeviceIDs: param.DeviceIDs,
	})
	if err != nil {
		return nil, err
	}

	var (
		unitNames = make(map[int]map[int]string)
		names     = make(map[int]string)
		results   = make([]response.DeviceDataFor3D, 0, len(param.DeviceIDs))
	)

	for _, device := range devices.Devices {
		unitNames[device.ID] = make(map[int]string)
		names[device.ID] = device.Name
	}

	for _, unit := range devices.Units {
		unitNames[unit.ID][unit.UnitID] = unit.Name
	}

	for _, v := range data {
		name, ok := names[v.ID]
		if !ok {
			d.l.Warn("get device name failure", zap.Error(err), zap.Int("device", v.ID))
			name = "unknown"
		}
		units := make([]response.DeviceDataUnitFor3D, 0, len(v.Units))
		for _, unit := range v.Units {
			unitName, ok := unitNames[v.ID][unit.Point]
			if !ok {
				d.l.Warn("get unit name failure", zap.Error(err), zap.Int("device", v.ID), zap.Int("unit", unit.Point))
				unitName = "unknown"
			}
			units = append(units, response.DeviceDataUnitFor3D{
				Point:  unit.Point,
				Value:  unit.Value,
				Status: unit.Status,
				Name:   unitName,
			})
		}

		var tmp = response.DeviceDataFor3D{
			Type:   v.Type,
			ID:     v.ID,
			Status: v.Status,
			Name:   name,
			Units:  units,
		}

		results = append(results, tmp)
	}

	return results, nil
}

func (d *DeviceService) RealtimeData(param request.RealtimeData) ([]response.DeviceData, error) {
	return d.deviceRepo.RealtimeData(param)
}

func (d *DeviceService) RealtimeStatus(param request.RealtimeData) ([]response.RealtimeDeviceStatus, error) {
	return d.deviceRepo.RealtimeStatus(param)
}

func (d *DeviceService) NameList(param request.NameList) (response.DeviceNames, error) {
	var result response.DeviceNames
	names, err := d.deviceRepo.NameList(param.DeviceIDs)
	if err != nil {
		return result, err
	}
	units, err := d.deviceRepo.UnitName(param.DeviceIDs)
	if err != nil {
		return result, err
	}
	return response.DeviceNames{
		Devices: names,
		Units:   units,
	}, nil
}

func (d *DeviceService) ListByAreaId(param request.DeviceListByAreaID) ([]response.DeviceList, error) {
	return d.deviceRepo.ListByAreaId(param.UID, param.AreaID)
}

func (d *DeviceService) ListByAreaIDRecursive(param request.DeviceListByAreaID) ([]model.Device, error) {
	return d.deviceRepo.ListByAreaIDRecursive(param.UID, param.AreaID)
}

func (d *DeviceService) ListWithConfig(param request.DeviceListWithConfig) ([]response.DeviceListWithConfig, error) {
	list, err := d.deviceRepo.ListWithConfig(param.AreaID, param.BusinessTypeID, param.DepartmentID)
	if err != nil {
		return list, err
	}
	for i := 0; i < len(list); i++ {
		for j := 0; j < len(list[i].Departments); j++ {
			list[i].DepartmentIDs = append(list[i].DepartmentIDs, list[i].Departments[j].DepartmentID)
		}
	}
	return list, nil
}

func (d *DeviceService) ListByPort(param request.DeviceListByPort) ([]model.DeviceWithAreaName, error) {
	return d.deviceRepo.ListByPort(param.PortID, param.UID)
}

func (d *DeviceService) AlarmBinding(param request.DeviceAlarmBinding) ([]model.Device, error) {
	return d.deviceRepo.ListByBindingAlarmed(param.BindingID)
}

func (d *DeviceService) SetAlarmBinding(param request.SetDeviceAlarmBinding) error {
	dabs := make([]model.DeviceAlarmBinding, 0, len(param.DeviceIDs))
	for _, id := range param.DeviceIDs {
		dabs = append(
			dabs,
			model.DeviceAlarmBinding{ID: id, BindingID: param.BindingID})
	}
	return d.deviceRepo.SetBindingAlarmed(param.BindingID, dabs)
}

func (d *DeviceService) callDOAlarm(binding model.DOBinding, cache model.DeviceConfigUnit) error {
	if cache.Relpoly.Type == "once" {
		result := d.deviceRepo.DOUnitLock(binding.DOID(), cache.Relpoly.OnceTime)
		if !result {
			d.l.Debug("do unit lock failure", zap.Int(binding.Type(), binding.Trigger()), zap.String("unit", cache.Name))
			return nil
		}
	}

	err := d.Control(request.DeviceControl{
		DeviceID: binding.DOID(),
		UnitID:   cache.ID,
		Value:    d.doOn,
	})
	if err != nil || cache.Relpoly.Type == "always" {
		return err
	}

	defer func() {
		_ = <-time.Tick(time.Duration(cache.Relpoly.OnceTime) * time.Second)
		err = d.Control(request.DeviceControl{
			DeviceID: binding.DOID(),
			UnitID:   cache.ID,
			Value:    d.doOff,
		})
		if err != nil {
			d.l.Warn("do off failure", zap.Error(err), zap.Int(binding.Type(), binding.Trigger()), zap.String("unit", cache.Name))
		}
	}()
	return nil
}

func (d *DeviceService) boardConfig(deviceType, unitName string) model.CommonDevice {
	empty := make([]string, 0, 0)
	data, err := json.Marshal(empty)
	if err != nil {
		d.l.Warn("marshal empty array failure", zap.Error(err))
		return model.CommonDevice{}
	}
	switch deviceType {
	case "AI":
		return model.CommonDevice{
			Unit: []model.DeviceConfigUnit{
				{
					ID:         0,
					Low:        -1,
					High:       1,
					Name:       unitName,
					Type:       "ai",
					RegNo:      2,
					RegRw:      "R",
					RegCmd:     3,
					RegNum:     1,
					Status:     "ok",
					Activity:   true,
					Savetime:   7200,
					SubValue:   data,
					FilterCnt:  10,
					ValueType:  "uint16",
					AlarmLevel: "重要",
					EffectTime: []string{
						"00:00:00",
						"23:59:59",
					},
					Alarmenable: true,
					Alarmstatus: 1,
				},
			},
		}
	case "DI":
		return model.CommonDevice{
			Unit: []model.DeviceConfigUnit{
				{
					ID:         0,
					Low:        -1,
					High:       1,
					Name:       unitName,
					Type:       "di",
					RegNo:      1,
					RegRw:      "R",
					Action:     0,
					RegCmd:     3,
					RegNum:     1,
					Status:     "ok",
					Activity:   true,
					Savetime:   7200,
					SubValue:   data,
					FilterCnt:  10,
					ValueType:  "uint16",
					AlarmLevel: "重要",
					EffectTime: []string{
						"00:00:00",
						"23:59:59",
					},
					Alarmenable: true,
					Alarmstatus: 1,
				},
			},
		}
	case "DO":
		return model.CommonDevice{
			Unit: []model.DeviceConfigUnit{
				{
					ID:         0,
					Name:       unitName,
					Type:       "do",
					RegNo:      3,
					RegRw:      "RW",
					RegCmd:     3,
					RegNum:     1,
					Status:     "ok",
					Activity:   true,
					Savetime:   7200,
					SubValue:   data,
					FilterCnt:  10,
					ValueType:  "uint16",
					AlarmLevel: "重要",
					EffectTime: []string{
						"00:00:00",
						"23:59:59",
					},
					Alarmenable: true,
					Alarmstatus: 1,
				},
			},
		}
	default:
		return model.CommonDevice{}
	}
}

func (d *DeviceService) ResumeReportData(param []request.ResumeDeviceData) error {
	return d.deviceRepo.ResumeStoreData(param)
}

func (d *DeviceService) Detail(param request.DeviceDetail) (response.DeviceDetail, error) {
	return d.deviceRepo.FirstWithDepartments(model.Device{
		ID: param.ID,
	},
		[]string{
			"id",
			"name",
			"area_id",
			"activity",
		}...,
	)
}

func (d *DeviceService) UpdateUnitByDriver(param request.UpdateDeviceUnitByDriver) error {
	var (
		updater = datatypes.JSONSet("config")
	)

	for key, value := range param.Fields {
		updater = updater.Set(
			fmt.Sprintf("{common_device,unit,%d,%s}", param.UnitID, key),
			value,
		)
	}
	err := d.deviceRepo.UpdateConfigByDriver(param.DriverID, updater)
	if err != nil {
		return err
	}

	ids, err := d.deviceRepo.IDsByDriverID(param.DriverID)
	if err != nil {
		return err
	}
	d.deviceRepo.BatchRemoveDeviceUnits(ids)

	return nil
}

func (d *DeviceService) NameListByDriverID(param request.DeviceNameListByDriver) ([]string, error) {
	return d.deviceRepo.NameListByDriverID(param.DriverID)
}

func (d *DeviceService) CloseSoundLight() error {
	list, err := d.deviceRepo.ListWithConfigByBusinessTypeID(d.soundLightTypeID)
	if err != nil {
		return err
	}

	for _, device := range list {
		for _, unit := range device.Config.Config.CommonDevice.Unit {
			if unit.Type != d.doType {
				continue
			}

			err = d.doControl(device.Config.Type, request.DeviceControl{
				DeviceID: device.ID,
				UnitID:   unit.ID,
				Value:    d.doOff,
			})
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func (d *DeviceService) ListByTopLevel(btID int, param request.DeviceListByTopLevel) ([]model.Device, error) {
	return d.deviceRepo.ListByTopLevel(btID, param.IsRecursive, model.Device{
		ID:             param.ID,
		BusinessTypeID: param.BusinessTypeID,
	})
}

func (d *DeviceService) ConfigListByTopLevel(btID int, param request.DeviceConfigListByTopLevel) ([]model.Config, error) {
	return d.deviceRepo.ConfigListByTopLevel(btID, param.ID)
}

func (d *DeviceService) createLimit(btID, createTotal int) error {
	bt, err := d.btRepo.First(model.BusinessType{
		ID: btID,
	})
	if err != nil {
		return err
	}
	total, err := d.deviceRepo.CountByBtID(btID)
	if err != nil {
		return err
	}

	if bt.Number < total+int64(createTotal) {
		return errors.New("添加后该类型设备数量已超过授权上限")
	}

	return nil
}

func (d *DeviceService) CreateAsset(param request.CreateAssetDevice) error {
	err := d.createLimit(param.BusinessTypeID, 1)
	if err != nil {
		return err
	}

	_, err = d.deviceRepo.Create(
		[]model.Device{
			{
				Name:           param.Name,
				Type:           param.Type,
				Activity:       model.DeviceActivityEnable,
				Status:         model.DeviceStatusNormal,
				AreaID:         param.AreaID,
				BusinessTypeID: param.BusinessTypeID,
			},
		},
		[]model.Config{
			{
				Type: model.DeviceTypeAsset,
				Config: model.DeviceConfig{
					AssetDevice: &model.AssetDevice{
						IP:   param.IP,
						Port: param.Port,
					},
				},
			},
		},
		param.CreateDeviceBasic,
	)

	return err
}

func (d *DeviceService) UpdateAsset(param request.UPdateAssetDevice) error {
	updater := datatypes.JSONSet("config")
	if param.IP == "" && param.Port == "" {
		return nil
	}
	if param.IP != "" {
		updater = updater.Set("{asset_device, ip}", param.IP)
	}
	if param.Port != "" {
		updater = updater.Set("{asset_device, port}", param.Port)
	}
	_, err := d.deviceRepo.UpdateConfig(param.ID, updater)
	return err
}

func (d *DeviceService) ReportDeviceData(param request.ReportDevicesData) error {
	gateway, err := d.gatewayRepo.First(model.Gateway{FlashID: &param.FID})
	if err != nil {
		return err
	}
	list, err := d.deviceRepo.ListWithSelector([]string{"id", "type", "internal_id"}, model.Device{GatewayID: &gateway.ID})
	if err != nil {
		return err
	}
	var (
		deviceMap = make(map[int]model.Device, len(list))
	)

	for _, device := range list {
		deviceMap[*device.InternalID] = device
	}

	for _, device := range param.Data {
		temp, ok := deviceMap[device.ID]
		if !ok {
			d.l.Warn("device not found", zap.Int("device", device.ID))
			continue
		}
		var data = request.DeviceData{
			ID:     temp.ID,
			Type:   temp.Type,
			Status: device.Status,
			Units:  make([]request.DeviceDataUnit, 0, len(device.Unit)),
		}

		for _, unit := range device.Unit {
			data.Units = append(data.Units, request.DeviceDataUnit{
				Point:  unit.ID,
				Value:  unit.Value,
				Status: unit.Status,
			})
		}

		err = d.deviceRepo.SetDataCache(data)
		if err != nil {
			d.l.Warn("set data cache failure", zap.Error(err))
			continue
		}
		err = d.deviceRepo.StoreData(data)
		if err != nil {
			d.l.Warn("store data failure", zap.Error(err))
			continue
		}

	}

	return nil
}

func (d *DeviceService) ReportAlarm(param request.ReportDeviceAlarm) error {
	gateway, err := d.gatewayRepo.First(model.Gateway{FlashID: &param.FID})
	if err != nil {
		d.l.Warn("get gateway by fid failure", zap.Error(err))
		return err
	}

	device, err := d.deviceRepo.FirstWithConfig(model.Device{GatewayID: &gateway.ID, InternalID: &param.DeviceID})
	if err != nil {
		d.l.Warn("get device failure", zap.Error(err))
		return err
	}

	var (
		unitName string
		level    string
		flag     string
		alarm    model.Alarm
		areaName string
		unitID   int
	)

	if device.AreaID != 0 {
		area, err := d.areaRepo.First(model.Area{ID: device.AreaID})
		if err == nil {
			areaName = area.Name
		}
	}

	for idx, unit := range device.Config.Config.ReportDevice.Unit {
		if unit.ID != param.UnitID {
			continue
		}
		level = unit.Level
		flag = unit.Flag
		unitName = unit.Name
		unitID = idx
	}
	path, err := device.Config.GetConfigPath()
	if err != nil {
		d.l.Warn("get config path failure", zap.Error(err))
		path = "common_device"
	}

	var msg = fmt.Sprintf(
		"区域：%s, 设备：%s, 测点：%s, 当前值：%s。",
		areaName,
		device.Name,
		unitName,
		param.Value,
	)
	switch param.Type {
	case 0:
		alarm.DeviceID = &device.ID
		alarm.UnitID = &unitID
		err = d.alarmService.Recovery1(alarm, path)
		if err != nil {
			d.l.Warn("recovery alarm failure", zap.Error(err))
		}

		err = d.alarmGroup.SendAlarm(
			device.ID,
			msg,
			"recovery",
			0,
			device.Name,
			unitName,
			level,
			"报警恢复",
			param.Value,
		)
		if err != nil {
			d.l.Warn("send alarm recovery failure", zap.Error(err))
		}
	case 1:
		alarm = model.Alarm{
			Type:       model.AlarmTypeCommon,
			Status:     model.AlarmStatusHappened,
			DeviceID:   &device.ID,
			DeviceName: &device.Name,
			UnitID:     &unitID,
			UnitName:   &unitName,
			Value:      &param.Value,
			AreaID:     &device.AreaID,
			AreaName:   &areaName,
			Level:      level,
			Flag:       &flag,
			Message:    msg,
		}
		err = d.alarmService.Create(alarm, path)
		if err != nil {
			d.l.Warn("create alarm failure", zap.Error(err))
		}

		d.alarmGroup.SendAlarm(
			device.ID,
			msg,
			"alarm",
			0,
			device.Name,
			unitName,
			level,
			"发生报警",
			param.Value,
		)
	default:
		return errors.New("未知报警类型")
	}

	return nil
}

func (d *DeviceService) ReportList(param request.ReportDeviceList) ([]model.DeviceWithGatewayInfo, int64, error) {
	var (
		filter model.Device
	)
	if param.GatewayID != 0 {
		filter.GatewayID = &param.GatewayID
	}
	return d.deviceRepo.ReportListWithGateway(param.Page, param.Size, filter)
}

func (d *DeviceService) SetReportInfo(param request.SetReportDeviceInfo) error {
}
