package service

import (
	"encoding/json"
	"fmt"
	"time"
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/system/crontab"
	"tw_platform/pkg/system/light_schedule"

	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
)

type LightScheduleService struct {
	lsr      *repository.LightScheduleRepo
	dr       *repository.DeviceRepo
	l        *zap.Logger
	cb       *crontab.Crontab
	adjustID int //可调光灯业务id
	onoffID  int //开关灯业务id
	lightID  int //照明业务id
}

func NewLightScheduleService(
	lsr *repository.LightScheduleRepo,
	dr *repository.DeviceRepo,
	l *zap.Logger,
	cb *crontab.Crontab,
) *LightScheduleService {
	return &LightScheduleService{
		lsr:      lsr,
		dr:       dr,
		l:        l,
		cb:       cb,
		adjustID: 93,
		onoffID:  73,
		lightID:  5,
	}
}

func (ls *LightScheduleService) Create(param request.CreateLightSchedule) error {
	id, err := ls.lsr.Create(param)
	if err != nil {
		return err
	}
	schedule := light_schedule.NewLightScheduleJob(id, ls.dr, ls.lsr, ls.l)
	entryID, err := ls.cb.CronWithSeconds.AddJob(param.Cron, schedule)
	if err != nil {
		return err
	}
	err = ls.lsr.Update(model.LightSchedule{
		ID:     id,
		CronID: int(entryID),
	})
	return err
}

func (ls *LightScheduleService) Update(param request.UpdateLightSchedule) error {
	var (
		data = model.LightSchedule{
			ID:     param.ID,
			Cron:   param.Cron,
			Status: param.Status,
			Name:   param.Name,
			Type:   param.Type,
		}
		actions = make([]model.LightScheduleDetail, 0)
		err     error
		del     = make([]int, 0)
		add     = make([]model.LightScheduleDetail, 0)
		oldMap  = make(map[string]model.LightScheduleDetail, 0)
		newMap  = make(map[string]model.LightScheduleDetail, len(param.Actions))
	)

	if param.Cron != "" {
		schedule, err := ls.lsr.First(model.LightSchedule{ID: param.ID})
		if err != nil {
			return err
		}
		ls.cb.CronWithSeconds.Remove(cron.EntryID(schedule.CronID))
		entryID, err := ls.cb.CronWithSeconds.AddJob(param.Cron, light_schedule.NewLightScheduleJob(param.ID, ls.dr, ls.lsr, ls.l))
		if err != nil {
			return err
		}
		err = ls.lsr.Update(model.LightSchedule{
			ID:     param.ID,
			CronID: int(entryID),
		})
		if err != nil {
			return err
		}
	}

	l, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return err
	}

	if param.Begin != "" {
		begin, err := time.ParseInLocation(time.DateTime, param.Begin, l)
		if err != nil {
			return err
		}
		data.BeginAt = &begin
	}
	if param.End != "" {
		end, err := time.ParseInLocation(time.DateTime, param.End, l)
		if err != nil {
			return err
		}
		data.EndAt = &end
	}

	if len(param.Actions) == 0 {
		return ls.lsr.Update(data)
	}
	actions, err = ls.lsr.Actions(model.LightScheduleDetail{
		ScheduleID: data.ID,
	})
	if err != nil {
		return err
	}

	for _, action := range actions {
		oldMap[fmt.Sprintf("%d_%d_%s", action.DeviceID, action.UnitID, action.Value)] = action
	}

	for _, action := range param.Actions {
		newMap[fmt.Sprintf("%d_%d_%s", action.DeviceID, action.UnitID, action.Value)] = model.LightScheduleDetail{
			ScheduleID: param.ID,
			DeviceID:   action.DeviceID,
			UnitID:     action.UnitID,
			Value:      action.Value,
		}
	}

	for _, action := range actions {
		if _, ok := newMap[fmt.Sprintf("%d_%d_%s", action.DeviceID, action.UnitID, action.Value)]; !ok {
			del = append(del, action.ID)
		}
	}

	for _, action := range param.Actions {
		if _, ok := oldMap[fmt.Sprintf("%d_%d_%s", action.DeviceID, action.UnitID, action.Value)]; !ok {
			add = append(add, model.LightScheduleDetail{
				ScheduleID: param.ID,
				DeviceID:   action.DeviceID,
				UnitID:     action.UnitID,
				Value:      action.Value,
			})
		}
	}

	return ls.lsr.UpdateWithDetail(data, add, del)
}

func (ls *LightScheduleService) Detail(param request.LightScheduleDetail) (model.LightScheduleWithAction, error) {
	return ls.lsr.FirstWithActions(model.LightSchedule{
		ID: param.ID,
	})
}

func (ls *LightScheduleService) List(param request.LightScheduleList) ([]model.LightSchedule, error) {
	return ls.lsr.List(param.AreaID)
}

func (ls *LightScheduleService) Delete(param request.LishtScheduleDelete) error {
	schedule, err := ls.lsr.First(model.LightSchedule{
		ID: param.ID,
	})
	if err != nil {
		return err
	}
	ls.cb.CronWithSeconds.Remove(cron.EntryID(schedule.CronID))
	return ls.lsr.Delete(model.LightSchedule{
		ID: param.ID,
	})
}

func (ls *LightScheduleService) ControlByArea(param request.LightScheduleControlByArea) error {
	var (
		payloads = make([]request.DeviceControl, 0)
	)
	list, err := ls.dr.DetailByAreaID(param.AreaID, param.BusinessTypeID)
	if err != nil {
		return err
	}
	for _, device := range list {
		if device.Config.Config.CommonDevice == nil {
			continue
		}
		for i := 1; i < len(device.Config.Config.CommonDevice.Unit); i++ {
			unit := device.Config.Config.CommonDevice.Unit[i]
			if device.BusinessTypeID == ls.onoffID && unit.ExtraType != model.DeviceUnitExtraTypeControlLight {
				continue
			}
			payloads = append(payloads, request.DeviceControl{
				DeviceID: device.ID,
				UnitID:   unit.ID,
				Value:    param.Value,
			})
		}
	}
	data, err := json.Marshal(payloads)
	if err != nil {
		return err
	}
	err = ls.dr.BatchControl(data)
	return err
}

func (ls *LightScheduleService) Statistic(param request.LightScheduleStatistic) (response.LightScheduleStatisticResp, error) {
	var data response.LightScheduleStatisticResp
	devices, err := ls.dr.DetailByAreaID(param.AreaID, ls.lightID)
	if err != nil {
		return data, err
	}

	var (
		ids     = make([]int, 0, len(devices))
		results = make([]response.LightScheduleStatistic, 0)
		dataMap = make(map[string]string, 0)
		nameMap = make(map[string]string, 0)
	)

	for _, device := range devices {
		ids = append(ids, device.ID)
	}
	resp, err := ls.dr.RealtimeData(request.RealtimeData{
		DeviceIDs: ids,
	})
	if err != nil {
		return data, err
	}

	var extraData struct {
		LightCount int `json:"lightCount"`
	}

	for _, data := range resp {
		for _, unit := range data.Units {
			dataMap[fmt.Sprintf("%d_%d", data.ID, unit.Point)] = unit.Value
		}
	}

	for _, device := range devices {
		if device.Config.Config.CommonDevice == nil {
			continue
		}
		for _, unit := range device.Config.Config.CommonDevice.Unit {
			if unit.ID == 0 || (device.BusinessTypeID == ls.onoffID && unit.ExtraType != model.DeviceUnitExtraTypeControlLight) {
				continue
			}
			key := fmt.Sprintf("%d_%d", device.ID, unit.ID)
			temp := response.LightScheduleStatistic{
				DeviceID:       device.ID,
				UnitID:         unit.ID,
				BusinessTypeID: device.BusinessTypeID,
				Value:          dataMap[key],
			}
			if unit.ExtraName != "" {
				nameMap[key] = unit.ExtraName
			} else {
				nameMap[key] = unit.Name
			}
			if device.BusinessTypeID == ls.onoffID && unit.ExtraData != "" {
				err = json.Unmarshal([]byte(unit.ExtraData), &extraData)
				if err == nil {
					temp.LightCount = extraData.LightCount
				}
			}
			results = append(results, temp)
		}
	}

	actions, err := ls.lsr.ActionsByAreaID(param.AreaID)
	if err != nil {
		return data, err
	}
	for _, action := range actions {
		key := fmt.Sprintf("%d_%d", action.DeviceID, action.UnitID)
		data.Actions = append(data.Actions, response.LightScheduleAction{
			Name:   nameMap[key],
			Value:  action.Value,
			ExecAt: action.ExecedAt,
		})
	}

	schedules, err := ls.lsr.List(param.AreaID)

	data.Schedules = schedules
	data.Statistics = results

	return data, nil
}
func (ls *LightScheduleService) StatisticAll(param request.LightScheduleStatistic) (response.LightScheduleStatisticResp, error) {
	var data response.LightScheduleStatisticResp
	devices, err := ls.dr.DetailByUserID(param.UID, ls.lightID)
	if err != nil {
		return data, err
	}

	var (
		ids     = make([]int, 0, len(devices))
		results = make([]response.LightScheduleStatistic, 0)
		dataMap = make(map[string]string, 0)
		nameMap = make(map[string]string, 0)
	)

	for _, device := range devices {
		ids = append(ids, device.ID)
	}
	resp, err := ls.dr.RealtimeData(request.RealtimeData{
		DeviceIDs: ids,
	})
	if err != nil {
		return data, err
	}

	var extraData struct {
		LightCount int `json:"lightCount"`
	}

	for _, data := range resp {
		for _, unit := range data.Units {
			dataMap[fmt.Sprintf("%d_%d", data.ID, unit.Point)] = unit.Value
		}
	}

	for _, device := range devices {
		if device.Config.Config.CommonDevice == nil {
			continue
		}
		for _, unit := range device.Config.Config.CommonDevice.Unit {
			if unit.ID == 0 || (device.BusinessTypeID == ls.onoffID && unit.ExtraType != model.DeviceUnitExtraTypeControlLight) {
				continue
			}
			key := fmt.Sprintf("%d_%d", device.ID, unit.ID)
			temp := response.LightScheduleStatistic{
				DeviceID:       device.ID,
				UnitID:         unit.ID,
				BusinessTypeID: device.BusinessTypeID,
				Value:          dataMap[key],
			}
			if unit.ExtraName != "" {
				nameMap[key] = unit.ExtraName
			} else {
				nameMap[key] = unit.Name
			}
			if device.BusinessTypeID == ls.onoffID && unit.ExtraData != "" {
				err = json.Unmarshal([]byte(unit.ExtraData), &extraData)
				if err == nil {
					temp.LightCount = extraData.LightCount
				}
			}
			results = append(results, temp)
		}
	}

	actions, err := ls.lsr.ActionsByUserID(param.UID)
	if err != nil {
		return data, err
	}
	for _, action := range actions {
		key := fmt.Sprintf("%d_%d", action.DeviceID, action.UnitID)
		data.Actions = append(data.Actions, response.LightScheduleAction{
			Name:   nameMap[key],
			Value:  action.Value,
			ExecAt: action.ExecedAt,
		})
	}

	schedules, err := ls.lsr.ListByUserID(param.UID)

	data.Schedules = schedules
	data.Statistics = results

	return data, nil
}

func (ls *LightScheduleService) ControlAll(param request.LightScheduleControlAll) error {
	var (
		payloads = make([]request.DeviceControl, 0)
	)
	list, err := ls.dr.DetailByBusinessTypeID(param.BusinessTypeID, param.UID)
	if err != nil {
		return err
	}
	for _, device := range list {
		if device.Config.Config.CommonDevice == nil {
			continue
		}
		for i := 1; i < len(device.Config.Config.CommonDevice.Unit); i++ {
			unit := device.Config.Config.CommonDevice.Unit[i]
			if device.BusinessTypeID == ls.onoffID && unit.ExtraType != model.DeviceUnitExtraTypeControlLight {
				continue
			}
			payloads = append(payloads, request.DeviceControl{
				DeviceID: device.ID,
				UnitID:   unit.ID,
				Value:    param.Value,
			})
		}
	}
	data, err := json.Marshal(payloads)
	if err != nil {
		return err
	}
	err = ls.dr.BatchControl(data)
	return err
}
