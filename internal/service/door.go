package service

import (
	"bytes"
	"tw_platform/internal/repository"
	"tw_platform/pkg/door/intemach"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/system/config"

	"gorm.io/datatypes"

	"go.uber.org/zap"
)

type DoorService struct {
	doorStore   *repository.DoorRepo
	config      *config.Config
	l           *zap.Logger
	deviceStore *repository.DeviceRepo
}

func NewDoorService(doorStore *repository.DoorRepo, config *config.Config, log *zap.Logger, deviceStore *repository.DeviceRepo) *DoorService {
	return &DoorService{
		doorStore:   doorStore,
		config:      config,
		l:           log.Named("door_service:"),
		deviceStore: deviceStore,
	}
}

func (d *DoorService) GetDoorList() (response.GetDoorListRes, error) {
	list, err := d.doorStore.GetDoorList()
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (d *DoorService) NewDoorList(req request.NewDoorReq) error {
	var (
		doorDerivce []model.Device
		doorConfig  []model.Config
	)

	for i := range req.Devices {
		//device
		dev := model.Device{
			Name:           req.Devices[i].DeviceName,
			Type:           req.Devices[i].Type,
			Activity:       model.DeviceActivityEnable,
			Status:         model.DeviceStatusNormal,
			AreaID:         req.CreateDeviceBasic.AreaID,
			BusinessTypeID: req.CreateDeviceBasic.BusinessTypeID,
		}
		doorDerivce = append(doorDerivce, dev)
		//config
		conf := model.Config{
			Type: 8,
			Config: model.DeviceConfig{
				DoorDevice: &req.Devices[i].Config,
			},
		}
		doorConfig = append(doorConfig, conf)
	}
	_, err := d.deviceStore.Create(doorDerivce, doorConfig, req.CreateDeviceBasic)
	return err
}

func (d *DoorService) GetDoorInfo(id int) (*model.DoorInfo, error) {
	return d.doorStore.GetDoorInfo(id)
}

func (d *DoorService) EditDoor(req request.EditDoorReq) error {
	var deviceInfo model.Device

	if req.DeviceName != "" {
		deviceInfo.Name = req.DeviceName
	}
	doorConfig := model.DoorInfo{
		Type:         req.Type,
		Ip:           req.Ip,
		ManName:      req.ManName,
		ManPwd:       req.ManPwd,
		ManuFacturer: req.ManuFacturer,
		Port:         req.Port,
		Name:         req.Name,
		Ability:      req.Ability,
		SubCount:     req.SubCount,
	}
	expr := datatypes.JSONSet("config").Set("{door_device}", doorConfig)
	_, err := d.deviceStore.Update(req.Id, deviceInfo)
	if err != nil {
		return err
	}
	_, err = d.deviceStore.UpdateConfig(req.Id, expr)
	if err != nil {
		return err
	}
	return err
}

func (d *DoorService) CtrlDoor(req request.CtrDoorReq, doorInfo *model.DoorInfo) error {
	im, err := intemach.GetDoorImpl(doorInfo)
	if err != nil {
		return err
	}

	return im.CtlDoorStatus(req.Way)
}

func (d *DoorService) GetDoorStatus(doorInfo *model.DoorInfo) (int, error) {
	im, err := intemach.GetDoorImpl(doorInfo)
	if err != nil {
		return 0, err
	}
	return im.GetDoorStatus()
}

func (d *DoorService) GetUserList(doorInfo *model.DoorInfo) (response.GetDoorUserRes, error) {
	im, err := intemach.GetDoorImpl(doorInfo)
	if err != nil {
		return nil, err
	}
	list, err := im.GetUserList()
	if err != nil {
		return nil, err
	}
	res := response.GetDoorUserRes{}
	for i := range list {
		res = append(res, response.GetDoorUserResItem{
			EmployeeNo:     list[i].EmployeeNo,
			Name:           list[i].Name,
			Password:       list[i].Password,
			LocalUIRight:   list[i].LocalUIRight,
			UserVerifyMode: list[i].UserVerifyMode,
		})
	}
	return res, nil
}

func (d *DoorService) AddUser(req request.AddDoorUserReq, info *model.DoorInfo) error {
	im, err := intemach.GetDoorImpl(info)
	if err != nil {
		return err
	}
	return im.AddOneUser(req.EmployeeNo, req.Name, req.Password, req.UserVerifyMode, req.LocalUIRight)
}

func (d *DoorService) EditUser(req request.EditDoorUserReq, info *model.DoorInfo) error {
	im, err := intemach.GetDoorImpl(info)
	if err != nil {
		return err
	}
	return im.ChangeOneUser(req.Emno, req.Name, req.Pwd, req.UserVerifyMode, req.LocalUIRight)
}

func (d *DoorService) DelUser(req request.DelDoorUserReq, info *model.DoorInfo) error {
	im, err := intemach.GetDoorImpl(info)
	if err != nil {
		return err
	}
	return im.DelOneUser(req.EmployeeNoList)
}

func (d *DoorService) GetCard(req request.GetCardReq, doorInfo *model.DoorInfo) (response.GetDoorCardRes, error) {
	im, err := intemach.GetDoorImpl(doorInfo)
	if err != nil {
		return nil, err
	}
	list, err := im.GetUserCard(req.EmployeeNo)
	if err != nil {
		return nil, err
	}

	res := response.GetDoorCardRes{}
	for i := range list {
		res = append(res, response.GetDoorCardResItem{
			CardNo:   list[i].CardNo,
			CardType: list[i].CardType,
		})
	}
	return res, nil
}

func (d *DoorService) AddCard(req request.AddCardReq, doorInfo *model.DoorInfo) error {
	im, err := intemach.GetDoorImpl(doorInfo)
	if err != nil {
		return err
	}
	return im.AddUserCard(req.EmployeeNo, req.CardNo, req.CardType)
}

func (d *DoorService) DelCard(req request.DelCardReq, doorInfo *model.DoorInfo) error {
	im, err := intemach.GetDoorImpl(doorInfo)
	if err != nil {
		return err
	}
	delNo := []intemach.DelCardByNoParam{}
	for i := range req.DelCardByNo {
		delNo = append(delNo, intemach.DelCardByNoParam{
			CardNo: req.DelCardByNo[i].CardNo,
		})
	}
	return im.DelUserCard(delNo)
}

func (d *DoorService) GetFPData(req request.GetFpDataReq, doorInfo *model.DoorInfo) (response.GetFpDataRes, error) {
	var (
		fpData response.GetFpDataRes
	)
	im, err := intemach.GetDoorImpl(doorInfo)
	if err != nil {
		return nil, err
	}
	list, err := im.GetUserFp(req.EmployeeNo)
	if err != nil {
		return nil, err
	}
	for i := range list {
		fpData = append(fpData, response.GetFpDataResItem{
			FingerPrintID: list[i].FingerPrintID,
			FingerType:    list[i].FingerType,
			FingerData:    list[i].FingerData,
		})
	}
	return fpData, nil
}

func (d *DoorService) AddFpData(req *request.AddFpDataReq, doorInfo *model.DoorInfo) error {
	im, err := intemach.GetDoorImpl(doorInfo)
	if err != nil {
		return err
	}

	return im.AddUserFp(req.EmployeeNo, req.FingerData, req.FingerType, req.FingerPrintId)

}

func (d *DoorService) GetFpProcess(doorInfo *model.DoorInfo) (*response.GetFpDataProcessRes, error) {

	im, err := intemach.GetDoorImpl(doorInfo)
	if err != nil {
		return nil, err
	}
	status, err := im.GetAddFpProgress()
	if err != nil {
		return nil, err
	}
	return &response.GetFpDataProcessRes{
		TotalStatus: status,
	}, nil
}

func (d *DoorService) GetDelFpProcess(doorInfo *model.DoorInfo) (*response.GetFpDelProcessRes, error) {
	im, err := intemach.GetDoorImpl(doorInfo)
	if err != nil {
		return nil, err
	}
	status, err := im.GetDelFpProgress()
	if err != nil {
		return nil, err
	}
	return &response.GetFpDelProcessRes{
		DelFingerProcess: status,
	}, nil
}

func (d *DoorService) DelFinger(req *request.DelFingerReq, info *model.DoorInfo) error {
	im, err := intemach.GetDoorImpl(info)
	if err != nil {
		return err
	}
	return im.DelUserFp(req.EmployeeNo, req.FingerPrintID)
}

func (d *DoorService) GetFace(req *request.GetFaceReq, info *model.DoorInfo) (*response.GetFaceRes, error) {
	im, err := intemach.GetDoorImpl(info)
	if err != nil {
		return nil, err
	}
	data, err := im.GetUserFace(req.EmployeeNo)
	if err != nil {
		return nil, err
	}
	return &response.GetFaceRes{
		Face: data.Face,
	}, nil
}

func (d *DoorService) DelFace(req *request.DelFaceReq, info *model.DoorInfo) error {
	im, err := intemach.GetDoorImpl(info)
	if err != nil {
		return err
	}
	return im.DelUserFace(req.EmployeeNo)
}

func (d *DoorService) AddFace(info *model.DoorInfo, f *request.WebCmdFafceInfo, b bytes.Buffer, ap model.HkAddFaceParam) error {
	im, err := intemach.GetDoorImpl(info)
	if err != nil {
		return err
	}
	return im.AddUserFace(f.FPID, b.Bytes(), ap)
}

func (d *DoorService) GetEventRecord(doorInfo *model.DoorInfo, doorId int, pos int, startTime string, endTime string) (*intemach.ToWebRecord, error) {
	im, err := intemach.GetDoorImpl(doorInfo)
	if err != nil {
		return nil, err
	}

	list, err := im.GetEventRecord(doorId, pos, startTime, endTime)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (d *DoorService) StartSync(req request.StartSyncReq, doorConf []model.Door) error {
	doorInfo := model.DoorInfo{}
	for i := range doorConf {
		if doorConf[i].Id == int64(req.Source.Id) {
			doorInfo = doorConf[i].DoorInfo
			break
		}
	}

	im, err := intemach.GetDoorImpl(&doorInfo)
	if err != nil {
		return err
	}

	//syc := request.StartSyncReq{
	//	Source:      req.Source,
	//	Destination: req.Destination,
	//}

	//syc := SyncReq{}
	//syc.Destination = req.Destination
	//syc.Source.Id = req.Source.Id
	//syc.Source.EmployeeNoList = req.Source.EmployeeNoList

	go im.StartSync(req, doorConf)
	return nil
}
func (d *DoorService) GetSyncResult() (bool, intemach.SyncRet, intemach.ErrSync) {
	return intemach.GetSyanresult()
}
