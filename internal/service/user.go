package service

import (
	"errors"
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/system/config"
	"tw_platform/pkg/utils"

	"golang.org/x/crypto/bcrypt"
)

type UserService struct {
	userStore *repository.UserRepo
	config    *config.Config
}

func NewUserService(
	userStore *repository.UserRepo,
	config *config.Config,
) *UserService {
	return &UserService{
		userStore: userStore,
		config:    config,
	}
}

func (us *UserService) Create(param request.CreateUser) error {
	password, err := bcrypt.GenerateFromPassword([]byte("changeMe"), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	user := model.User{
		Name:     param.Name,
		Nickname: param.Nickname,
		Phone:    param.Phone,
		Email:    param.Email,
		Password: string(password),
		RoleID:   param.RoleID,
		Status:   model.UserStatusNormal,
	}
	return us.userStore.Create(&user)
}

func (us *UserService) Login(param request.Login) (response.Login, error) {
	var (
		filter model.User
		token  string
		login  response.Login
	)

	if utils.ValidatePhoneNum(param.Account) {
		filter.Phone = param.Account
	} else {
		filter.Name = param.Account
	}
	user, err := us.userStore.FirstWithRole(filter)
	if err != nil {
		return login, errors.New("用户不存在")
	}

	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(param.Password))
	if err != nil {
		return login, errors.New("密码不匹配")
	}

	token, err = NewToken(user.ID, user.RoleID, us.config.JWT)
	login.Token = token
	login.Nickname = user.Nickname
	login.RoleName = user.RoleName
	login.UserID = user.ID
	return login, err
}

func (us *UserService) List(param request.UserList) ([]response.UserList, int64, error) {
	var (
		filter = model.User{
			Name:   param.Name,
			Phone:  param.Phone,
			RoleID: param.RoleID,
		}
	)

	return us.userStore.List(param.Page, param.Size, filter)
}

func (us *UserService) SetArea(param request.SetUserArea) error {
	var (
		areas = make([]model.UserArea, 0, len(param.Areas))
	)

	for _, id := range param.Areas {
		areas = append(areas, model.UserArea{
			UserID: param.ID,
			AreaID: id,
		})
	}

	return us.userStore.SetArea(param.ID, areas)
}

func (us *UserService) GetArea(param request.GetUserArea) ([]int, error) {
	if param.ID == 0 {
		param.ID = param.UID
	}
	return us.userStore.GetArea(param.ID)
}

func (us *UserService) SetDepartment(param request.SetUserDepartment) error {
	var (
		departments = make([]model.UserDepartment, 0, len(param.Departments))
	)

	for _, id := range param.Departments {
		departments = append(departments, model.UserDepartment{
			UserID:       param.ID,
			DepartmentID: id,
		})
	}

	return us.userStore.SetDepartment(param.ID, departments)
}

func (us *UserService) GetDepartment(param request.GetUserDepartment) ([]int, error) {
	if param.ID == 0 {
		param.ID = param.UID
	}
	return us.userStore.GetDepartment(param.ID)
}

func (us *UserService) SetBusinessType(param request.SetUserBusinessType) error {
	var (
		businessTypes = make([]model.UserBusinessType, 0, len(param.BusinessTypes))
	)

	for _, id := range param.BusinessTypes {
		businessTypes = append(businessTypes, model.UserBusinessType{
			UserID:         param.ID,
			BusinessTypeID: id,
		})
	}
	return us.userStore.SetBusinessType(param.ID, businessTypes)
}

func (us *UserService) GetBusinessType(param request.GetUserBusinessType) ([]int, error) {
	if param.ID == 0 {
		param.ID = param.UID
	}
	return us.userStore.GetBusinessType(param.ID)
}

func (us *UserService) Update(param request.UpdateUser) error {

	var user = model.User{
		ID: param.ID,
	}

	if param.Phone != "" {
		user.Phone = param.Phone
	}
	if param.Nickname != "" {
		user.Nickname = param.Nickname
	}
	if param.RoleID != 0 {
		user.RoleID = param.RoleID
	}
	if param.Status != 0 {
		user.Status = model.UserStatus(param.Status)
	}
	if param.Email != "" {
		user.Email = param.Email
	}

	return us.userStore.Update(user)
}

func (us *UserService) ChangePassword(param request.ChangePassword) error {
	user := model.User{
		ID: param.UID,
	}
	u, err := us.userStore.First(user)
	if err != nil {
		return err
	}
	err = bcrypt.CompareHashAndPassword([]byte(u.Password), []byte(param.Old))
	if err != nil {
		return errors.New("原密码不正确")
	}
	password, err := bcrypt.GenerateFromPassword([]byte(param.New), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	user.Password = string(password)
	return us.userStore.Update(user)
}

func (us *UserService) ResetPassword(param request.ResetPassword) error {
	user := model.User{
		ID: param.ID,
	}
	pwd, err := bcrypt.GenerateFromPassword([]byte(param.Password), bcrypt.DefaultCost)

	if err != nil {
		return err
	}
	user.Password = string(pwd)
	return us.userStore.Update(user)
}

func (us *UserService) ListSimplify(param request.UserListSimplify) ([]model.User, error) {
	return us.userStore.ListSimplify(model.User{
		ID: param.ID,
	})

}

func (us *UserService) Delete(param request.DeleteUser) error {
	return us.userStore.Delete(param.ID)
}

func (us *UserService) ListByID(param request.UserListByID) ([]model.User, error) {
	return us.userStore.ListByID(param.IDs)
}

func (us *UserService) ListByMenuID(param request.UserListByMenuID) ([]model.User, error) {
	return us.userStore.ListByMenuID(param.MenuID)
}
