package service

import (
	"bytes"
	"encoding/base64"
	"errors"
	"fmt"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"sync"
	"time"
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/system/config"

	uuid "github.com/satori/go.uuid"
	"go.uber.org/zap"
	"gorm.io/datatypes"
)

type VideoService struct {
	videoStore  *repository.VideoRepo
	config      *config.Config
	l           *zap.Logger
	deviceStore *repository.DeviceRepo
	rtmpAddr    string
}

var processMap sync.Map
var SSEMap sync.Map
var processNumMap sync.Map

const KEEP_ALIVE_MSG = "keepalvie"
const STOP_MSG = "stop"

type CtlFFMPEG_MSG struct {
	Action string
}
type processNum struct {
	Connections int
}

func NewVideoService(videoStore *repository.VideoRepo, config *config.Config, log *zap.Logger, deviceStore *repository.DeviceRepo) *VideoService {
	return &VideoService{
		videoStore:  videoStore,
		config:      config,
		l:           log.Named("video_service:"),
		deviceStore: deviceStore,
	}
}

func (us *VideoService) GetVideoList() ([]response.VideoListResponse, error) {
	list, err := us.videoStore.GetVideoList()
	if err != nil {
		return []response.VideoListResponse{}, err
	}
	return list, nil
}

func (us *VideoService) SetVideoList(param []request.CreateVideo) error {
	var err error
	for _, value := range param {
		var (
			deviceList = make([]model.Device, 0, len(value.Devices))
			configList = make([]model.Config, 0, len(value.Devices))
			conf       model.VideoDevice
		)
		for _, v := range value.Devices {
			configs := conf
			configs.Brand = v.Config.Brand

			configs.ChannelID = v.Config.ChannelID
			configs.IP = v.Config.IP
			configs.Name = v.Config.Name
			configs.Password = v.Config.Password
			configs.Stream = v.Config.Stream
			deviceList = append(deviceList, model.Device{
				Name:           v.DeviceName,
				Type:           v.Type,
				Activity:       1,
				Status:         1,
				AreaID:         value.AreaID,
				BusinessTypeID: value.BusinessTypeID,
			})
			if v.Type == "camera" {
				configList = append(configList, model.Config{
					Type: model.DeviceTypeCamera,
					Config: model.DeviceConfig{
						VideoDevice: &configs,
					},
				})
			} else {
				configList = append(configList, model.Config{
					Type: model.DeviceTypeNvr,
					Config: model.DeviceConfig{
						VideoDevice: &configs,
					},
				})
			}
		}
		/*		us.l.Info("create device", zap.Any("deviceList", deviceList), zap.Any("configList", configList))
				us.l.Info("create device", zap.Any("param.CreateDeviceBasic", value.CreateDeviceBasic))*/
		_, err = us.deviceStore.Create(deviceList, configList, value.CreateDeviceBasic)
		if err != nil {
			return err
		}
	}
	return err
}
func (us *VideoService) ModifyVideo(param request.ModifyVideo) error {
	var deviceInfo model.Device

	if param.DeviceName != "" {
		deviceInfo.Name = param.DeviceName
	}
	if param.Type != "" {
		deviceInfo.Type = param.Type
	}
	videoConfig := model.VideoDevice{
		Brand:     param.Config.Brand,
		ChannelID: param.Config.ChannelID,
		IP:        param.Config.IP,
		Name:      param.Config.Name,
		Password:  param.Config.Password,
		Stream:    param.Config.Stream,
	}
	expr := datatypes.JSONSet("config").Set("{video_device}", videoConfig)
	_, err := us.deviceStore.Update(param.ID, deviceInfo)
	if err != nil {
		return err
	}
	_, err = us.deviceStore.UpdateConfig(param.ID, expr)
	if err != nil {
		return err
	}
	return err
}
func (us *VideoService) PlayVideo(param request.RealTimePlayRequest) (url string, processCh string, err error) {
	var (
		cmd     *exec.Cmd
		process *os.Process
	)
	videoInfo, err := us.videoStore.PlayVideo(param)
	if err != nil {
		return "", "", err
	}
	rtsp := ""
	hashStr := videoInfo.Config.VideoDevice.Brand + strconv.Itoa(videoInfo.ID) + strconv.Itoa(param.ChannelID) + strconv.Itoa(int(videoInfo.Type))
	processCh = uuid.NewV3(uuid.NamespaceURL, hashStr).String()
	if ch, ok := processMap.Load(processCh); ok {
		if num, result := processNumMap.Load(processCh); result {
			connections := num.(processNum).Connections + 1
			processNumMap.Store(processCh, processNum{Connections: connections})
		}

		*ch.(*chan CtlFFMPEG_MSG) <- CtlFFMPEG_MSG{Action: KEEP_ALIVE_MSG}
		rtmpUrl := fmt.Sprintf("/stream/realtime%d%d", param.Id, param.ChannelID)
		return rtmpUrl, processCh, err
	}
	switch videoInfo.Type {
	case model.DeviceTypeCamera:
		rtsp = GenerateRtsp(videoInfo.Config, param.Id)
	case model.DeviceTypeNvr:
		channelId := param.ChannelID
		switch videoInfo.Config.VideoDevice.Brand {
		case "海康":
			//使用主码流
			subChStr := ""
			if videoInfo.Config.VideoDevice.Stream == "main" {
				subChStr = strconv.Itoa(channelId) + "01"
			} else {
				subChStr = strconv.Itoa(channelId) + "02"
			}
			rtsp = fmt.Sprintf(`rtsp://%s:%s@%s/%s`, videoInfo.Config.VideoDevice.Name, videoInfo.Config.VideoDevice.Password, videoInfo.Config.VideoDevice.IP+":554", "Streaming/Channels/"+subChStr)
		case "大华":
			// rtsp://admin:dahua123@***********:554/cam/realmonitor?channel=2&subtype=0
			//username: 用户名。例如admin。
			//password: 密码。例如admin。
			//ip: 为设备IP。例如 **********。
			//port: 端口号默认为554，若为默认可不填写。
			//channel: 通道号，起始为1。例如通道2，则为channel=2。
			//subtype: 码流类型，主码流为0（即subtype=0），辅码流为1（即subtype=1）
			suffix := ""
			if videoInfo.Config.VideoDevice.Stream == "main" {
				suffix = fmt.Sprintf(`cam/realmonitor?channel=%d&subtype=0`, channelId)
			} else {
				suffix = fmt.Sprintf(`cam/realmonitor?channel=%d&subtype=1`, channelId)
			}
			rtsp = fmt.Sprintf(`rtsp://%s:%s@%s/%s`, videoInfo.Config.VideoDevice.Name, videoInfo.Config.VideoDevice.Password, videoInfo.Config.VideoDevice.IP+":554", suffix)
		case "华为":
			//rtsp://admin:HuaWei123@************:443/rtsp/streaming?channel=3&subtype=0&metadata=true
			suffix := ""
			if videoInfo.Config.VideoDevice.Stream == "main" {
				suffix = fmt.Sprintf(`rtsp/streaming?channel=%d&subtype=0&metadata=true`, channelId)
			} else {
				suffix = fmt.Sprintf(`rtsp/streaming?channel=%d&subtype=1&metadata=true`, channelId)
			}
			rtsp = fmt.Sprintf(`rtsp://%s:%s@%s/%s`, videoInfo.Config.VideoDevice.Name, videoInfo.Config.VideoDevice.Password, videoInfo.Config.VideoDevice.IP+":443", suffix)
		case "宇视":
			//rtsp://用户名:密码@ip:port/unicast/c<channel number>/s<stream type>/live
			//<channel number>: 1-n
			//<stream type>: 0（主流），1（辅流）
			//通道1主码流示例： rtsp://admin:admin@***********:554/unicast/c1/s0/live
			suffix := ""
			if videoInfo.Config.VideoDevice.Stream == "main" {
				suffix = "0"
			} else {
				suffix = "1"
			}
			rtsp = fmt.Sprintf(`rtsp://%s:%s@%s:554/unicast/c%d/s%s/live`, videoInfo.Config.VideoDevice.Name, videoInfo.Config.VideoDevice.Password, videoInfo.Config.VideoDevice.IP, channelId, suffix)
		}
	default:
		return "", "", err
	}
	reflush := make(chan CtlFFMPEG_MSG)
	if cmd, process, url, err = StartFFMPEG(rtsp, param, us.l, us.config.VideoRtmp.Addr, us.config.VideoResolution.Resolution); err != nil {
		us.l.Error("StartFFMPEG error", zap.Error(err))
		return "", "", err
	} else {
		go keepFFMPEG(cmd, process, &reflush, processCh)
	}
	return url, processCh, err
}

func (us *VideoService) PlaybackVideo(param request.PlaybackRequest) (url string, processCh string, err error) {
	var (
		cmd     *exec.Cmd
		process *os.Process
	)
	videoInfo, err := us.videoStore.PlaybackVideo(param)
	if err != nil {
		return "", "", err
	}
	//if param.ChannelID > videoInfo.Config.VideoDevice.BranchNumber {
	//	return "", "", errors.New("通道号超出范围")
	//}
	analysis_time := func(timeStr string) string {
		tss, _ := time.ParseInLocation("2006:01:02:15:04:05", timeStr, time.Local)
		h8, _ := time.ParseDuration("-8h")
		t2 := tss.Add(h8)
		return t2.Format("2006-01-02T15:04:05Z")
	}
	rtsp := ""
	hashStr := videoInfo.Config.VideoDevice.Brand + strconv.Itoa(int(videoInfo.Type)) + strconv.Itoa(videoInfo.ID) + strconv.Itoa(param.ChannelID) + param.StartTime + param.EndTime
	processCh = uuid.NewV3(uuid.NamespaceURL, hashStr).String()
	if ch, ok := processMap.Load(processCh); ok {
		if num, result := processNumMap.Load(processCh); result {
			connections := num.(processNum).Connections + 1
			processNumMap.Store(processCh, processNum{Connections: connections})
		}
		*ch.(*chan CtlFFMPEG_MSG) <- CtlFFMPEG_MSG{Action: KEEP_ALIVE_MSG}
		rtmpUrl := fmt.Sprintf("/stream/playback%d%d%s", param.Id, param.ChannelID, processCh)
		return rtmpUrl, processCh, err
	}
	switch videoInfo.Config.VideoDevice.Brand {
	case "海康":
		timeargs_s := strings.Split(param.StartTime, ":")
		timeargs_e := strings.Split(param.EndTime, ":")
		timesday := timeargs_s[0] + timeargs_s[1] + timeargs_s[2]
		timest := timeargs_s[3] + timeargs_s[4] + timeargs_s[5]
		timeeday := timeargs_e[0] + timeargs_e[1] + timeargs_e[2]
		timeet := timeargs_e[3] + timeargs_e[4] + timeargs_e[5]
		if videoInfo.Config.VideoDevice.Stream == "main" {
			rtsp = fmt.Sprintf(`rtsp://%s:%s@%s:554/Streaming/tracks/%d01?starttime=%st%sz&endtime=%st%sz`,
				videoInfo.Config.VideoDevice.Name, videoInfo.Config.VideoDevice.Password, videoInfo.Config.VideoDevice.IP, param.ChannelID, timesday, timest, timeeday, timeet)
		} else {
			rtsp = fmt.Sprintf(`rtsp://%s:%s@%s:554/Streaming/tracks/%d02?starttime=%st%sz&endtime=%st%sz`,
				videoInfo.Config.VideoDevice.Name, videoInfo.Config.VideoDevice.Password, videoInfo.Config.VideoDevice.IP, param.ChannelID, timesday, timest, timeeday, timeet)
		}
	case "大华":
		//rtsp://admin:admin@***********:554/cam/playback?channel=1&subtype=0&starttime=2017_01_10_01_00_00&endtime=2017_01_10_02_00_00
		timeargs_s := strings.Split(param.StartTime, ":")
		timeargs_e := strings.Split(param.EndTime, ":")
		if videoInfo.Config.VideoDevice.Stream == "main" {
			rtsp = fmt.Sprintf("rtsp://%s:%s@%s:554/cam/playback?channel=%d?subtype=0&starttime=%s_%s_%s_%s_%s_%s&endtime=%s_%s_%s_%s_%s_%s",
				videoInfo.Config.VideoDevice.Name, videoInfo.Config.VideoDevice.Password, videoInfo.Config.VideoDevice.IP, param.ChannelID, timeargs_s[0], timeargs_s[1], timeargs_s[2], timeargs_s[3], timeargs_s[4], timeargs_s[5],
				timeargs_e[0], timeargs_e[1], timeargs_e[2], timeargs_e[3], timeargs_e[4], timeargs_e[5])
		} else {
			rtsp = fmt.Sprintf("rtsp://%s:%s@%s:554/cam/playback?channel=%d?subtype=1&starttime=%s_%s_%s_%s_%s_%s&endtime=%s_%s_%s_%s_%s_%s",
				videoInfo.Config.VideoDevice.Name, videoInfo.Config.VideoDevice.Password, videoInfo.Config.VideoDevice.IP, param.ChannelID, timeargs_s[0], timeargs_s[1], timeargs_s[2], timeargs_s[3], timeargs_s[4], timeargs_s[5],
				timeargs_e[0], timeargs_e[1], timeargs_e[2], timeargs_e[3], timeargs_e[4], timeargs_e[5])
		}
	case "华为":
		//"rtsp://"+user+":"+pass+"@"+ip+":"+port+"/rtsp/playback?channel="+ch+"&sutbype=0&recordtype=4294967295&starttime="+time1+"&endtime="+time2
		//华为的回放时间参数是UTC时间，需要将前端参数减去8小时

		if videoInfo.Config.VideoDevice.Stream == "main" {
			rtsp = fmt.Sprintf("rtsp://%s:%s@%s:443/rtsp/playback?channel=%d&csutbype=0&recordtype=4294967295&starttime=%s&endtime=%s",
				videoInfo.Config.VideoDevice.Name, videoInfo.Config.VideoDevice.Password, videoInfo.Config.VideoDevice.IP, param.ChannelID, analysis_time(param.StartTime), analysis_time(param.EndTime))
		} else {
			rtsp = fmt.Sprintf("rtsp://%s:%s@%s:443/rtsp/playback?channel=%d&csutbype=1&recordtype=4294967295&starttime=%s&endtime=%s",
				videoInfo.Config.VideoDevice.Name, videoInfo.Config.VideoDevice.Password, videoInfo.Config.VideoDevice.IP, param.ChannelID, analysis_time(param.StartTime), analysis_time(param.EndTime))
		}
	case "宇视":
		/*
				URL规定：
			rtsp://username:password@<address>:<port>/c<number>/b<starttime>/e<endtime>/replay/
			举例说明：
			回放通道 01 远程录像：
			rtsp://admin:admin@*************:554/c1/b1494208846/e1494209026/replay/
			时间范围是 starttime 到 endtime，其中 starttime 和 endtime 为 UTC 时间戳格式，需要减去8小时。
		*/
		//begints, _ := time.Parse("2006:01:02:15:04:05", v.Start)
		//endts, _ := time.Parse("2006:01:02:15:04:05", v.End)
		rtsp = fmt.Sprintf("rtsp://%s:%s@%s:554/c%d/b%s/e%s/replay/", videoInfo.Config.VideoDevice.Name, videoInfo.Config.VideoDevice.Password, videoInfo.Config.VideoDevice.IP, param.ChannelID, analysis_time(param.StartTime), analysis_time(param.EndTime))
	default:
		return "", "", errors.New("不支持的NVR型号")
	}
	//us.l.Debug("type", zap.Any("type", videoInfo.Type))
	reflush := make(chan CtlFFMPEG_MSG)
	if cmd, process, url, err = StartFFMPEGForPlayBack(rtsp, param, processCh, us.config.VideoRtmp.Addr, us.config.VideoResolution.Resolution); err != nil {
		return "", "", err
	} else {
		go keepFFMPEG(cmd, process, &reflush, processCh)
	}
	return url, processCh, err
}

func (us *VideoService) PreviewVideo(param request.PreviewVideoRequest) (url string, processCh string, err error) {
	var (
		cmd     *exec.Cmd
		process *os.Process
	)
	rtsp := ""
	hashStr := param.Brand + strconv.Itoa(param.Index) + strconv.Itoa(param.ChannelID) + param.Type
	processCh = uuid.NewV3(uuid.NamespaceURL, hashStr).String()
	if ch, ok := processMap.Load(processCh); ok {
		if num, result := processNumMap.Load(processCh); result {
			connections := num.(processNum).Connections + 1
			processNumMap.Store(processCh, processNum{Connections: connections})
		}

		*ch.(*chan CtlFFMPEG_MSG) <- CtlFFMPEG_MSG{Action: KEEP_ALIVE_MSG}
		rtmpUrl := fmt.Sprintf("/stream/realtime%d", param.ChannelID)
		return rtmpUrl, processCh, err
	}
	switch param.Type {
	case "camera":
		rtsp = GeneratePreviewRtsp(param)
	case "nvr":
		channelId := param.ChannelID
		switch param.Brand {
		case "海康":
			//使用主码流
			subChStr := ""
			if param.Stream == "main" {
				subChStr = strconv.Itoa(channelId) + "01"
			} else {
				subChStr = strconv.Itoa(channelId) + "02"
			}
			rtsp = fmt.Sprintf(`rtsp://%s:%s@%s/%s`, param.Name, param.Password, param.IP+":554", "Streaming/Channels/"+subChStr)
		case "大华":
			// rtsp://admin:dahua123@***********:554/cam/realmonitor?channel=2&subtype=0
			//username: 用户名。例如admin。
			//password: 密码。例如admin。
			//ip: 为设备IP。例如 **********。
			//port: 端口号默认为554，若为默认可不填写。
			//channel: 通道号，起始为1。例如通道2，则为channel=2。
			//subtype: 码流类型，主码流为0（即subtype=0），辅码流为1（即subtype=1）
			suffix := ""
			if param.Stream == "main" {
				suffix = fmt.Sprintf(`cam/realmonitor?channel=%d&subtype=0`, channelId)
			} else {
				suffix = fmt.Sprintf(`cam/realmonitor?channel=%d&subtype=1`, channelId)
			}
			rtsp = fmt.Sprintf(`rtsp://%s:%s@%s/%s`, param.Name, param.Password, param.IP+":554", suffix)
		case "华为":
			//rtsp://admin:HuaWei123@************:443/rtsp/streaming?channel=3&subtype=0&metadata=true
			suffix := ""
			if param.Stream == "main" {
				suffix = fmt.Sprintf(`rtsp/streaming?channel=%d&subtype=0&metadata=true`, channelId)
			} else {
				suffix = fmt.Sprintf(`rtsp/streaming?channel=%d&subtype=1&metadata=true`, channelId)
			}
			rtsp = fmt.Sprintf(`rtsp://%s:%s@%s/%s`, param.Name, param.Password, param.IP+":443", suffix)
		case "宇视":
			//rtsp://用户名:密码@ip:port/unicast/c<channel number>/s<stream type>/live
			//<channel number>: 1-n
			//<stream type>: 0（主流），1（辅流）
			//通道1主码流示例： rtsp://admin:admin@***********:554/unicast/c1/s0/live
			suffix := ""
			if param.Stream == "main" {
				suffix = "0"
			} else {
				suffix = "1"
			}
			rtsp = fmt.Sprintf(`rtsp://%s:%s@%s:554/unicast/c%d/s%s/live`, param.Name, param.Password, param.IP, channelId, suffix)
		}
	default:
		return "", "", err
	}
	us.l.Debug("type", zap.Any("type", param.Type))
	reflush := make(chan CtlFFMPEG_MSG)
	if cmd, process, url, err = StartFFMPEGPreview(rtsp, param, us.config.VideoRtmp.Addr, us.config.VideoResolution.Resolution); err != nil {
		return "", "", err
	} else {
		go keepFFMPEG(cmd, process, &reflush, processCh)
	}
	return url, processCh, err
}

func (us *VideoService) StopPlayingFFMPEG(param request.VideoProcessChannel) {
	if ch, ok := processMap.Load(param.ProcessChannel); ok {
		if num, result := processNumMap.Load(param.ProcessChannel); result {
			connections := num.(processNum).Connections
			if connections == 0 {
				//us.l.Debug("stop ffmpeg", zap.Any("param", param.ProcessChannel))
				*ch.(*chan CtlFFMPEG_MSG) <- CtlFFMPEG_MSG{Action: STOP_MSG}
			} else {
				connections = connections - 1
				processNumMap.Store(param.ProcessChannel, processNum{Connections: connections})
			}
		}

	} else {
		fmt.Println("未找到正在播放的视频")
	}

}
func (us *VideoService) KeepAliveFFMPEG(param request.VideoProcessChannel) {

	if ch, ok := processMap.Load(param.ProcessChannel); ok {
		*ch.(*chan CtlFFMPEG_MSG) <- CtlFFMPEG_MSG{Action: KEEP_ALIVE_MSG}
	} else {
		fmt.Println("未找到正在播放的视频")
	}

}
func (us *VideoService) CaptureImage(id int) (response.UpdateImage, error) {
	var (
		imageInfo response.UpdateImage
		rtsp      string
		err       error
	)
	data, err := us.videoStore.CaptureImage(id)
	if err != nil {
		return response.UpdateImage{}, err
	}
	switch data.Type {
	case model.DeviceTypeCamera:
		rtsp = GenerateRtsp(data.Config, data.Config.VideoDevice.ChannelID)
	case model.DeviceTypeNvr:
		channelId := data.Config.VideoDevice.ChannelID
		switch data.Config.VideoDevice.Brand {
		case "海康":
			//使用主码流
			subChStr := ""
			if data.Config.VideoDevice.Stream == "main" {
				subChStr = strconv.Itoa(channelId) + "01"
			} else {
				subChStr = strconv.Itoa(channelId) + "02"
			}
			rtsp = fmt.Sprintf(`rtsp://%s:%s@%s/%s`, data.Config.VideoDevice.Name, data.Config.VideoDevice.Password, data.Config.VideoDevice.IP+":554", "Streaming/Channels/"+subChStr)
		case "大华":
			// rtsp://admin:dahua123@***********:554/cam/realmonitor?channel=2&subtype=0
			//username: 用户名。例如admin。
			//password: 密码。例如admin。
			//ip: 为设备IP。例如 **********。
			//port: 端口号默认为554，若为默认可不填写。
			//channel: 通道号，起始为1。例如通道2，则为channel=2。
			//subtype: 码流类型，主码流为0（即subtype=0），辅码流为1（即subtype=1）
			suffix := ""
			if data.Config.VideoDevice.Stream == "main" {
				suffix = fmt.Sprintf(`cam/realmonitor?channel=%d&subtype=0`, channelId)
			} else {
				suffix = fmt.Sprintf(`cam/realmonitor?channel=%d&subtype=1`, channelId)
			}
			rtsp = fmt.Sprintf(`rtsp://%s:%s@%s/%s`, data.Config.VideoDevice.Name, data.Config.VideoDevice.Password, data.Config.VideoDevice.IP+":554", suffix)
		case "华为":
			//rtsp://admin:HuaWei123@************:443/rtsp/streaming?channel=3&subtype=0&metadata=true
			suffix := ""
			if data.Config.VideoDevice.Stream == "main" {
				suffix = fmt.Sprintf(`rtsp/streaming?channel=%d&subtype=0&metadata=true`, channelId)
			} else {
				suffix = fmt.Sprintf(`rtsp/streaming?channel=%d&subtype=1&metadata=true`, channelId)
			}
			rtsp = fmt.Sprintf(`rtsp://%s:%s@%s/%s`, data.Config.VideoDevice.Name, data.Config.VideoDevice.Password, data.Config.VideoDevice.IP+":443", suffix)
		case "宇视":
			//rtsp://用户名:密码@ip:port/unicast/c<channel number>/s<stream type>/live
			//<channel number>: 1-n
			//<stream type>: 0（主流），1（辅流）
			//通道1主码流示例： rtsp://admin:admin@***********:554/unicast/c1/s0/live
			suffix := ""
			if data.Config.VideoDevice.Stream == "main" {
				suffix = "0"
			} else {
				suffix = "1"
			}
			rtsp = fmt.Sprintf(`rtsp://%s:%s@%s:554/unicast/c%d/s%s/live`, data.Config.VideoDevice.Name, data.Config.VideoDevice.Password, data.Config.VideoDevice.IP, channelId, suffix)
		}
	default:
		rtsp = " "
	}
	params := []string{
		"-i",
		rtsp,
		"-r",
		"1",
		"-ss",
		"00:00:0",
		"-t",
		"00:00:01",
		"-s", us.config.VideoResolution.Resolution,
		"-f",
		"image2",
		"-",
	}

	cmd := exec.Command("ffmpeg", params...)
	var out bytes.Buffer
	cmd.Stdout = &out
	err = cmd.Start()
	if err != nil {
		return response.UpdateImage{}, err
	}
	err = cmd.Wait()
	if err != nil {
		return response.UpdateImage{}, err
	}
	imgBase64 := base64.StdEncoding.EncodeToString(out.Bytes())
	imageInfo = response.UpdateImage{
		Image: imgBase64,
		Name:  data.Name,
	}

	return imageInfo, nil
}
func GenerateRtsp(param model.DeviceConfig, channelId int) string {
	rtsp := ""
	streamStr := ""
	subid := channelId + 1
	switch param.VideoDevice.Brand {
	case "海康":
		if param.VideoDevice.Stream == "main" {
			streamStr = "main"
		} else {
			streamStr = "sub"
		}
		rtsp = fmt.Sprintf(`rtsp://%s:%s@%s/h264/ch%d/%s/av_stream`, param.VideoDevice.Name, param.VideoDevice.Password, param.VideoDevice.IP+":554", subid, streamStr)
	case "大华":
		if param.VideoDevice.Stream == "main" {
			streamStr = "subtype=0"
		} else {
			streamStr = "subtype=1"
		}
		rtsp = fmt.Sprintf(`rtsp://%s:%s@%s/cam/realmonitor?channel=%d&%s`, param.VideoDevice.Name, param.VideoDevice.Password, param.VideoDevice.IP+":554", subid, streamStr)
	case "华为":
		if param.VideoDevice.Stream == "main" {
			streamStr = "Media1"
		} else {
			streamStr = "Media2"
		}
		rtsp = fmt.Sprintf(`rtsp://%s:%s@%s/LiveMedia/ch%d/%s`, param.VideoDevice.Name, param.VideoDevice.Password, param.VideoDevice.IP, subid, streamStr)
	case "宇视":
		//rtsp://admin:admin@***********:554/video1
		if param.VideoDevice.Stream == "main" {
			streamStr = "video1"
		} else {
			streamStr = "video2"
		}
		rtsp = fmt.Sprintf(`rtsp://%s:%s@%s:554/%s`, param.VideoDevice.Name, param.VideoDevice.Password, param.VideoDevice.IP, streamStr)
	default:
		rtsp = ""
	}
	return rtsp
}
func GeneratePreviewRtsp(param request.PreviewVideoRequest) string {
	rtsp := ""
	streamStr := ""
	subid := param.ChannelID
	switch param.Brand {
	case "海康":
		if param.Stream == "main" {
			streamStr = "main"
		} else {
			streamStr = "sub"
		}
		rtsp = fmt.Sprintf(`rtsp://%s:%s@%s/h264/ch%d/%s/av_stream`, param.Name, param.Password, param.IP+":554", subid, streamStr)
	case "大华":
		if param.Stream == "main" {
			streamStr = "subtype=0"
		} else {
			streamStr = "subtype=1"
		}
		rtsp = fmt.Sprintf(`rtsp://%s:%s@%s/cam/realmonitor?channel=%d&%s`, param.Name, param.Password, param.IP+":554", subid, streamStr)
	case "华为":
		if param.Stream == "main" {
			streamStr = "Media1"
		} else {
			streamStr = "Media2"
		}
		rtsp = fmt.Sprintf(`rtsp://%s:%s@%s/LiveMedia/ch%d/%s`, param.Name, param.Password, param.IP, subid, streamStr)
	case "宇视":
		//rtsp://admin:admin@***********:554/video1
		if param.Stream == "main" {
			streamStr = "video1"
		} else {
			streamStr = "video2"
		}
		rtsp = fmt.Sprintf(`rtsp://%s:%s@%s:554/%s`, param.Name, param.Password, param.IP, streamStr)
	default:
		rtsp = ""
	}
	return rtsp
}
func StartFFMPEG(rtsp string, param request.RealTimePlayRequest, l *zap.Logger, rtmpAddr string, videoResolution string) (*exec.Cmd, *os.Process, string, error) {
	rtmp := fmt.Sprintf("rtmp://%s:31935/stream/realtime%d%d", rtmpAddr, param.Id, param.ChannelID)
	l.Debug("rtmp addr --------", zap.Any("rtmp", rtmp))
	rtmpUrl := fmt.Sprintf("/stream/realtime%d%d", param.Id, param.ChannelID)

	args := []string{
		//"-hwaccel", "videotoolbox",
		"-re",
		"-rtsp_transport", "tcp",
		"-i", rtsp,
		"-f", "flv",
		//"-c:v", "h264_videotoolbox", libx264
		"-vcodec", "libx264", // mac 硬件解码
		//"-vprofile", "baseline",
		"-max_delay", "5000",
		"-an",
		"-strict", "experimental",
		"-r", "25",
		"-b:v", "500k",
		"-fflags", "nobuffer",
		"-f", "flv",
		"-q", "0",
		rtmp,
	}

	cmd := exec.Command("ffmpeg", args...)
	err := cmd.Start()
	l.Debug("ffmpeg  args--------", zap.Any("cmd  is:", args))
	if err != nil {
		l.Error("ERR--------", zap.Error(err))
		return nil, nil, "", errors.New("打开视频流失败")
	}
	l.Debug("start ffmpeg ----------------------")
	return cmd, cmd.Process, rtmpUrl, nil
}
func StartFFMPEGForPlayBack(rtsp string, param request.PlaybackRequest, processCh string, rtmpAddr string, videoResolution string) (*exec.Cmd, *os.Process, string, error) {

	rtmp := fmt.Sprintf("rtmp://%s:31935/stream/playback%d%d%s", rtmpAddr, param.Id, param.ChannelID, processCh)
	rtmpUrl := fmt.Sprintf("/stream/playback%d%d%s", param.Id, param.ChannelID, processCh)

	args := []string{
		//"-hwaccel", "videotoolbox",
		"-re",
		"-rtsp_transport", "tcp",
		"-i", rtsp,
		"-f", "flv",
		//"-c:v", "h264_videotoolbox", libx264
		"-vcodec", "libx264", // mac 硬件解码
		//"-vprofile", "baseline",
		"-max_delay", "5000",
		"-an",
		"-strict", "experimental",
		"-r", "25",
		"-b:v", "500k",
		"-fflags", "nobuffer",
		"-f", "flv",
		"-q", "0",
		rtmp,
	}
	cmd := exec.Command("ffmpeg", args...)
	err := cmd.Start()
	if err != nil {
		return nil, nil, "", errors.New("打开视频流失败")
	}
	fmt.Println("start ffmpeg ----------------------", processCh)
	return cmd, cmd.Process, rtmpUrl, nil
}
func StartFFMPEGPreview(rtsp string, param request.PreviewVideoRequest, rtmpAddr string, videoResolution string) (*exec.Cmd, *os.Process, string, error) {

	rtmp := fmt.Sprintf("rtmp://%s:31935/stream/realtime%d%d", rtmpAddr, param.Index, param.ChannelID)
	rtmpUrl := fmt.Sprintf("/stream/realtime%d%d", param.Index, param.ChannelID)

	args := []string{
		//"-hwaccel", "videotoolbox",
		"-re",
		"-rtsp_transport", "tcp",
		"-i", rtsp,
		"-f", "flv",
		//"-c:v", "h264_videotoolbox", libx264
		"-vcodec", "libx264", // mac 硬件解码
		//"-vprofile", "baseline",
		"-max_delay", "5000",
		"-an",
		"-strict", "experimental",
		"-r", "25",
		"-b:v", "500k",
		"-fflags", "nobuffer",
		"-f", "flv",
		"-q", "0",
		rtmp,
	}
	cmd := exec.Command("ffmpeg", args...)
	err := cmd.Start()

	if err != nil {
		return nil, nil, "", errors.New("打开视频流失败")
	}
	fmt.Println("strat ffmpeg ----------------------")
	return cmd, cmd.Process, rtmpUrl, nil
}
func keepFFMPEG(cmd *exec.Cmd, ffmpegProcess *os.Process, ch *chan CtlFFMPEG_MSG, playCh string) {
	processMap.Store(playCh, ch)
	processNumMap.Store(playCh, processNum{Connections: 0})
	//processMap.Range(func(key, value interface{}) bool {
	//	fmt.Println("key:", key, "value:", value)
	//	return true
	//})
	t := time.NewTimer(15 * time.Second)
	for {
		t.Reset(15 * time.Second)
		//fmt.Printf("-------play keep alive %s\n", playCh)
		select {
		case <-t.C:
			processMap.Delete(playCh)
			close(*ch)
			ffmpegProcess.Kill()
			fmt.Printf("ffmpeg quit %s\n", playCh)
			return
		case msg := <-*ch:
			switch msg.Action {
			case KEEP_ALIVE_MSG:
				//检查这个是否还存活
				//fmt.Println(cmd.ProcessState)
				if cmd.ProcessState != nil && !cmd.ProcessState.Exited() {
					processMap.Delete(playCh)
					close(*ch)
					ffmpegProcess.Kill()
					return
				} else {
					//fmt.Printf("ffmpeg alived\n")
				}
			case STOP_MSG:
				processMap.Delete(playCh)
				close(*ch)
				ffmpegProcess.Kill()
				fmt.Printf("-------play stop ,quit2222 %s\n", playCh)
				//退出这个ffmpeg协程
				return
			}
		}
	}
}

func (vs *VideoService) Create(param request.CreateVideoDevice) error {
	_, err := vs.deviceStore.Create(param.DeviceList, param.ConfigList, param.CreateDeviceBasic)
	return err
}

func (vs *VideoService) Detail(param request.VideoDetail) (model.DeviceWithConfig, error) {
	return vs.deviceStore.Detail(param.ID)
}
