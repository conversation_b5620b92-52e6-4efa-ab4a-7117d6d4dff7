package service

import (
	"time"
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"

	"go.uber.org/zap"
)

type CronTaskService struct {
	taskRepo *repository.CronTaskRepo
	l        *zap.Logger
}

func NewCronTaskService(taskRepo *repository.CronTaskRepo, log *zap.Logger) *CronTaskService {
	return &CronTaskService{
		taskRepo: taskRepo,
		l:        log.Named("crontask_service:"),
	}
}
func (s *CronTaskService) Create(param request.CreateCronTask) error {
	var (
		cron_task = model.CronTask{
			Name:        param.Name,
			Enabled:     param.Enabled,
			Description: param.Description,
		}
		config = model.CronTaskConfig{
			Condition: param.Condition,
			Action:    param.Action,
		}
	)
	return s.taskRepo.Create(cron_task, config)
}
func (s *CronTaskService) GetCronTaskList() ([]model.CronTask, error) {
	return s.taskRepo.GetCronTaskList()
}
func (s *CronTaskService) GetCronTaskAll() ([]model.CronTaskAll, error) {
	return s.taskRepo.GetCronTaskAll()
}
func (s *CronTaskService) GetCronTaskDetail(id int) (model.CronTaskConfig, error) {
	return s.taskRepo.GetCronTaskDetail(id)
}
func (s *CronTaskService) Delete(id int) error {
	return s.taskRepo.Delete(id)
}
func (s *CronTaskService) Update(id int, param request.CreateCronTask) error {
	var (
		cron_task = model.CronTask{
			Name:        param.Name,
			Enabled:     param.Enabled,
			Description: param.Description,
		}
		config = model.CronTaskConfig{
			Condition: param.Condition,
			Action:    param.Action,
		}
	)
	config.Condition.NextExecutionTime = time.Now()
	return s.taskRepo.Update(id, cron_task, config)
}
func (s *CronTaskService) UpdateTaskNextExecutionTime(id int, nextExecutionTime time.Time) error {
	return s.taskRepo.UpdateTaskNextExecutionTime(id, nextExecutionTime)
}
