package service

import (
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"go.uber.org/zap"
)

type EventTaskService struct {
	taskRepo *repository.EventTaskRepo
	l        *zap.Logger
}

func NewEventTaskService(taskRepo *repository.EventTaskRepo, log *zap.Logger) *EventTaskService {
	return &EventTaskService{
		taskRepo: taskRepo,
		l:        log.Named("crontask_service:"),
	}
}
func (s *EventTaskService) Create(param request.CreateEventTask) error {
	var (
		cron_task = model.EventTask{
			Name:        param.Name,
			Enabled:     param.Enabled,
			Description: param.Description,
		}
		config = model.EventTaskConfig{
			Condition: param.Condition,
			Action:    param.Action,
		}
	)
	return s.taskRepo.Create(cron_task, config)
}
func (s *EventTaskService) GetEventTaskList() ([]response.ListEventTask, error) {
	return s.taskRepo.GetEventTaskList()
}
func (s *EventTaskService) GetEventTaskAll() ([]model.EventTaskAll, error) {
	return s.taskRepo.GetEventTaskAll()
}
func (s *EventTaskService) GetEventTaskDetail(id int) (model.EventTaskConfig, error) {
	return s.taskRepo.GetEventTaskDetail(id)
}
func (s *EventTaskService) Delete(id int) error {
	return s.taskRepo.Delete(id)
}
func (s *EventTaskService) Update(id int, param request.CreateEventTask) error {
	var (
		cron_task = model.EventTask{
			Name:        param.Name,
			Enabled:     param.Enabled,
			Description: param.Description,
		}
		config = model.EventTaskConfig{
			Condition: param.Condition,
			Action:    param.Action,
		}
	)
	return s.taskRepo.Update(id, cron_task, config)
}
func (s *EventTaskService) UpdateTaskStatus(id int, has_actioned bool) error {
	return s.taskRepo.UpdateTaskStatus(id, has_actioned)
}
