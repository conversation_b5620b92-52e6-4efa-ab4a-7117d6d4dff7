package service

import (
	"sort"
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"go.uber.org/zap"
)

const (
	topMenuPID = -1
)

type MenuService struct {
	menuRepo *repository.MenuRepo
	l        *zap.Logger
}

func NewMenuService(menuRepo *repository.MenuRepo, l *zap.Logger) *MenuService {
	return &MenuService{
		menuRepo: menuRepo,
		l:        l,
	}
}

func (m *MenuService) Create(param request.CreateMenu) error {
	var menu = model.Menu{
		ParentID:  param.ParentID,
		Name:      param.Name,
		Type:      param.Type,
		Path:      param.Path,
		MatchPath: param.MatchPath,
		Component: param.Component,
		Visible:   param.Visible,
		Redirect:  param.Redirect,
		Perm:      param.Perm,
		Sort:      param.Sort,
		Icon:      param.Icon,
	}

	return m.menuRepo.Create(menu)
}

func (m *MenuService) Tree(param request.MenuTree) ([]response.MenuTree, error) {
	list, err := m.menuRepo.List(param.RID)
	if err != nil {
		return nil, err
	}

	result := m.buildTree(list, topMenuPID)

	m.sort(result)

	return result, nil
}

func (m *MenuService) buildTree(list []model.Menu, parentID int) []response.MenuTree {
	var result = make([]response.MenuTree, 0)
	for _, item := range list {
		if item.ParentID != parentID {
			continue
		}
		var temp = response.MenuTree{
			Menu: item,
		}
		temp.Children = m.buildTree(list, item.ID)
		result = append(result, temp)
	}
	return result
}

func (m *MenuService) sort(menus []response.MenuTree) {
	if len(menus) == 0 {
		return
	}
	sort.Slice(menus, func(i, j int) bool {
		return menus[i].Sort < menus[j].Sort
	})
	for _, menu := range menus {
		m.sort(menu.Children)
	}
}

func (m *MenuService) Delete(param request.DeleteMenu) error {
	return m.menuRepo.Delete(param.ID)
}

func (m *MenuService) Detail(id int) (model.Menu, error) {
	return m.menuRepo.Detail(id)
}

func (m *MenuService) Update(param request.UpdateMenu) error {
	var menu = model.Menu{
		ID: param.ID,
	}

	if param.Name != "" {
		menu.Name = param.Name
	}
	if param.ParentID != 0 {
		menu.ParentID = param.ParentID
	}
	if param.Type != 0 {
		menu.Type = param.Type
	}
	if param.Path != "" {
		menu.Path = param.Path
	}
	if param.Component != "" {
		menu.Component = param.Component
	}
	if param.Visible != 0 {
		menu.Visible = param.Visible
	}
	if param.Redirect != "" {
		menu.Redirect = param.Redirect
	}
	if param.Perm != "" {
		menu.Perm = param.Perm
	}
	if param.Sort != 0 {
		menu.Sort = param.Sort
	}
	if param.ParentID != 0 {
		menu.ParentID = param.ParentID
	}

	if param.Icon != "" {
		menu.Icon = param.Icon
	}

	if param.MatchPath != "" {
		menu.MatchPath = param.MatchPath
	}

	return m.menuRepo.Update(menu)
}

func (m *MenuService) ListByRole(id int) ([]int, error) {
	return m.menuRepo.IDs(id)
}

func (m *MenuService) ListByPID(param request.MenuListByPID) ([]model.Menu, error) {
	return m.menuRepo.ListByPID(param.PID)
}
