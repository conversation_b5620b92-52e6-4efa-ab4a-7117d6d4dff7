package service

import (
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"

	"go.uber.org/zap"
)

type VirtualVariableService struct {
	virtualRepo *repository.VirtualVariableRepo
	l           *zap.Logger
}

func NewVirtualVariableService(virtualRepo *repository.VirtualVariableRepo, log *zap.Logger) *VirtualVariableService {
	return &VirtualVariableService{
		virtualRepo: virtualRepo,
		l:           log.Named("energy_service:"),
	}
}
func (s *VirtualVariableService) Create(param model.VirtualDevice) error {
	return s.virtualRepo.Create(param)
}
func (s *VirtualVariableService) List() ([]model.VirtualDevice, error) {
	return s.virtualRepo.List()
}
func (d *VirtualVariableService) Update(param model.VirtualDevice) error {
	_, err := d.virtualRepo.Update(param.ID, param)
	return err
}
func (d *VirtualVariableService) UpdateUnit(param request.UpdateVirtualUnit) error {
	_, err := d.virtualRepo.UpdateUnit(param)
	return err
}
func (d *VirtualVariableService) Delete(id int) error {
	return d.virtualRepo.Ddelet(id)
}
func (d *VirtualVariableService) Detail(id int) (model.VirtualDevice, error) {
	return d.virtualRepo.Detail(id)
}
