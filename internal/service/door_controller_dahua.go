package service

import (
	"fmt"
	. "tw_platform/pkg/door/controller/comm"
	dh "tw_platform/pkg/door/controller/dahua"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
)

/*
大华门禁控制器
*/
func (d *DoorService) GetDoorControllerStatus_Dahua(dinfo *model.DoorInfo) (*DoorStatus, error) {
	dh := &dh.DH{
		AdminName: dinfo.ManName,
		AdminPwd:  dinfo.ManPwd,
		Host:      fmt.Sprintf("http://%s:%d", dinfo.Ip, dinfo.Port),
		SubCount:  dinfo.SubCount,
	}
	status, err := dh.GetDoorStatus()
	if err != nil {

		return nil, err
	}
	return status, nil

}
func (d *DoorService) CtrlDoorDoorController_Dahua(dinfo *model.DoorInfo, req request.CtrDoorControllerReq) error {
	dh := &dh.DH{
		AdminName: dinfo.ManName,
		AdminPwd:  dinfo.ManPwd,
		Host:      fmt.Sprintf("http://%s:%d", dinfo.Ip, dinfo.Port),
		SubCount:  dinfo.SubCount,
	}
	return dh.CtrDoor(*req.SubId, req.Way)

}
func (d *DoorService) InsertUser_Dahua(dinfo *model.DoorInfo, req request.InserUserDahuaReq) error {
	dh := &dh.DH{
		AdminName: dinfo.ManName,
		AdminPwd:  dinfo.ManPwd,
		Host:      fmt.Sprintf("http://%s:%d", dinfo.Ip, dinfo.Port),
		SubCount:  dinfo.SubCount,
	}
	return dh.InsertUser(req)
}
func (d *DoorService) AccessUser_Dahua(dinfo *model.DoorInfo) ([]dh.UserInfo, error) {
	dh := &dh.DH{
		AdminName: dinfo.ManName,
		AdminPwd:  dinfo.ManPwd,
		Host:      fmt.Sprintf("http://%s:%d", dinfo.Ip, dinfo.Port),
		SubCount:  dinfo.SubCount,
	}
	return dh.AccessUser()
}
func (d *DoorService) RemoveUser_Dahua(dinfo *model.DoorInfo, req request.GetCardDahuaReq) error {
	dh := &dh.DH{
		AdminName: dinfo.ManName,
		AdminPwd:  dinfo.ManPwd,
		Host:      fmt.Sprintf("http://%s:%d", dinfo.Ip, dinfo.Port),
		SubCount:  dinfo.SubCount,
	}
	return dh.RemoveUser(req.UserID)
}

func (d *DoorService) InsertCard_Dahua(dinfo *model.DoorInfo, req request.InserCardDahuaReq) error {
	dh := &dh.DH{
		AdminName: dinfo.ManName,
		AdminPwd:  dinfo.ManPwd,
		Host:      fmt.Sprintf("http://%s:%d", dinfo.Ip, dinfo.Port),
		SubCount:  dinfo.SubCount,
	}
	return dh.InsertCard(req)
}
func (d *DoorService) AccessCard_Dahua(dinfo *model.DoorInfo, user_id string) ([]dh.CardInfo, error) {
	dh := &dh.DH{
		AdminName: dinfo.ManName,
		AdminPwd:  dinfo.ManPwd,
		Host:      fmt.Sprintf("http://%s:%d", dinfo.Ip, dinfo.Port),
		SubCount:  dinfo.SubCount,
	}
	return dh.AccessCard(user_id)
}
func (d *DoorService) RemoveCard_Dahua(dinfo *model.DoorInfo, req request.RemoveCardDahuaReq) error {
	dh := &dh.DH{
		AdminName: dinfo.ManName,
		AdminPwd:  dinfo.ManPwd,
		Host:      fmt.Sprintf("http://%s:%d", dinfo.Ip, dinfo.Port),
		SubCount:  dinfo.SubCount,
	}
	return dh.RemoveCard(req.CardNo)
}
func (d *DoorService) GetRecord_Dahua(dinfo *model.DoorInfo, req request.GetRecordDahuaReq) (dh.RecordData, error) {
	dh := &dh.DH{
		AdminName: dinfo.ManName,
		AdminPwd:  dinfo.ManPwd,
		Host:      fmt.Sprintf("http://%s:%d", dinfo.Ip, dinfo.Port),
		SubCount:  dinfo.SubCount,
	}
	return dh.GetRecord(req.StartTime, req.EndTime)
}
