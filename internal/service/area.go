package service

import (
	"errors"
	"io"
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/utils"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type AreaService struct {
	areaRepo   *repository.AreaRepo
	deviceRepo *repository.DeviceRepo

	l *zap.Logger

	defaultPID int
	pueBTID    int
}

func NewAreaService(
	areaRepo *repository.AreaRepo,
	deviceRepo *repository.DeviceRepo,
	l *zap.Logger,
) *AreaService {
	return &AreaService{
		areaRepo:   areaRepo,
		deviceRepo: deviceRepo,
		l:          l,
		defaultPID: -1,
		pueBTID:    36,
	}
}

func (as *AreaService) Create(param request.CreateArea) error {
	var (
		relations = make([]model.AreaRelation, 0)
		err       error
	)
	if param.ParentID != as.defaultPID {
		relations, err = as.areaRepo.RelationListByChildID(param.ParentID)
		if err != nil {
			return err
		}
	}

	area := model.Area{
		Name:      param.Name,
		ParentID:  param.ParentID,
		Longitude: param.Longitude,
		Latitude:  param.Latitude,
		Status:    model.AreaStatusNormal,
	}
	return as.areaRepo.Create(area, relations)
}

func (as *AreaService) Tree(param request.AreaTreeByUserID) ([]response.AreaTree, error) {
	list, err := as.areaRepo.FindByUserIDWithTypeCounts(param.UID, param.BusinessTypeID)
	if err != nil {
		return nil, err
	}
	tree := as.treeWithTypeCounts(list, as.defaultPID)
	if len(tree) == 0 {
		return nil, nil
	}
	return tree, nil
}
func (as *AreaService) TreeOnlyByScene(param request.AreaTreeByUserIDScene) ([]response.AreaTreeOnly, error) {
	list, err := as.areaRepo.FindByUserIDByScene(param.UID, param.BusinessTypeID)
	if err != nil {
		return nil, err
	}

	tree := as.treeOnly(list, as.defaultPID)
	if len(tree) == 0 {
		return nil, nil
	}
	return tree, nil
}

func (as *AreaService) TreeOnly(param request.AreaTreeByUserID) ([]response.AreaTreeOnly, error) {
	list, err := as.areaRepo.FindByUserID(param.UID, param.BusinessTypeID)
	if err != nil {
		return nil, err
	}

	tree := as.treeOnly(list, as.defaultPID)
	if len(tree) == 0 {
		return nil, nil
	}
	return tree, nil
}

func (as *AreaService) treeWithTypeCounts(param []model.AreaWithTypes, parentID int) []response.AreaTree {
	var result = make([]response.AreaTree, 0)
	for _, v := range param {
		if v.ParentID != parentID {
			continue
		}

		var temp = response.AreaTree{
			AreaWithTypes: v,
		}
		temp.Children = as.treeWithTypeCounts(param, v.ID)
		if len(temp.AreaWithTypes.Types) == 0 && len(temp.Children) == 0 {
			continue
		}
		result = append(result, temp)
	}
	return result
}
func (as *AreaService) treeWithTypeCountsAllowEmpty(param []model.AreaWithTypes, parentID int) []response.AreaTree {
	var result = make([]response.AreaTree, 0)
	for _, v := range param {
		if v.ParentID != parentID {
			continue
		}

		var temp = response.AreaTree{
			AreaWithTypes: v,
		}
		temp.Children = as.treeWithTypeCountsAllowEmpty(param, v.ID)
		result = append(result, temp)
	}
	return result
}
func (as *AreaService) treeOnly(param []model.Area, parentID int) []response.AreaTreeOnly {
	var result = make([]response.AreaTreeOnly, 0)
	for _, v := range param {
		if v.ParentID != parentID {
			continue
		}

		var temp = response.AreaTreeOnly{
			Area: v,
		}
		temp.Children = as.treeOnly(param, v.ID)
		result = append(result, temp)
	}
	return result
}

func (as *AreaService) All() ([]response.AreaTreeOnly, error) {
	list, err := as.areaRepo.All()
	if err != nil {
		return nil, err
	}
	tree := as.treeOnly(list, as.defaultPID)
	return tree, nil
}

func (as *AreaService) AllWithTypeCounts() ([]response.AreaTree, error) {
	list, err := as.areaRepo.AllWithTypeCounts()
	if err != nil {
		return nil, err
	}
	tree := as.treeWithTypeCountsAllowEmpty(list, as.defaultPID)
	return tree, nil
}

func (as *AreaService) AllWithDevices() ([]response.AreaTreeWithDevices, error) {
	list, err := as.areaRepo.AllWithDevices()
	if err != nil {
		return nil, err
	}
	result := as.treeWithDevices(list, as.defaultPID)
	return result, nil
}

func (as *AreaService) treeWithDevices(param []model.AreaWithDevices, parentID int) []response.AreaTreeWithDevices {
	var result = make([]response.AreaTreeWithDevices, 0)
	for _, v := range param {
		if v.ParentID != parentID {
			continue
		}

		var temp = response.AreaTreeWithDevices{
			AreaWithDevices: v,
		}
		temp.Children = as.treeWithDevices(param, v.ID)
		result = append(result, temp)
	}
	return result
}

func (as *AreaService) TreeWithDeviceConfigs(param request.AreaTreeWithDeviceConfigs) ([]response.AreaTreeWithDeviceConfigs, error) {
	var (
		result []response.AreaTreeWithDeviceConfigs
	)

	list, err := as.areaRepo.ListWithDeviceConfig(param.UID, param.BusinessTypeID, param.Type)
	if err != nil {
		return result, err
	}

	result = as.treeWithDeviceConfigs(list, as.defaultPID)

	return result, nil
}
func (as *AreaService) treeWithDeviceConfigs(param []model.AreaWithDeviceConfigs, parentID int) []response.AreaTreeWithDeviceConfigs {
	var result = make([]response.AreaTreeWithDeviceConfigs, 0)
	for _, v := range param {
		if v.ParentID != parentID {
			continue
		}

		var temp = response.AreaTreeWithDeviceConfigs{
			AreaWithDeviceConfigs: v,
		}
		temp.Children = as.treeWithDeviceConfigs(param, v.ID)
		if len(temp.Devices) == 0 && len(temp.Children) == 0 {
			continue
		}
		result = append(result, temp)
	}
	return result
}

func (as *AreaService) Delete(param request.DeleteArea) error {
	_, err := as.deviceRepo.First(model.Device{AreaID: param.ID})
	if err == nil {
		return errors.New("该区域下存在设备")
	}
	_, err = as.areaRepo.First(model.Area{ParentID: param.ID})
	if err == nil {
		return errors.New("该区域下存在子区域")
	}

	scenes, err := as.areaRepo.FindSceneByAreaID(param.ID)
	if err != nil {
		return err
	}

	for _, v := range scenes {
		if v.ImgPath != nil {
			err = utils.DeleteFile(*v.ImgPath)
			if err != nil {
				as.l.Warn("delete scene img error", zap.Error(err))
			}
		}
	}

	return as.areaRepo.Delete(param.ID)
}

func (as *AreaService) CreateScene(param request.CreateScene) (int, error) {
	var scene = model.AreaScene{
		AreaID:         param.AreaID,
		BusinessTypeID: param.BusinessTypeID,
		Type:           param.Type,
		Data:           param.Data,
		ExType:         param.ExType,
	}

	if scene.ExType == 0 {
		scene.ExType = model.AreaSceneExTypeNormal
	}

	switch {
	case param.Img == nil && param.ImgPath == "":
	case param.Img != nil:

		fd, err := param.Img.Open()
		if err != nil {
			return 0, err
		}

		defer fd.Close()

		data, err := io.ReadAll(fd)
		if err != nil {
			return 0, err
		}

		path, err := utils.SaveFile(param.Img.Filename, model.StaticScene, data)
		if err != nil {
			return 0, err
		}
		scene.ImgPath = &path
	case param.ImgPath != "":
		name, err := utils.CopyFile(param.ImgPath)
		if err != nil {
			return 0, err
		}
		scene.ImgPath = &name
	}

	switch param.Type {
	case model.SceneType2D:
		btIDs, err := as.areaRepo.FindNotExistsScene(param.AreaID)
		if err != nil {
			return 0, err
		}
		var (
			scenes = make([]model.AreaScene, 0, len(btIDs))
			path   string
			hasImg bool
		)

		if scene.ImgPath != nil {
			path = model.StaticScene + *scene.ImgPath
			hasImg = true
		}

		for _, v := range btIDs {
			tmp := scene
			if hasImg {
				name, err := utils.CopyFile(path)
				if err != nil {
					return 0, err
				}
				tmp.ImgPath = &name
			}
			tmp.BusinessTypeID = v
			scenes = append(scenes, tmp)
		}

		scenes = append(scenes, scene)
		return 0, as.areaRepo.CreateSceneBatch(scenes)
	default:
		return as.areaRepo.CreateScene(scene)
	}
}

func (as *AreaService) SceneDetail(param request.SceneDetail) (model.AreaScene, error) {
	var (
		filter = model.AreaScene{
			AreaID:         param.AreaID,
			BusinessTypeID: param.BusinessTypeID,
			Type:           param.Type,
		}
	)
	scene, err := as.areaRepo.LastScene(filter)
	if err != nil && err != gorm.ErrRecordNotFound {
		return scene, err
	}
	if err == gorm.ErrRecordNotFound && param.Type == model.SceneTypeScreen {
		scene, err = as.areaRepo.LastSceneRecursive(filter)
		if err != nil && err != gorm.ErrRecordNotFound {
			return scene, err
		}
	}
	return scene, nil
}

func (as *AreaService) UpdateScene(param request.UpdateScene) (string, error) {
	var (
		scene model.AreaScene
		name  string
	)
	if param.Data != "" {
		scene.Data = param.Data
	}
	if param.ExType != 0 {
		scene.ExType = param.ExType
	}
	if param.Img == nil && param.ImgPath == "" {
		return "", as.areaRepo.UpdateScene(param.ID, scene)
	}
	old, err := as.areaRepo.LastScene(model.AreaScene{
		ID: param.ID,
	})
	if err != nil {
		return "", err
	}
	if old.ImgPath != nil {
		err = utils.DeleteFile(*old.ImgPath)
		if err != nil {
			return "", err
		}
	}

	switch {
	case param.Img != nil:
		fd, err := param.Img.Open()
		if err != nil {
			return "", err
		}
		defer fd.Close()

		data, err := io.ReadAll(fd)
		if err != nil {
			return "", err
		}
		name, err = utils.SaveFile(param.Img.Filename, model.StaticScene, data)
		if err != nil {
			return "", err
		}
		scene.ImgPath = &name
	case param.ImgPath != "":
		name, err = utils.CopyFile(param.ImgPath)
		if err != nil {
			return "", err
		}
		scene.ImgPath = &name
	}
	return name, as.areaRepo.UpdateScene(param.ID, scene)
}

func (as *AreaService) DeleteScene(param request.DeleteScene) error {
	scene, err := as.areaRepo.LastScene(model.AreaScene{
		ID: param.ID,
	})
	if err != nil {
		return err
	}
	if scene.ImgPath != nil {
		err = utils.DeleteFile(*scene.ImgPath)
		if err != nil {
			return err
		}
	}
	return as.areaRepo.DeleteScene(param.ID)
}

func (as *AreaService) Update(param request.UpdateArea) error {
	var area model.Area

	if param.Name != "" {
		area.Name = param.Name
	}
	if param.Status != 0 {
		area.Status = param.Status
	}
	if param.Longitude != "" {
		area.Longitude = param.Longitude
	}
	if param.Latitude != "" {
		area.Latitude = param.Latitude
	}

	return as.areaRepo.Update(param.ID, area)
}

func (s *AreaService) PUE(param request.GetPUE) (response.GetPUE, error) {
	var (
		result response.GetPUE
	)
	device, err := s.deviceRepo.First(model.Device{
		AreaID:         param.AreaID,
		BusinessTypeID: s.pueBTID,
	})
	if err != nil {
		return result, err
	}
	cache, err := s.deviceRepo.DataCache(device.ID)
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return result, errors.New("该区域下未配置PUE！")
		}
		return result, err
	}

	if cache.Units == nil || len(cache.Units) == 0 {
		return result, errors.New("PUE设备读取错误")
	}
	result.PUE = cache.Units[0].Value
	return result, nil
}

func (s *AreaService) ListByPID(param request.AreaListByPID) ([]model.Area, error) {
	var pid = param.ParentID
	if pid == 0 {
		pid = s.defaultPID
	}
	return s.areaRepo.ListByPID(pid, param.BusinessTypeID)
}
func (s *AreaService) ListByPIDRecursive(param request.AreaListByPID) ([]model.Area, error) {
	var pid = param.ParentID
	if pid == 0 {
		pid = s.defaultPID
	}
	return s.areaRepo.ListByPIDRecursive(pid, param.BusinessTypeID)
}

func (as *AreaService) PUEEveryHour(param request.PUEEveryHour) ([]response.PUEEveryHour, error) {
	device, err := as.deviceRepo.First(model.Device{
		AreaID:         param.AreaID,
		BusinessTypeID: as.pueBTID,
	})
	if err != nil {
		return nil, err
	}

	return as.deviceRepo.PUEEveryHour(device.ID)
}

func (as *AreaService) DefaultSceneType(param request.DefaultSceneType) error {
	return as.areaRepo.DefaultSceneType(model.AreaDefaultSceneType{
		AreaID:         param.AreaID,
		BusinessTypeID: param.BusinessTypeID,
		SceneType:      param.SceneType,
	})
}

func (as *AreaService) DefaultSceneTypeList(param request.AreaDefaultSceneType) ([]response.AreaDefaultSceneType, error) {
	return as.areaRepo.DefaultSceneTypeList(model.AreaDefaultSceneType{
		AreaID:         param.AreaID,
		BusinessTypeID: as.defaultPID,
	})
}

func (as *AreaService) TreeWithAlarmCounts(param request.AreaTreeWithAlarmCounts) ([]response.AreaTreeWithAlarmCounts, error) {
	list, err := as.areaRepo.FindByUserIDWithAlarmCounts(param.UID)
	if err != nil {
		return nil, err
	}

	return as.treeWithAlarmCounts(list, as.defaultPID), nil
}

func (as *AreaService) treeWithAlarmCounts(param []model.AreaWithAlarmCounts, parentID int) []response.AreaTreeWithAlarmCounts {
	var result = make([]response.AreaTreeWithAlarmCounts, 0)

	for _, v := range param {
		if v.ParentID != parentID {
			continue
		}

		var temp = response.AreaTreeWithAlarmCounts{
			AreaWithAlarmCounts: v,
		}
		temp.Children = as.treeWithAlarmCounts(param, v.ID)
		result = append(result, temp)
	}

	return result
}

func (as *AreaService) ListWithAlarmedByPid(param request.AreaListWithAlarmedByPid) ([]response.AreaListWithAlarmed, error) {
	if param.ParentID == 0 {
		param.ParentID = as.defaultPID
	}

	list, err := as.areaRepo.ListWithAlarmedByPID(param)
	if err != nil {
		return nil, err
	}
	var (
		results = make([]response.AreaListWithAlarmed, 0)
	)
	for i := 0; i < len(list); i++ {
		var temp = response.AreaListWithAlarmed{
			Area: list[i].Area,
		}

		if len(list[i].Alarms) != 0 {
			temp.IsAlarmed = true
		}

		results = append(results, temp)
	}
	return results, nil
}

func (as *AreaService) TreeWithDevicesByBtID(param request.AreaTreeWithDevicesByBtID) (response.AreaTreeWithDevicesByBtIDResp, error) {
	list, err := as.areaRepo.FindByUserIDWithDevices(param.UID, param.BusinessTypeID)
	var result response.AreaTreeWithDevicesByBtIDResp
	if err != nil {
		return result, err
	}

	var (
		deviceIds = make([]int, 0)
		nameMap   = make(map[int]map[int]response.DeviceUnitName, 0)
		normal    int
		alarmed   int
	)

	for _, area := range list {
		for _, device := range area.Devices {
			deviceIds = append(deviceIds, device.ID)
		}
	}

	names, err := as.deviceRepo.UnitName(deviceIds)
	if err != nil {
		return result, err
	}

	for _, name := range names {
		_, ok := nameMap[name.ID]
		if !ok {
			nameMap[name.ID] = make(map[int]response.DeviceUnitName, 0)
		}
		nameMap[name.ID][name.UnitID] = name
	}

	var results = make([]response.AreaTreeWithDevicesByBtID, 0)

	for _, area := range list {
		tmp := response.AreaTreeWithDevicesByBtID{
			ID:       area.ID,
			Name:     area.Name,
			ParentID: area.ParentID,
			Devices:  make([]response.AreaTreeWithDevicesByBtIDDevice, 0, len(area.Devices)),
		}

		for _, device := range area.Devices {
			devTmp := response.AreaTreeWithDevicesByBtIDDevice{
				ID:   device.ID,
				Name: device.Name,
				Data: make([]response.AreaTreeWithDevicesByBtIDDeviceUnit, 0),
			}
			caches, err := as.deviceRepo.DataCache(device.ID)
			if err != nil {
				continue
			}
			devTmp.Status = caches.Status

			if caches.Status == model.DeviceStatusNormal {
				normal++
			} else {
				alarmed++
			}

			name, ok := nameMap[device.ID]
			if !ok {
				continue
			}
			for _, unit := range caches.Units {
				unitInfo, ok := name[unit.Point]
				if !ok {
					continue
				}
				devTmp.Data = append(devTmp.Data, response.AreaTreeWithDevicesByBtIDDeviceUnit{
					ID:       unit.Point,
					Name:     unitInfo.Name,
					Value:    unit.Value,
					Status:   unit.Status,
					SubValue: unitInfo.SubValue,
					Flag:     unitInfo.Flag,
				})

			}
			tmp.Devices = append(tmp.Devices, devTmp)
		}
		results = append(results, tmp)
	}
	tree := as.treeWithDevicesData(results, as.defaultPID)

	return response.AreaTreeWithDevicesByBtIDResp{
		Tree:         tree,
		NormalTotal:  normal,
		AlarmedTotal: alarmed,
	}, nil
}

func (as *AreaService) treeWithDevicesData(list []response.AreaTreeWithDevicesByBtID, parentID int) []response.AreaTreeWithDevicesByBtID {
	var result = make([]response.AreaTreeWithDevicesByBtID, 0)
	for _, v := range list {
		if v.ParentID != parentID {
			continue
		}

		var tmp = response.AreaTreeWithDevicesByBtID{
			ID:      v.ID,
			Name:    v.Name,
			Devices: v.Devices,
		}
		tmp.Children = as.treeWithDevicesData(list, v.ID)
		if len(tmp.Children) == 0 && len(tmp.Devices) == 0 {
			continue
		}
		result = append(result, tmp)
	}
	return result
}

func (as *AreaService) IDs(param request.AreaIDs) ([]int, error) {
	return as.areaRepo.IDs(param.UID)
}

func (as *AreaService) RealtimeStatus(ids []int) ([]response.RealtimeAreaStatus, error) {
	var (
		results = make([]response.RealtimeAreaStatus, 0, len(ids))
		alarmed = make(map[int]struct{})
		ok      bool
	)
	list, err := as.areaRepo.Alarmed(ids)
	if err != nil {
		return results, err
	}

	for _, item := range list {
		alarmed[item.ID] = struct{}{}
	}

	for _, id := range ids {
		var status bool
		_, ok = alarmed[id]
		if ok {
			status = true
		}

		results = append(results, response.RealtimeAreaStatus{
			ID:        id,
			IsAlarmed: status,
		})
	}

	return results, nil
}

func (as *AreaService) VideoListByPidTile(param request.VideoListByPidTile) ([]response.VideoListByPidTile, error) {

	list, err := as.areaRepo.VideoListByPidTile(param.AreaID, param.UID, param.BusinessTypeID)

	as.l.Debug("AreaService", zap.Any("list", list))

	return list, err
}
