package service

import (
	"encoding/json"
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
)

type DriverService struct {
	driverRepo *repository.DriverRepo
}

func NewDriverService(driverRepo *repository.DriverRepo) *DriverService {
	return &DriverService{
		driverRepo: driverRepo,
	}
}

func (d *DriverService) Create(param request.CreateDriver) error {
	var content model.DriverContent
	err := json.Unmarshal([]byte(param.Content), &content)
	if err != nil {
		return err
	}
	driver := model.Driver{
		Agree:        param.Agree,
		Use:          param.Use,
		ManuFacturer: param.ManuFacturer,
		DevType:      param.DevType,
		Model:        param.Model,
		Name:         param.Name,
		Content:      content,
	}
	return d.driverRepo.Create(driver)
}

func (d *DriverService) List(param request.DriverList) ([]model.Driver, int64, error) {
	filter := model.Driver{
		DevType:      param.DevType,
		ManuFacturer: param.ManuFacturer,
		Use:          param.Use,
		Agree:        param.Agree,
	}
	return d.driverRepo.List(param.Page, param.Size, param.Order, filter)
}

func (d *DriverService) Detail(id int) (model.Driver, error) {
	return d.driverRepo.GetByID(id)
}

func (d *DriverService) Delete(id int) error {
	return d.driverRepo.DeleteByID(id)
}

func (d *DriverService) TypeList() ([]string, error) {
	return d.driverRepo.Group("dev_type")
}
func (d *DriverService) ManuFacturerList() ([]string, error) {
	return d.driverRepo.Group("manu_facturer")
}

func (d *DriverService) AgreeList() ([]string, error) {
	return d.driverRepo.Group("agree")
}
