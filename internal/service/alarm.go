package service

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"sort"
	"time"
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/system/standby"

	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"
)

type AlarmService struct {
	alarmRepo      *repository.AlarmRepo
	deviceRepo     *repository.DeviceRepo
	areaRepo       *repository.AreaRepo
	gatewayRepo    *repository.GatewayRepo
	alarmGroupServ *AlarmGroupService

	stats *standby.StandbyStats

	l *zap.Logger

	pushClients map[string]pushClient
}

type pushClient struct {
	addr       string
	ch         chan string
	areaIDs    map[int]struct{}
	gatewayIDs map[int]struct{}
}

func NewAlarmService(
	alarmRepo *repository.AlarmRepo,
	deviceRepo *repository.DeviceRepo,
	areaRepo *repository.AreaRepo,
	gatewayRepo *repository.GatewayRepo,
	alarmGroupServ *AlarmGroupService,
	stats *standby.StandbyStats,
	l *zap.Logger,
) *AlarmService {
	var as = &AlarmService{
		alarmRepo:      alarmRepo,
		deviceRepo:     deviceRepo,
		areaRepo:       areaRepo,
		gatewayRepo:    gatewayRepo,
		alarmGroupServ: alarmGroupServ,
		stats:          stats,

		l:           l.Named("alarm_service"),
		pushClients: make(map[string]pushClient),
	}
	go as.Subscribe()
	return as
}

func (as *AlarmService) HistoryList(param request.HistoryAlarmList) ([]model.HistoryAlarmWithTimeline, int64, error) {
	return as.alarmRepo.HistoryList(param)
}

func (as *AlarmService) RealtimeAlarmList(param request.RealtimeAlarmList) ([]response.RealtimeList, int64, error) {
	return as.alarmRepo.RealtimeAlarmList(param)
}
func (as *AlarmService) ExportHistoryList(param request.ExportHistoryAlarm) ([]model.HistoryAlarmWithTimeline, error) {
	return as.alarmRepo.ExportHistoryList(param)
}

func (as *AlarmService) Handle(param request.HandleAlarm) error {
	return as.alarmRepo.UpdateRealtimeAlarm(model.Alarm{
		ID:     param.ID,
		Status: model.AlarmStatusHandled,
	})
}

func (as *AlarmService) blockAlarm(alarm model.HistoryAlarm) error {
	//网关报警无法屏蔽
	err := as.deviceRepo.BlockAlarm(*alarm.DeviceID, *alarm.UnitID)
	if err != nil {
		return err
	}
	result := as.alarmRepo.RemoveAlarmed(*alarm.DeviceID, *alarm.UnitID)
	if result == 0 {
		//报警已恢复
		return nil
	}

	as.alarmRepo.RemoveAlarmCounts(*alarm.DeviceID, *alarm.UnitID)
	err = as.alarmRepo.SetStatusBlocked(alarm.AlarmID, alarm.ID)
	if err != nil {
		return err
	}
	return nil
}

func (as *AlarmService) SetAlarmStatus(param request.SetAlarmStatus) error {
	alarm, err := as.alarmRepo.FirstHistoryAlarm(param.ID)
	if err != nil {
		return err
	}
	switch param.Status {
	case model.AlarmStatusBlocked:
		err = as.blockAlarm(alarm)
	case model.AlarmStatusHandled:
		err = as.alarmRepo.SetStatusHandled(alarm.AlarmID, alarm.ID)
	default:
		err = errors.New("未知报警状态")
	}

	return err
}

func (as *AlarmService) Create(alarm model.Alarm, path string) error {
	return as.alarmRepo.Create(alarm, path)
}

func (as *AlarmService) CreateOutside(param request.CreateAlarmOutside) error {
	area, err := as.areaRepo.First(model.Area{ID: param.AreaID})
	if err != nil {
		return err
	}
	var alarm = model.Alarm{
		Type:       param.Type,
		Status:     model.AlarmStatusHappened,
		DeviceID:   &param.DeviceID,
		UnitID:     &param.UnitID,
		DeviceName: &param.DeviceName,
		UnitName:   &param.UnitName,
		Value:      &param.Value,
		Flag:       &param.Flag,
		AreaID:     &param.AreaID,
		AreaName:   &area.Name,
		Level:      param.Level,
		Message:    param.Message,
	}
	err = as.alarmRepo.Create(alarm, "")
	if err != nil {
		return err
	}
	err = as.alarmGroupServ.SendAlarm(
		param.DeviceID,
		param.Message,
		"alarm",
		0,
		param.DeviceName,
		param.UnitName,
		param.Level,
		"发生报警",
		param.Value,
	)
	return err
}

func (as *AlarmService) Recovery(filter model.Alarm) error {
	return as.alarmRepo.Recovery(filter)
}
func (as *AlarmService) Recovery1(filter model.Alarm, path string) error {
	return as.alarmRepo.Recovery1(filter, path)
}

func (as *AlarmService) RecoveryOutside(param request.RecoveryAlarmOutside) error {
	return as.alarmRepo.Recovery1(model.Alarm{
		DeviceID: &param.DeviceID,
		UnitID:   &param.UnitID,
		Type:     param.Type,
	}, "")
}

func (as *AlarmService) Sync(param request.SyncAlarm) error {
	if !as.stats.Enabled() || as.stats.Role() == model.StandbyTypeMaster {
		return nil
	}

	return as.alarmRepo.Create(param.Alarm, param.Path)
}

func (as *AlarmService) TotalOfTime(param request.AlarmTotalOfTime) (response.AlarmTotalOfTimeList, error) {
	data, err := as.alarmRepo.TotalOfTime(param)
	if err != nil {
		return data, err
	}
	var (
		resultsMap = make(map[string]response.AlarmTotalOfTime)
		now        = time.Now()
		i          time.Duration
		results    = make([]response.AlarmTotalOfTime, 0, 24)
		format     string
		number     time.Duration
		d          time.Duration
	)

	switch param.Type {
	case request.AlarmTotalOfDay:
		format = "2006-01-02"
		number = -30
		d = 24 * time.Hour
	case request.AlarmTotalOfHour:
		format = "2006-01-02 15"
		number = -24
		d = time.Hour
	case request.AlarmTotalOfMinute:
		format = "2006-01-02 15:04"
		number = -10
		d = time.Minute
	default:
		return results, errors.New("未知统计类型")
	}

	for _, result := range data {
		temp, err := time.Parse(time.RFC3339, result.Time)
		if err != nil {
			return results, err
		}
		result.Time = temp.Format(format)
		resultsMap[result.Time] = result
	}

	for i = 0; i > number; i-- {
		temp := now.Add(i * d).Format(format)

		_, ok := resultsMap[temp]
		if ok {
			continue
		}
		resultsMap[temp] = response.AlarmTotalOfTime{
			Time: temp,
		}
	}

	for _, result := range resultsMap {
		results = append(results, result)
	}

	sort.Sort(response.AlarmTotalOfTimeList(results))

	return results, nil
}

func (as *AlarmService) RealTimePush(param request.SystemStatistic) ([]response.RealtimeList, error) {
	return as.alarmRepo.RealtimeAlarmPush(param)
}

func (as *AlarmService) RealtimeListWS(param request.RealtimeAlarmList) ([]response.RealtimeList, error) {
	return as.alarmRepo.RealtimeListWS(param)
}

var (
	statusMap = map[model.AlarmStatus]string{
		model.AlarmStatusHappened:  "已发生",
		model.AlarmStatusRecovered: "已恢复",
		model.AlarmStatusHandled:   "已处理",
		model.AlarmStatusBlocked:   "已屏蔽",
	}
)

func (as *AlarmService) Generate() error {
	var (
		now   = time.Now()
		begin = now.AddDate(0, -1, 0).Format(time.DateOnly)
		end   = now.AddDate(0, 0, -1).Format(time.DateOnly)
	)
	list, err := as.alarmRepo.FilterByDate(begin, end)

	fd := excelize.NewFile()
	defer fd.Close()

	device, err := fd.NewSheet("设备")
	if err != nil {
		return err
	}
	gateway, err := fd.NewSheet("网关")
	if err != nil {
		return err
	}
	_, _ = device, gateway

	err = fd.DeleteSheet("Sheet1")
	if err != nil {
		return err
	}
	fd.SetCellValue("设备", "A1", "区域名称")
	fd.SetCellValue("设备", "B1", "报警等级")
	fd.SetCellValue("设备", "C1", "设备名")
	fd.SetCellValue("设备", "D1", "测点名")
	fd.SetCellValue("设备", "E1", "报警值")
	fd.SetCellValue("设备", "F1", "单位")
	fd.SetCellValue("设备", "G1", "当前状态")
	fd.SetCellValue("设备", "H1", "报警消息")
	fd.SetCellValue("设备", "I1", "报警时间")
	fd.SetCellValue("设备", "J1", "处理时间")
	fd.SetCellValue("设备", "K1", "恢复时间")

	fd.SetCellValue("网关", "A1", "名称")
	fd.SetCellValue("网关", "B1", "报警等级")
	fd.SetCellValue("网关", "C1", "当前状态")
	fd.SetCellValue("网关", "D1", "消息")
	fd.SetCellValue("网关", "E1", "报警时间")
	fd.SetCellValue("网关", "F1", "处理时间")
	fd.SetCellValue("网关", "G1", "恢复时间")

	fd.SetColWidth("设备", "A", "A", 40)
	fd.SetColWidth("设备", "C", "C", 20)
	fd.SetColWidth("设备", "D", "D", 15)
	fd.SetColWidth("设备", "H", "H", 173)
	fd.SetColWidth("设备", "I", "K", 18)

	fd.SetColWidth("网关", "A", "A", 20)
	fd.SetColWidth("网关", "D", "D", 173)
	fd.SetColWidth("网关", "E", "G", 18)

	style, err := fd.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "center",
		},
	})
	if err != nil {
		return err
	}

	fd.SetColStyle("设备", "A:K", style)
	fd.SetColStyle("网关", "A:G", style)

	deviceIdx := 0
	gatewayIdx := 0

	for _, v := range list {
		switch v.Type {
		case model.AlarmTypeCommon:
			deviceIdx++
			row := deviceIdx + 1
			fd.SetCellValue("设备", fmt.Sprintf("A%d", row), *v.AreaName)
			fd.SetCellValue("设备", fmt.Sprintf("B%d", row), v.Level)
			fd.SetCellValue("设备", fmt.Sprintf("C%d", row), *v.DeviceName)
			fd.SetCellValue("设备", fmt.Sprintf("D%d", row), *v.UnitName)
			fd.SetCellValue("设备", fmt.Sprintf("E%d", row), *v.Value)
			fd.SetCellValue("设备", fmt.Sprintf("F%d", row), *v.Flag)
			fd.SetCellValue("设备", fmt.Sprintf("G%d", row), statusMap[v.Status])
			fd.SetCellValue("设备", fmt.Sprintf("H%d", row), v.Message)
			for _, timelines := range v.Timelines {
				switch timelines.Status {
				case model.AlarmStatusHappened:
					fd.SetCellValue("设备", fmt.Sprintf("I%d", row), timelines.CreatedAt.Format(time.DateTime))
				case model.AlarmStatusHandled:
					fd.SetCellValue("设备", fmt.Sprintf("J%d", row), timelines.CreatedAt.Format(time.DateTime))
				case model.AlarmStatusRecovered:
					fd.SetCellValue("设备", fmt.Sprintf("K%d", row), timelines.CreatedAt.Format(time.DateTime))
				}
			}

		case model.AlarmTypeGateway:
			gatewayIdx++
			row := gatewayIdx + 1
			fd.SetCellValue("网关", fmt.Sprintf("A%d", row), *v.GatewayName)
			fd.SetCellValue("网关", fmt.Sprintf("B%d", row), v.Level)
			fd.SetCellValue("网关", fmt.Sprintf("C%d", row), statusMap[v.Status])
			fd.SetCellValue("网关", fmt.Sprintf("D%d", row), v.Message)
			for _, timelines := range v.Timelines {
				switch timelines.Status {
				case model.AlarmStatusHappened:
					fd.SetCellValue("网关", fmt.Sprintf("E%d", row), timelines.CreatedAt.Format(time.DateTime))
				case model.AlarmStatusHandled:
					fd.SetCellValue("网关", fmt.Sprintf("F%d", row), timelines.CreatedAt.Format(time.DateTime))
				case model.AlarmStatusRecovered:
					fd.SetCellValue("网关", fmt.Sprintf("G%d", row), timelines.CreatedAt.Format(time.DateTime))
				}
			}
		default:
			return errors.New("未知报警类型")
		}
	}

	var (
		month    = now.AddDate(0, -1, 0).Format("2006-01")
		fileName = fmt.Sprintf("%s.xlsx", month)
	)
	err = fd.SaveAs(fmt.Sprintf("%s%s", model.StaticExportAlarm, fileName))
	if err != nil {
		return err
	}

	err = as.alarmRepo.ExportCreate(model.ExportAlarm{
		Path:  fileName,
		Month: month,
	})
	if err != nil {
		return err
	}

	return err
}

func (as *AlarmService) ExportList(param request.PageInfo) ([]model.ExportAlarm, int64, error) {
	return as.alarmRepo.ExportList(param)
}

func (as *AlarmService) Publish(param response.AlarmListPayload) error {
	return as.alarmRepo.Publish(param)
}

func (as *AlarmService) Subscribe() {
	var (
		data response.AlarmListPayload
		err  error
	)
	for {
		data, err = as.alarmRepo.Subscribe()
		if err != nil {
			as.l.Warn("subscribe alarm failure", zap.Error(err))
			continue
		}

		if data.Alarm.Message != "" {
			err = as.pushToClient(data.Alarm)
			if err != nil {
				as.l.Warn("push to client failure", zap.Error(err))
			}
		}

		if !as.stats.Enabled() || as.stats.Role() != model.StandbyTypeMaster {
			continue
		}
		err = as.Push(data)
		if err != nil {
			as.l.Warn("publish alarm failure", zap.Error(err))
			continue
		}
	}
}

func (as *AlarmService) ApplyPublish(param response.AlarmListPayload) error {
	var (
		err error
	)
	switch param.Type {
	case model.AlarmStatusHappened:
		err = as.alarmRepo.Create(param.Alarm, param.Path)
	case model.AlarmStatusRecovered:
		err = as.alarmRepo.Recovery1(param.Alarm, param.Path)
	default:
		return errors.New("unknow alarm type")
	}

	return err
}

func (as *AlarmService) Push(param response.AlarmListPayload) error {
	var (
		client = http.Client{
			Timeout: 5 * time.Second,
		}
	)
	data, err := json.Marshal(param)
	if err != nil {
		return err
	}
	reader := bytes.NewReader(data)
	req, err := http.NewRequest(
		http.MethodPost,
		fmt.Sprintf("%s/api/v1/alarm/apply_publish", as.stats.Host()),
		reader,
	)
	if err != nil {
		return err
	}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	var respT struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	}
	err = json.NewDecoder(resp.Body).Decode(&respT)
	if err != nil {
		return err
	}
	if respT.Code != 1 {
		return errors.New(respT.Msg)
	}
	return nil
}

func (as *AlarmService) AddPushClient(uid int, addr string) (chan string, error) {
	var (
		areaIDmap    = make(map[int]struct{})
		gatewayIDmap = make(map[int]struct{})
	)
	aids, err := as.areaRepo.IDs(uid)
	if err != nil {
		return nil, err
	}
	for _, id := range aids {
		areaIDmap[id] = struct{}{}
	}

	gids, err := as.gatewayRepo.IDsByUID(uid)
	if err != nil {
		return nil, err
	}

	for _, id := range gids {
		gatewayIDmap[id] = struct{}{}
	}
	var ps = pushClient{
		addr:       addr,
		ch:         make(chan string, 0),
		areaIDs:    areaIDmap,
		gatewayIDs: gatewayIDmap,
	}

	as.pushClients[addr] = ps
	return ps.ch, nil
}

func (as *AlarmService) DeletePushClient(addr string) {
	client, ok := as.pushClients[addr]
	if !ok {
		return
	}
	close(client.ch)
	delete(as.pushClients, addr)
}

func (as *AlarmService) pushToClient(alarm model.Alarm) error {
	var (
		ok bool
	)
	for _, client := range as.pushClients {
		switch {
		case alarm.AreaID != nil && *alarm.AreaID != 0:
			_, ok = client.areaIDs[*alarm.AreaID]
			if !ok {
				continue
			}
		case alarm.GatewayID != nil && *alarm.GatewayID != 0:
			_, ok = client.gatewayIDs[*alarm.GatewayID]
			if !ok {
				continue
			}
		default:
			return errors.New("unknow alarm type")
		}
		client.ch <- alarm.Message
	}
	return nil
}

func (as *AlarmService) Remove(param request.AlarmRemove) error {
	alarm, err := as.alarmRepo.FirstRealtimeAlarm(model.Alarm{
		ID: param.ID,
	})
	if err != nil {
		return err
	}
	if alarm.Status != model.AlarmStatusHandled {
		return errors.New("报警未处理，无法移除！")
	}
	var (
		path   string
		filter model.Alarm
	)

	switch {
	case alarm.Type == model.AlarmTypeCommon && alarm.DeviceID != nil && *alarm.DeviceID != 0:
		conf, err := as.deviceRepo.Config(model.Config{
			ID: *alarm.DeviceID,
		})
		if err != nil {
			return err
		}
		path, err = conf.GetConfigPath()
		if err != nil {
			return err
		}
		filter.DeviceID = alarm.DeviceID
		filter.UnitID = alarm.UnitID
	case alarm.GatewayID != nil && *alarm.GatewayID != 0:
		filter.GatewayID = alarm.GatewayID
	// 仓储报警恢复单独处理
	case alarm.Type == model.AlarmTypeAsset:
		return as.alarmRepo.RecoveryAsset(param.ID)
	}

	err = as.Recovery1(filter, path)
	return err
}
