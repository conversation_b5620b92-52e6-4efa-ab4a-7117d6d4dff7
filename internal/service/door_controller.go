package service

import (
	"errors"
	"fmt"
	. "tw_platform/pkg/door/controller/comm"
	hk "tw_platform/pkg/door/controller/haikang"
	zk "tw_platform/pkg/door/controller/zkt"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
)

/*
门禁控制器
*/
type Door_Controller interface {
	//控门
	CtrDoor(doorid int, way string) error
	//获取门的状态
	GetDoorStatus() (*DoorStatus, error)
	//获取历史记录
	GetDoorcontrollerRecord(startTime, endTime int64) (*HisRecord, error)
	//获取卡片列表
	GetCardList() (*DoorList, error)
	//添加卡
	AddCard(cardNo string, doorids []int) error
	//删除卡
	DelCard(cardNo string) error
}

func getControIns(door_info *model.DoorInfo) Door_Controller {
	cfg := door_info
	var ins Door_Controller
	switch cfg.ManuFacturer {
	case "中控":
		zk := zk.Connect(cfg.Ip, cfg.Port, cfg.SubCount)
		if zk == nil {
			return nil
		}
		zk.DoorNum = cfg.SubCount
		ins = zk
	case "海康":
		hk := hk.Connect(cfg.Ip, cfg.ManName, cfg.ManPwd, cfg.SubCount)
		if hk == nil {
			return nil
		}
		hk.DoorNum = cfg.SubCount
		ins = hk
	default:
		return nil
	}
	if ins == nil {
		return nil
	}
	return ins
}
func (d *DoorService) GetDoorControllerStatus(dinfo *model.DoorInfo) (*DoorStatus, error) {
	if dinfo.Type != "控制器" {
		return nil, errors.New("这不是控制器")
	}
	ctlIns := getControIns(dinfo)
	if ctlIns == nil {
		return nil, errors.New("初始化控制器失败")
	}
	status, err := ctlIns.GetDoorStatus()
	if err != nil {

		return nil, err
	}
	return status, nil

}
func (d *DoorService) CtrlDoorDoorController(dinfo *model.DoorInfo, req request.CtrDoorControllerReq) error {
	if dinfo.Type != "控制器" {
		return errors.New("这不是控制器")
	}
	ctlIns := getControIns(dinfo)
	if ctlIns == nil {
		return errors.New("初始化控制器失败")
	}
	err := ctlIns.CtrDoor(*req.SubId, req.Way)
	if err != nil {

		return err
	}
	return nil

}
func (d *DoorService) GetDoorControllerCard(dinfo *model.DoorInfo) (*DoorList, error) {
	if dinfo.Type != "控制器" {
		return nil, errors.New("这不是控制器")
	}
	ctlIns := getControIns(dinfo)
	if ctlIns == nil {
		return nil, errors.New("初始化控制器失败")
	}
	list, err := ctlIns.GetCardList()
	if err != nil {

		return nil, err
	}
	return list, nil

}
func (d *DoorService) AddDoorControllerCard(dinfo *model.DoorInfo, cardNo string, doorids []int) error {
	if dinfo.Type != "控制器" {
		return errors.New("这不是控制器")
	}
	ctlIns := getControIns(dinfo)
	if ctlIns == nil {
		return errors.New("初始化控制器失败")
	}
	err := ctlIns.AddCard(cardNo, doorids)
	if err != nil {

		return err
	}
	return nil

}
func (d *DoorService) DelDoorControllerCard(dinfo *model.DoorInfo, cardNo string) error {
	if dinfo.Type != "控制器" {
		return errors.New("这不是控制器")
	}
	ctlIns := getControIns(dinfo)
	if ctlIns == nil {
		return errors.New("初始化控制器失败")
	}
	err := ctlIns.DelCard(cardNo)
	if err != nil {

		return err
	}
	return nil

}
func (d *DoorService) GetDoorControllerRecord(dinfo *model.DoorInfo, startTime, endTime int64) (*HisRecord, error) {
	if dinfo.Type != "控制器" {
		return nil, errors.New("这不是控制器")
	}
	ctlIns := getControIns(dinfo)
	if ctlIns == nil {
		return nil, errors.New("初始化控制器失败")
	}
	status, err := ctlIns.GetDoorcontrollerRecord(startTime, endTime)
	if err != nil {

		return nil, err
	}
	return status, nil

}

type SyncRet struct {
	Percentage int    `json:"percentage"`
	Info       string `json:"info"`
}
type ErrSync struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Desc string `json:"desc"`
}

var g_syncRet SyncRet
var g_syncSucc bool
var g_syncErr ErrSync

func (d *DoorService) DoorControllerdoSync(req *request.SyncReq) error {
	g_syncRet.Percentage = 0
	g_syncRet.Info = "开始同步信息"
	g_syncSucc = true

	totalStep := len(req.CardList) * len(req.Id)
	nowStep := 0
	for _, id := range req.Id {
		dinfo, err := d.GetDoorInfo(id)
		if err != nil {
			g_syncSucc = false
			g_syncErr.Code = 10003
			g_syncErr.Msg = err.Error()
			g_syncErr.Desc = fmt.Sprintf("id:%d", id)
			return err
		}
		if dinfo.Type != CONTROLLER_TYPE {
			g_syncSucc = false
			g_syncErr.Code = 10003
			g_syncErr.Msg = "待下发设备中类型不是门控器"
			fmt.Printf("%s\n", g_syncErr.Msg)
			g_syncErr.Desc = fmt.Sprintf("id:%d", id)
			return errors.New("这不是控制器")
		}
		ctlIns := getControIns(dinfo)
		if ctlIns == nil {
			g_syncSucc = false
			g_syncErr.Code = 10003
			g_syncErr.Msg = "初始化门控器错误"
			fmt.Printf("%s\n", g_syncErr.Msg)
			g_syncErr.Desc = fmt.Sprintf("id:%d", id)
			return errors.New("初始化门控器错误")
		}
		for index := range req.CardList {
			cardNo := req.CardList[index].CardNo
			if err := ctlIns.AddCard(cardNo, req.CardList[index].Door); err != nil {
				g_syncSucc = false
				g_syncErr.Code = 10003
				g_syncErr.Msg = "添加卡错误"
				fmt.Printf("%s\n", g_syncErr.Msg)
				g_syncErr.Desc = fmt.Sprintf("卡号:%s添加至%d错误", cardNo, id)
				return fmt.Errorf("卡号:%s添加至%d错误", cardNo, id)
			} else {
				nowStep = nowStep + 1
				g_syncRet.Percentage = 100 * nowStep / totalStep
				g_syncRet.Info = fmt.Sprintf("添加卡号%s至%s成功", cardNo, dinfo.Name)
			}
		}
	}
	g_syncRet.Percentage = 100
	g_syncRet.Info = "成功"
	return nil
}
func (d *DoorService) GetDoorControllerSyncResult() (bool, SyncRet, ErrSync) {
	if g_syncSucc {
		return true, g_syncRet, g_syncErr
	} else {
		return false, g_syncRet, g_syncErr
	}
}
