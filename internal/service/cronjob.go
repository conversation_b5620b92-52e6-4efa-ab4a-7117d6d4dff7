package service

import (
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/system/crontab"

	"go.uber.org/zap"
	"gorm.io/datatypes"
)

type CronjobService struct {
	cronjobRepo *repository.CronjobRepo
	c           *crontab.Crontab
	l           *zap.Logger
}

func NewCronjobService(
	cr *repository.CronjobRepo,
	c *crontab.Crontab,
	log *zap.Logger,
) *CronjobService {
	return &CronjobService{
		cronjobRepo: cr,
		c:           c,
		l:           log.Named("cronjob_service:"),
	}
}

func (cs *CronjobService) Create(param request.CreateCronjob) error {
	var job = model.CronJob{
		Name:    param.Name,
		Comment: param.Comment,
		Cron:    param.Cron,
		Type:    param.Type,
		Topic:   param.Topic,
		Data:    datatypes.JSON(param.Data),
		Begin:   &param.Begin,
		End:     &param.End,
	}
	jobID, err := cs.c.CronWithSeconds.AddJob(job.Cron, job)
	if err != nil {
		return err
	}
	job.JobID = jobID

	return cs.cronjobRepo.Create(job)

}

func (cs *CronjobService) Update(param request.UpdateCronjob) error {
	var job = model.CronJob{
		ID:      param.ID,
		Name:    param.Name,
		Comment: param.Comment,
		Cron:    param.Cron,
		Type:    param.Type,
		Topic:   param.Topic,
		Status:  param.Status,
		Data:    datatypes.JSON(param.Data),
		Begin:   &param.Begin,
		End:     &param.End,
	}

	err := cs.cronjobRepo.Update(job)
	if err != nil {
		return err
	}
	info, err := cs.cronjobRepo.First(model.CronJob{
		ID: param.ID,
	})
	if err != nil {
		return err
	}

	cs.c.CronWithSeconds.Remove(info.JobID)
	id, err := cs.c.CronWithSeconds.AddJob(param.Cron, info)
	if err != nil {
		return err
	}

	return cs.cronjobRepo.Update(model.CronJob{
		ID:    param.ID,
		JobID: id,
	})
}

func (cs *CronjobService) List(param request.CronjobList) ([]model.CronJob, int64, error) {
	return cs.cronjobRepo.List(param.Page, param.Size, model.CronJob{})
}

func (cs *CronjobService) Delete(param request.CronjobDelete) error {
	info, err := cs.cronjobRepo.First(model.CronJob{
		ID: param.ID,
	})
	if err != nil {
		return err
	}
	cs.c.CronWithSeconds.Remove(info.JobID)
	return cs.cronjobRepo.Delete(param.ID)
}
