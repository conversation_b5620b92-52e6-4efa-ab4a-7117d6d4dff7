package service

import (
	"errors"
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/system/role_permission"
)

type RoleService struct {
	roleRepo *repository.RoleRepo
	userRepo *repository.UserRepo
	rp       *role_permission.RolePermissions
}

func NewRoleService(
	roleRepo *repository.RoleRepo,
	userRepo *repository.UserRepo,
	rp *role_permission.RolePermissions,
) *RoleService {
	return &RoleService{
		roleRepo: roleRepo,
		userRepo: userRepo,
		rp:       rp,
	}
}

func (s *RoleService) Create(param request.CreateRole) error {
	var role = model.Role{
		ID:   param.ID,
		Name: param.Name,
	}
	return s.roleRepo.Create(role)
}

func (s *RoleService) List(param request.RoleList) ([]model.Role, int64, error) {
	return s.roleRepo.ListByPage(param.Page, param.Size, model.Role{})
}

func (s *RoleService) Update(param request.UpdateRole) error {
	var role model.Role
	if param.NewID != 0 {
		role.ID = param.NewID
	}

	if param.Name != "" {
		role.Name = param.Name
	}

	return s.roleRepo.Update(param.ID, role)
}

func (s *RoleService) Delete(param request.DeleteRole) error {
	_, err := s.userRepo.First(model.User{RoleID: param.ID})
	if err == nil {
		return errors.New("该角色下存在用户")
	}
	return s.roleRepo.Delete(param.ID)
}

func (s *RoleService) SetMenus(param request.SetmenusToRole) error {
	var (
		rms = make([]model.RoleMenu, 0, len(param.IDs))
	)
	for _, id := range param.IDs {
		rms = append(rms, model.RoleMenu{
			RoleID: param.RoleID,
			MenuID: id,
		})
	}
	return s.roleRepo.SetMenus(param.RoleID, rms)
}

func (s *RoleService) GetMenus(param request.GetMenusByRole) ([]int, error) {
	if param.RoleID == 0 {
		param.RoleID = param.RID
	}
	ids, err := s.roleRepo.GetMenus(param.RoleID)
	return ids, err
}

func (s *RoleService) AllocableList(param request.AllocableList) ([]model.Role, error) {
	if param.RoleID == 0 {
		param.RoleID = param.RID
	}
	return s.roleRepo.AllocableList(param.RoleID)
}

func (s *RoleService) Permissions(param request.GetPermissionsByRole) ([]int, error) {
	if param.RoleID == 0 {
		param.RoleID = param.RID
	}
	return s.roleRepo.Permissions(param.RoleID)
}

func (s *RoleService) SetPermissions(param request.SetPermissionsToRole) error {
	err := s.roleRepo.SetPermissions(param.RoleID, param.IDs)
	if err != nil {
		return err
	}
	return s.rp.Replace(param.RoleID, param.IDs)
}
