package service

import (
	"errors"
	"time"
	"tw_platform/pkg/system/config"

	"github.com/dgrijalva/jwt-go"
)

type Token struct {
	ID        int   `json:"id"`
	RoleID    int   `json:"role_id"`
	ExpiredAT int64 `json:"expired_at"`
}

func (t Token) Valid() error {
	if t.ExpiredAT < time.Now().Unix() {
		return errors.New("token expired")
	}

	return nil
}

func NewToken(uid, roleID int, conf config.JWT) (string, error) {
	t := Token{
		ID:        uid,
		RoleID:    roleID,
		ExpiredAT: time.Now().AddDate(0, 0, conf.ExpiredTime).Unix(),
	}

	return jwt.NewWithClaims(jwt.SigningMethodHS256, t).
		SignedString([]byte(conf.SecretKey))
}
