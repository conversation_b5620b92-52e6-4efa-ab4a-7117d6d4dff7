package service

import (
	"fmt"
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/utils"

	"go.uber.org/zap"
)

type AlarmGroupService struct {
	groupRepo                *repository.AlarmGroupRepo
	notificationGroupService *NotificationGroupService
	gatewayRepo              *repository.GatewayRepo
	l                        *zap.Logger
}

func NewAlarmGroupService(groupRepo *repository.AlarmGroupRepo, notificationGroupService *NotificationGroupService, gatewayRepo *repository.GatewayRepo, log *zap.Logger) *AlarmGroupService {
	return &AlarmGroupService{
		groupRepo:                groupRepo,
		notificationGroupService: notificationGroupService,
		gatewayRepo:              gatewayRepo,
		l:                        log.Named("alarm_group_service:"),
	}
}
func (s *AlarmGroupService) Create(param request.CreateAlarmGroup) error {
	var (
		group = model.AlarmGroup{
			Name:        param.Name,
			Enabled:     param.Enabled,
			Description: param.Description,
			Config:      param.Config,
		}
	)
	return s.groupRepo.Create(group, param.Devices, param.NotificationGroups)
}
func (s *AlarmGroupService) List() ([]model.AlarmGroupDetail, error) {
	return s.groupRepo.List()
}
func (d *AlarmGroupService) Delete(param request.DeleteAlarmGroup) error {
	return d.groupRepo.Delete(param.ID)
}
func (s *AlarmGroupService) Update(param request.UpdateAlarmGroup) error {
	var (
		group = model.AlarmGroup{
			ID:          param.ID,
			Name:        param.Name,
			Description: param.Description,
			Enabled:     param.Enabled,
			Config:      param.Config,
		}
	)
	return s.groupRepo.Update(group, param.Devices, param.NotificationGroups)
}

// alarm_type alarm 报警 recovery恢复 保留其他方式
func (s *AlarmGroupService) SendAlarm(devid int, msg string, alarm_type string, priority int,
	device_name, unit_name, level, message, value string,
) error {
	groupList, err := s.List()
	if err != nil {
		return err
	}
	for _, group := range groupList {
		if group.Config.Priority < priority {
			continue
		}
		if len(group.Config.AlarmType) > 0 {
			if !utils.Contains(group.Config.AlarmType, alarm_type) {
				continue
			}
		}

		for _, device := range group.Devices {
			if device.DeviceId != devid {
				continue
			}
			for _, notification := range group.Notifications {
				s.notificationGroupService.ExecuteNotification(notification.NotificationGroupId,
					"设备报警",
					msg,
					device_name,
					unit_name,
					level,
					message,
					value,
				)
			}
		}
	}
	return nil
}
func (s *AlarmGroupService) SendGateWayAlarm(gateway_id int, status uint8,

) error {
	//todo 有点绕 考虑网关加入报警组?
	deviceslist, err := s.gatewayRepo.ListWithDevices(model.Gateway{Type: model.GatewayTypeModbus})
	if err != nil {
		return err
	}
	groupList, err := s.List()
	if err != nil {
		return err
	}
	var (
		msg string
	)
	switch status {
	case model.GatewayStatusOnline:
		msg = "报警恢复:"
	case model.GatewayStatusOffline:
		msg = "报警产生:"
	}
	uniqueMap := make(map[int]struct{})
	for _, gatewaydevices := range deviceslist {
		if gatewaydevices.ID != gateway_id {
			continue
		}
		msg = msg + fmt.Sprintf("网关[%s]IP[%s]离线", gatewaydevices.Name, *gatewaydevices.IP)
		for _, port := range gatewaydevices.Ports {
			if port.Port != "" && len(port.Devices) > 0 {
				for _, devicetemp := range port.Devices {
					uniqueMap[devicetemp.ID] = struct{}{}
				}
			}
		}
	}
	//todo 网关离线修改
	for _, group := range groupList {
		for _, device := range group.Devices {
			if _, exists := uniqueMap[device.DeviceId]; !exists {
				continue
			}
			for _, notification := range group.Notifications {
				s.notificationGroupService.ExecuteNotification(notification.NotificationGroupId,
					"设备报警",
					msg,
					"网关离线",
					"网关离线",
					"网关离线",
					"网关离线",
					"网关离线",
				)
			}
			break
		}
	}
	return nil
}
