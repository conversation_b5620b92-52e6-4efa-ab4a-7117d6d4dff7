package service

import (
	"fmt"
	"os/exec"
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/golang-module/carbon/v2"
	"go.uber.org/zap"
)

const (
	VIDEO_CAPTURE = "capture"
	VIDEO_RECORD  = "video"
)

type LinkageService struct {
	linkageStore    *repository.LinkageRepo
	l               *zap.Logger
	linkCaptureRepo *repository.LinkAgeCaptureRepo
}

func NewLinkageService(linkageStore *repository.LinkageRepo, log *zap.Logger, linkCaptureRepo *repository.LinkAgeCaptureRepo) *LinkageService {
	return &LinkageService{
		linkageStore:    linkageStore,
		linkCaptureRepo: linkCaptureRepo,
		l:               log.Named("linkage_service:"),
	}
}
func (l *LinkageService) GetLinkageList() ([]model.Linkage, error) {
	list, err := l.linkageStore.GetLinkageList()
	if err != nil {
		return list, err
	}
	return list, err
}
func (l *LinkageService) GetLinkageName(id int) (response.LinkageName, error) {
	name, err := l.linkageStore.GetLinkageName(id)
	if err != nil {
		return name, err
	}
	return name, err
}
func (l *LinkageService) GetLinkageDetail(id int) (model.LinkageData, error) {
	detail, err := l.linkageStore.GetLinkageDetail(id)
	if err != nil {
		return detail, err
	}
	return detail, err
}
func (l *LinkageService) CreateLinkage(param request.CreateLinkage) (int, error) {
	var (
		linkage     model.Linkage
		linkageData model.LinkageData
	)
	linkage.Name = param.Name
	linkage.Description = param.Description
	id, err := l.linkageStore.CreateLinkage(linkage, linkageData)

	if err != nil {
		return 0, err
	}
	return id, err
}

func (l *LinkageService) DeleteLinkage(id int) error {
	return l.linkageStore.DeleteLinkage(id)
}
func (l *LinkageService) UpdateLinkageInfo(param request.UpdateLinkageInfo) error {
	var info model.Linkage
	info.Name = param.Name
	info.Description = param.Description

	_, err := l.linkageStore.UpdateLinkageInfo(param.ID, info)
	return err
}
func (l *LinkageService) UpdateLinkageData(param request.UpdateLinkageData) error {
	var data model.LinkageData
	if param.TimeInfo != nil {
		data.TimeInfo = param.TimeInfo
	}
	if param.Script != "" {
		data.Script = param.Script
	}
	if param.RawData != "" {
		data.RawData = param.RawData
	}
	_, err := l.linkageStore.UpdateLinkageData(param.ID, data)
	return err
}

func (l *LinkageService) LinkageVideoAction(cameraName string, actionType string, actionValue,
	device_id int, device_name string,
	info model.DeviceConfig, channelId int) {
	var (
		capture model.LinkageCapture
	)
	rtsp := ""
	params := []string{}

	rtsp = GenerateRtsp(info, channelId)
	if rtsp == "" {
		return
	}

	// ./ffmpeg.exe -i rtsp://admin:tw123456@***********:554/h265/ch1/main/av_stream -r 1 -ss 00:00:00 -t 00:00:01  -f image2 image.jpg

	if actionType == VIDEO_CAPTURE {
		//抓拍
		//repcnt, err := strconv.Atoi(actionValue)
		//if err != nil {
		//	return
		//}
		for i := 0; i < actionValue; i++ {
			timeStr := carbon.Now().Format("YmdHis")
			fmt.Println(cameraName)
			fileName := fmt.Sprintf("%slinkage_record_%s.jpg", model.StaticLinkage, timeStr)
			params = []string{
				"-i",
				rtsp,
				"-r",
				"1",
				"-ss",
				"00:00:00",
				"-t",
				"00:00:01",
				"-f",
				"image2",
				fileName,
			}
			cmd := exec.Command("ffmpeg", params...)
			err := cmd.Start()
			if err != nil {
				fmt.Println(err)
				return
			}
			err = cmd.Wait()
			if err != nil {
				str := err.Error()
				println(str)
			}
			capture.Image = fileName
			capture.Description = cameraName
			capture.DeviceID = device_id
			capture.DeviceName = device_name
			err = l.linkCaptureRepo.Create(&capture)
			if err != nil {
				l.l.Error("linkCaptureRepo Create err", zap.Error(err))
			}
		}
	} else if actionType == VIDEO_RECORD {
		//录像
		//duration, err := strconv.Atoi(actionValue)
		duStr := fmt.Sprintf("%02d", actionValue)
		tStr := fmt.Sprintf("00:00:%s", duStr)
		//if err != nil {
		//	return
		//}
		timeStr := carbon.Now().Format("YmdHis")
		fileName := fmt.Sprintf("%slinkage_record_%s.mp4", model.StaticLinkage, timeStr)
		params = []string{
			"-i",
			rtsp,
			"-r",
			"30",
			"-ss",
			"00:00:00",
			"-t",
			tStr,
			"-f",
			"mp4",
			fileName,
		}
		cmd := exec.Command("ffmpeg", params...)
		cmd.Start()

	}
}
