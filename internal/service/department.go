package service

import (
	"errors"
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
)

type DepartmentService struct {
	departmentRepo *repository.DepartmentRepo
	defaultPID     int
}

func NewDepartmentService(
	departmentRepo *repository.DepartmentRepo,
) *DepartmentService {
	return &DepartmentService{
		departmentRepo: departmentRepo,
		defaultPID:     -1,
	}
}

func (s *DepartmentService) Create(param request.CreateDepartment) error {
	var (
		department = model.Department{
			Name:     param.Name,
			ParentID: param.ParentID,
			Status:   model.DepartmentStatusEnable,
		}
		relations = make([]model.DepartmentRelation, 0)
		err       error
	)
	if param.ParentID != s.defaultPID {
		relations, err = s.departmentRepo.RelationListByChildID(param.ParentID)
		if err != nil {
			return err
		}
	}
	return s.departmentRepo.Create(department, relations)
}

func (s *DepartmentService) All() ([]response.DepartmentTreeOnly, error) {
	list, err := s.departmentRepo.All()
	if err != nil {
		return nil, err
	}
	result := s.treeOnly(list, s.defaultPID)
	return result, nil
}

func (s *DepartmentService) AllWithTypeCounts() ([]response.DepartmentTreeWithTypeCount, error) {
	list, err := s.departmentRepo.AllWithTypeCounts()
	if err != nil {
		return nil, err
	}
	result := s.treeWithTypeCounts(list, s.defaultPID)
	return result, nil
}

func (s *DepartmentService) TreeOnly(param request.DepartmentTreeByUserID) ([]response.DepartmentTreeOnly, error) {
	list, err := s.departmentRepo.FindByUserID(param.UID)
	if err != nil {
		return nil, err
	}
	result := s.treeOnly(list, s.defaultPID)
	return result, nil
}

func (s *DepartmentService) treeOnly(list []model.Department, parentID int) []response.DepartmentTreeOnly {
	var result = make([]response.DepartmentTreeOnly, 0, len(list))

	for _, v := range list {
		if v.ParentID != parentID {
			continue
		}
		var temp = response.DepartmentTreeOnly{
			Department: v,
		}
		temp.Children = s.treeOnly(list, v.ID)
		result = append(result, temp)
	}
	return result
}

func (s *DepartmentService) Tree(param request.DepartmentTreeByUserID) ([]response.DepartmentTreeWithTypeCount, error) {
	list, err := s.departmentRepo.FindByUserIDWithTypeCounts(param.UID, param.BusinessTypeID)
	if err != nil {
		return nil, err
	}

	result := s.treeWithTypeCounts(list, s.defaultPID)

	return result, nil
}

func (s *DepartmentService) treeWithTypeCounts(list []model.DepartmentWithTypeCount, parentID int) []response.DepartmentTreeWithTypeCount {
	var result = make([]response.DepartmentTreeWithTypeCount, 0, len(list))
	for _, v := range list {
		if v.ParentID != parentID {
			continue
		}
		var temp = response.DepartmentTreeWithTypeCount{
			DepartmentWithTypeCount: v,
		}
		temp.Children = s.treeWithTypeCounts(list, v.ID)
		result = append(result, temp)
	}
	return result
}

func (s *DepartmentService) Update(param request.UpdateDepartment) error {
	return s.departmentRepo.Update(
		param.ID,
		model.Department{
			Name:   param.Name,
			Status: param.Status,
		})
}

func (s *DepartmentService) Delete(param request.DeleteDepartment) error {
	exist, err := s.departmentRepo.HasDevice(param.ID)
	if err != nil {
		return err
	}
	if exist {
		return errors.New("该部门下存在设备")
	}
	_, err = s.departmentRepo.First(model.Department{ParentID: param.ID})
	if err == nil {
		return errors.New("该部门下存在子部门")

	}
	return s.departmentRepo.Delete(param.ID)
}

func (s *DepartmentService) ListByFilter(param request.DepartmentListByFilter) ([]model.Department, error) {
	return s.departmentRepo.ListByFilter(model.Department{
		ID:       param.ID,
		ParentID: param.ParentID,
	})
}
