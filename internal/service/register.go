package service

import (
	"crypto/des"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/system/config"
	"tw_platform/pkg/system/device_capture"
	dongle "tw_platform/pkg/system/usb"
	"tw_platform/pkg/utils"

	"github.com/robfig/cron/v3"
	"gorm.io/gorm"
)

const (
	DEV_NOT_ACTIVIE = "noactive"
	DEV_ACTIVE      = "active"
	key             = "tw*^dhjk"
	FULL_ACTIVE     = "LONG"

	LOGREMAIN       = 999999
	ThreeDPermisson = "3d"
)
const HASEXPIRED = 1
const NOEXPIRED = 0

type RegisterService struct {
	registerStore *repository.RegisterRepo
	config        *config.Config

	dc        *device_capture.DeviceCapture
	btService *BusinessTypeService
}

var IsExpired int32 = NOEXPIRED
var lastFuncRandStr string

var lastRandStr string
var (
	CIP_INVALID  = errors.New("paraminvalid")
	GET_CPU_FAIL = errors.New("getcpufail")
)
var ExpireTS int64

func NewRegisterService(
	registerStore *repository.RegisterRepo,
	config *config.Config,
	dc *device_capture.DeviceCapture,
	btService *BusinessTypeService,
) *RegisterService {
	return &RegisterService{
		registerStore: registerStore,
		config:        config,

		dc:        dc,
		btService: btService,
	}
}

func DeSEcbBase64Decode(src string) string {
	bret := ""
	var err error
	defer func() {
		if r := recover(); r != nil {
			fmt.Println("painc:", r)
			bret = ""
		}
	}()
	data, err := hex.DecodeString(src)
	if err != nil {
		panic(err)
	}
	keyByte := []byte(key)
	block, err := des.NewCipher(keyByte)
	if err != nil {
		panic(err)
	}
	bs := block.BlockSize()
	if len(data)%bs != 0 {
		panic("crypto/cipher: input not full blocks")
	}
	out := make([]byte, len(data))
	dst := out
	for len(data) > 0 {
		block.Decrypt(dst, data[:bs])
		data = data[bs:]
		dst = dst[bs:]
	}
	b64str := string(out)
	pbytes, _ := base64.StdEncoding.DecodeString(b64str)
	bret = string(pbytes)
	return bret
}

func isMatchMachCode(cmpStr, cmpRandom string) error {
	machstr := strings.Split(cmpStr, " ")
	matchRandom := false
	matchKey := false
	for i := range machstr {
		if machstr[i] == cmpRandom {
			matchRandom = true
			break
		}
	}
	if !matchRandom {
		return errors.New("授权页面已改变，请重试！")
	}

	keystr := dongle.GetKeyHardId()
	if keystr == "" {
		return errors.New("获取key失败")
	}
	for i := range machstr {
		if machstr[i] != keystr {
			matchKey = true
			break
		}
	}
	if !matchKey {
		return errors.New("key匹配失败")
	}
	return nil
}

func (r *RegisterService) GetTypeRegCnt(regType string) (*model.Function, error) {
	list, err := r.GetFuncPermission()
	if err != nil {
		return nil, err
	}
	for i := range list {
		if list[i].Type == regType {
			return &list[i], nil
		}
	}
	return nil, fmt.Errorf("未找到%s有效的授权信息", regType)
}

func (r *RegisterService) GetRegisterInfo() (*model.RegisterOut, error) {
	reg, err := r.registerStore.GetRegInfo()
	if err != nil {
		return nil, err
	}

	str := reg.RegInfo

	//生成加密狗id
	dongleid := dongle.GetKeyHardId()
	if dongleid == "" {
		return nil, errors.New("获取加密狗信息失败")
	}

	reg_str := ""
	act_str := ""
	rawstr, _ := base64.StdEncoding.DecodeString(str)
	rawstrlist := strings.Split(string(rawstr), " ")
	if len(rawstrlist) != 3 {
		return nil, errors.New("无效的注册信息")
	}
	if rawstrlist[0] == DEV_ACTIVE {
		act_str = "已永久激活"
	} else {
		//未激活
		tstr := rawstrlist[2]
		ts, err := strconv.ParseInt(tstr, 10, 64)
		if err != nil {
			return nil, err
		}
		act_str = utils.GetTimeStr(ts)
	}

	lastRandStr = utils.GetRandomStr(8)
	reg_str_raw := lastRandStr + " " + dongleid
	fmt.Println("raw str:", reg_str_raw)
	reg_str = base64.StdEncoding.EncodeToString([]byte(reg_str_raw))
	return &model.RegisterOut{
		RegCode: reg_str,
		RegInfo: act_str,
	}, nil
}

func (r *RegisterService) SetRegInfo(regStr string) error {
	raw := DeSEcbBase64Decode(regStr)

	rawlist := strings.Split(raw, " ")
	//发送的注册信息
	withprefix := strings.TrimLeft(rawlist[1], "*")
	sendRegStrList := strings.Split(withprefix, "-")
	threed := sendRegStrList[0]
	machbyte, err := base64.StdEncoding.DecodeString(sendRegStrList[len(sendRegStrList)-1])
	if err != nil {
		return CIP_INVALID
	}
	if err := isMatchMachCode(string(machbyte), lastFuncRandStr); err != nil {
		return err
	}
	machstr := strings.Split(string(machbyte), " ")

	in := len(rawlist)
	regday := rawlist[in-2]

	tsstr := rawlist[in-1]
	bindidstr := machstr[len(machstr)-1]

	dbstr := ""

	if regday == FULL_ACTIVE {
		//永久激活,记录 active bindidstri LONG
		dbstr = DEV_ACTIVE + " " + bindidstr + " " + "LONG"
		ExpireTS = LOGREMAIN
		//	db.AddLog("设置为永久激活")
	} else {
		//有限天数的激活,更新时间戳和硬件绑定信息
		// DEV_NOT_ACTIVIE
		adddays, err := strconv.Atoi(regday)
		if err != nil {
			return err
		}
		ts, err := strconv.ParseInt(tsstr, 10, 64)
		if err != nil {
			return err
		}
		expts := time.Unix(ts, 0).AddDate(0, 0, adddays).Unix()
		if time.Now().Unix() >= expts {
			return CIP_INVALID
		}
		ExpireTS = expts
		expstr := strconv.FormatInt(expts, 10)
		dbstr = DEV_NOT_ACTIVIE + " " + bindidstr + " " + expstr
		//db.AddLog(fmt.Sprintf("激活%d天,截止:%s", adddays, util.GetTimeStr(expts)))
	}
	//写数据库
	//svc := db.New()
	if err := r.registerStore.SetRegInfo(dbstr, threed); err != nil {
		return err
	}
	//更新过期时间，运行硬件采集
	IsExpired = NOEXPIRED

	//开启硬件采集
	r.dc.SetStatus(model.SetDeviceCaptureStatusReq{
		Status: model.DeviceCaptureStatusOn,
	})
	return nil
}

const (
	functionIdx = iota
	machIdx
	registerIdx
	timeStampIdx
)

func (r *RegisterService) SetFunction(funcStr string) error {
	raw := DeSEcbBase64Decode(funcStr)
	raw = strings.Trim(raw, " ")
	rawlist := strings.Split(raw, "- ")

	if len(rawlist) != 4 {
		return errors.New("授权码长度异常，请检查是否完整！")
	}

	match := rawlist[machIdx]
	machbyte, err := base64.StdEncoding.DecodeString(match)
	if err != nil {
		return errors.New("授权码解码异常，请检查是否完整！")
	}
	machStr := string(machbyte)
	if err := isMatchMachCode(machStr, lastRandStr); err != nil {
		return err
	}

	preList := strings.Split(rawlist[functionIdx], " ")
	jsonStr := ""
	for i := 1; i < len(preList); i++ {
		jsonStr += preList[i]
	}
	jsonStr = strings.TrimLeft(jsonStr, "*")

	funs := make([]model.BusinessTypeTree, 0)
	if err := json.Unmarshal([]byte(jsonStr), &funs); err != nil {
		return err
	}

	//register
	var (
		dbstr     string
		regday    = rawlist[registerIdx]
		machArr   = strings.Split(machStr, " ")
		bindidstr = machArr[len(machArr)-1]
		tsstr     = rawlist[timeStampIdx]
	)
	if regday == FULL_ACTIVE {
		//永久激活,记录 active bindidstri LONG
		dbstr = DEV_ACTIVE + " " + bindidstr + " " + "LONG"
		ExpireTS = LOGREMAIN
		//	db.AddLog("设置为永久激活")
	} else {
		//有限天数的激活,更新时间戳和硬件绑定信息
		// DEV_NOT_ACTIVIE
		adddays, err := strconv.Atoi(regday)
		if err != nil {
			return err
		}
		ts, err := strconv.ParseInt(tsstr, 10, 64)
		if err != nil {
			return err
		}
		expts := time.Unix(ts, 0).AddDate(0, 0, adddays).Unix()
		if time.Now().Unix() >= expts {
			return CIP_INVALID
		}
		ExpireTS = expts
		expstr := strconv.FormatInt(expts, 10)
		dbstr = DEV_NOT_ACTIVIE + " " + bindidstr + " " + expstr
		//db.AddLog(fmt.Sprintf("激活%d天,截止:%s", adddays, util.GetTimeStr(expts)))
	}

	//更新过期时间，运行硬件采集
	IsExpired = NOEXPIRED

	//开启硬件采集
	r.dc.SetStatus(model.SetDeviceCaptureStatusReq{
		Status: model.DeviceCaptureStatusOn,
	})

	// return r.registerStore.SetFunction(funs)
	regCode := base64.StdEncoding.EncodeToString([]byte(dbstr))
	return r.btService.CreateByMandate(funs, regCode)
}

func (r *RegisterService) GetFuncCode() (string, error) {
	dongleid := dongle.GetKeyHardId()
	if dongleid == "" {
		return "", errors.New("获取加密狗信息失败")
	}
	lastFuncRandStr = utils.GetRandomStr(8)
	funcStr := lastFuncRandStr + " " + dongleid
	reg_str := base64.StdEncoding.EncodeToString([]byte(funcStr))
	return reg_str, nil
}

func (r *RegisterService) GetFuncPermission() ([]model.Function, error) {
	return r.registerStore.GetFuncItems()
}

func (r *RegisterService) Get3dPermission() (bool, error) {
	out, err := r.registerStore.GetRegInfo()
	if err != nil {
		return false, err
	}
	if out.ThreeD == ThreeDPermisson {
		return true, nil
	}
	return false, nil
}

//----------------------------------------------------------------------------------------------
//     处理系统激活的方法
//----------------------------------------------------------------------------------------------

func (r *RegisterService) wrapCycleCheck() func() {
	return func() {
		r.checkNowRegInfo(false)
	}
}

// 一小时检查一次
// * * * * * *	每秒执行一次
// 0 */1 * * * *	每分钟执行一次
// 0 0 */1 * * *	每小时执行一次
// 0 0 0 */1 * *	每天 00:00执行一次
// 0 30 23 */1 * *	每天 23:30执行一次
// 0 0 0 1 */1 *	每月的第一天执行
// 0 30 21 * * 1	每周一 21:30执行
func (r *RegisterService) cycle() {
	local, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		fmt.Printf("err: %s", err.Error())
	}

	interval := cron.New(cron.WithLocation(local), cron.WithSeconds())
	str := "0  0 */1  * * ?"
	cycleFun := r.wrapCycleCheck()
	_, err = interval.AddFunc(str, cycleFun)
	if err != nil {
		panic(err.Error())
	}
	interval.Start()

	select {}
}

func (r *RegisterService) checkNowRegInfo(startFlag bool) string {
	fmt.Println("---------check  expired------------")
	//获取当前的注册信息

	reg, _ := r.registerStore.GetRegInfo()
	regcip := reg.RegInfo
	//base64解码
	regraw, _ := base64.StdEncoding.DecodeString(regcip)
	regstr := string(regraw)
	reglist := strings.Split(regstr, " ")

	//注册信息
	reginfo := reglist[0]
	//硬件id
	bindinfo := reglist[1]
	//时间戳
	tstr := reglist[2]

	keyid := dongle.GetKeyHardId()

	fmt.Println(reginfo, bindinfo, tstr)
	//根据注册信息和当前搜到的硬件信息，处理最终的注册结果以及运行时长
	return r.decideRunTime(reginfo, bindinfo, tstr, keyid, startFlag)
}

/*
* 　已永久激活并存在有效设备,正常运行
*　 已永久激活但无有效设备，随机运行２－４小时后转到提示
*　 非永久激活未到授权截止，正常运行至截止
*   已过授权时间，随机运行２－４小时后转到提示
 */
func (r *RegisterService) decideRunTime(reginfo string, idstr string, timestr string, keyid string, startUpFlag bool) string {
	switch reginfo {
	case DEV_NOT_ACTIVIE:
		ts, _ := strconv.ParseInt(timestr, 10, 64)
		if startUpFlag {
			ExpireTS = ts
		}
		if time.Now().Unix() >= ts {
			//已经过了授权的时间，则随机运行1-4小时
			//第一次启动可以运行，但每晚的12点检查不再随机运行
			if !startUpFlag {
				return ""
			}
			addhours := rand.Intn(4) + 1
			addt := time.Duration(addhours) * time.Hour
			ExpireTS = time.Now().Add(addt).Unix()
			IsExpired = NOEXPIRED
			return "未激活,已过授权日期,运行至:" + utils.GetTimeStr(ExpireTS)
		} else {
			exprielog := "未永久授权,授权截止日期: " + utils.GetTimeStr(ts)
			return exprielog
		}
	case DEV_ACTIVE:
		//判断绑定的设备是否存在
		if idstr != keyid {
			if !startUpFlag {
				//没有找到。已经永久授权的情况下，直接强制退出到登录界面
				fmt.Println("no valid key")
				keystr := ""
				if keyid == "" {
					keystr = "为空"
				} else {
					keystr = keyid
				}
				log := fmt.Sprintf("已永久激活，但没有找到key,bind:%s,key:%s,回授权页面", idstr, keystr)
				//if !startUpFlag {
				//	db.AddLog(log)
				//}
				if ExpireTS == LOGREMAIN {
					ExpireTS = time.Now().Unix()
				}
				return log
			} else {
				//随机运行一段时间后停止
				addhours := rand.Intn(4) + 1
				addt := time.Duration(addhours) * time.Hour
				ExpireTS = time.Now().Add(addt).Unix()
				IsExpired = NOEXPIRED
				return "无有效的key,随机运行后停止"
			}
		} else {
			if ExpireTS != LOGREMAIN || IsExpired == HASEXPIRED {
				log := fmt.Sprintf("key之前状态不正确，恢复正常")
				ExpireTS = LOGREMAIN
				IsExpired = NOEXPIRED
				r.dc.SetStatus(model.SetDeviceCaptureStatusReq{
					Status: model.DeviceCaptureStatusOn,
				})
				return log
			} else {
				if startUpFlag {
					return "已永久授权，当前环境正确"
				}
			}
		}
	}
	return ""
}

func (r *RegisterService) CheckPermission() {
	_, err := r.GetRegisterInfo()

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			now := time.Now()
			util := now.AddDate(0, 0, 7).Unix()
			utstr := strconv.FormatInt(util, 10)
			str := "noactive " + "notbind " + utstr
			r.registerStore.SetRegInfo(str, "")
		} else {
			panic(err.Error())
		}
	}

	result := r.checkNowRegInfo(true)
	fmt.Println(result)
	go r.cycle()
}
