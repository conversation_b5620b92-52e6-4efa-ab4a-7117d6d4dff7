package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"strconv"
	"time"
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/system/config"
	"tw_platform/pkg/system/standby"
	"tw_platform/pkg/utils"

	"go.uber.org/zap"
)

type StandbyService struct {
	standbyRepo *repository.StandbyRepo
	systemRepo  *repository.SystemRepo

	stats *standby.StandbyStats
	l     *zap.Logger

	config *config.Config

	defaultID int
}

func NewStandbyService(
	standbyRepo *repository.StandbyRepo,
	systemRepo *repository.SystemRepo,
	stats *standby.StandbyStats,
	config *config.Config,
	l *zap.Logger,
) *StandbyService {
	return &StandbyService{
		standbyRepo: standbyRepo,
		systemRepo:  systemRepo,
		stats:       stats,
		config:      config,
		l:           l,
		defaultID:   1,
	}
}

func (s *StandbyService) SyncTables() (response.SyncTables, error) {
	var (
		result response.SyncTables
	)
	info, err := s.GetStandby()
	if err != nil {
		return result, err
	}
	if info.Status != model.StandbyStatusEnabled {
		return result, errors.New("该节点未启用双机热备")
	}
	if info.Type != model.StandbyTypeMaster {
		return result, errors.New("该节点不是主节点")
	}

	// tables, err := s.standbyRepo.SyncTables()
	tables, err := s.generateSQLFile()
	if err != nil {
		return result, err
	}

	result.Path = tables

	sum, err := utils.FileMD5(tables)
	if err != nil {
		return result, err
	}
	result.MD5 = sum
	return result, nil
}

func (s *StandbyService) Sync() error {
	info, err := s.GetStandby()
	if err != nil {
		return err
	}

	if info.Status != model.StandbyStatusEnabled {
		return errors.New("该节点未启用双机热备")
	}
	if info.Type != model.StandbyTypeSlave {
		return errors.New("该节点不是从节点")
	}
	path, err := s.standbyRepo.GetSyncTables(info)
	if err != nil {
		return err
	}

	err = s.standbyRepo.TruncateTables()
	if err != nil {
		return err
	}
	err = s.importSQLFile(path)
	if err != nil {
		return err
	}
	s.standbyRepo.RestartIdentity()
	return nil
}

func (s *StandbyService) Stats() (bool, model.StandbyStatus) {
	return s.stats.Enabled(), s.stats.Role()
}

func (s *StandbyService) SetStandby(param request.SetStandby) error {
	if param.Status == model.StandbyStatusEnabled && param.Type == model.StandbyTypeSlave {
		if !utils.IsAccept(param.IP, param.Port) {
			return errors.New("该IP或端口不可用")
		}
	}
	standby := model.Standby{
		ID:     s.defaultID,
		Status: param.Status,
		Type:   param.Type,
		IP:     param.IP,
		Port:   param.Port,
	}
	err := s.standbyRepo.SetStandby(standby)
	if err != nil {
		return err
	}

	return s.stats.Update(standby)
}

func (s *StandbyService) GetStandby() (model.Standby, error) {
	return s.standbyRepo.GetStandby(s.defaultID)
}

func (s *StandbyService) FetchPush(param request.FetchTables) error {
	fd, err := param.Tables.Open()
	if err != nil {
		return err
	}
	defer fd.Close()
	f, err := os.Create(param.Tables.Filename)
	if err != nil {
		return err
	}
	defer f.Close()

	_, err = io.Copy(f, fd)
	if err != nil {
		return err
	}

	err = s.standbyRepo.TruncateTables()
	if err != nil {
		return err
	}

	err = s.importSQLFile(param.Tables.Filename)
	if err != nil {
		return err
	}
	s.standbyRepo.RestartIdentity()
	return nil
}

func (s *StandbyService) PushTables() error {
	info, err := s.GetStandby()
	if err != nil {
		return err
	}

	if info.Status != model.StandbyStatusEnabled {
		return errors.New("该节点未启用双机热备")
	}
	if info.Type != model.StandbyTypeMaster {
		return errors.New("该节点不是主节点")
	}
	path, err := s.generateSQLFile()
	if err != nil {
		return err
	}
	err = s.standbyRepo.PushTables(info, path)
	return err
}

func (s *StandbyService) DeviceData(param request.StandbyDataReq) ([]request.StandbyData, error) {
	return s.standbyRepo.DeviceData(param.Begin, param.End)
}

func (s *StandbyService) FetchDeviceData() error {
	if !s.stats.Enabled() || s.stats.Role() != model.StandbyTypeSlave {
		return nil
	}
	var (
		client = http.Client{
			Timeout: 20 * time.Second,
		}
		now = time.Now()
	)

	req, err := http.NewRequest(
		http.MethodGet,
		fmt.Sprintf(
			"%s/api/v1/standby/device_data?begin=%d&end=%d",
			s.stats.Host(),
			now.Add(-1*time.Minute).Unix(),
			now.Unix(),
		),
		nil,
	)
	if err != nil {
		return err
	}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("fetch device data errorcode:%d", resp.StatusCode)
	}
	response := struct {
		Code int                   `json:"code"`
		Msg  string                `json:"msg"`
		Data []request.StandbyData `json:"data"`
	}{}
	err = json.NewDecoder(resp.Body).Decode(&response)
	if err != nil {
		return err
	}
	if response.Code != 1 {
		return errors.New(response.Msg)
	}

	if len(response.Data) == 0 {
		return nil
	}

	err = s.standbyRepo.FetchDeviceData(response.Data)

	return err
}

func (s *StandbyService) generateSQLFile() (string, error) {
	var (
		fName = fmt.Sprintf("%s%s.sql", model.StaticStandby, time.Now().Format("20060102150405"))
		args  = []string{
			"-U",
			s.config.PostgreSQL.Name,
			"-d",
			s.config.PostgreSQL.DB,
			"-h",
			s.config.PostgreSQL.Host,
			"-p",
			strconv.Itoa(s.config.PostgreSQL.Port),
			"--data-only",
			"--column-inserts",
			"-f",
			fName,
		}
	)
	pgDump, err := exec.LookPath("pg_dump")
	if err != nil {
		return "", errors.New("必要的工具不存在，请联系技术人员。")
	}
	for name := range s.standbyRepo.UnHandlerTables {
		args = append(args, "-T", name)
	}
	cmd := exec.Command(pgDump, args...)
	cmd.Env = append(cmd.Env, fmt.Sprintf("PGPASSWORD=%s", s.config.PostgreSQL.Password))
	out, err := cmd.CombinedOutput()
	if err != nil {
		s.l.Warn("export sql file failure", zap.String("std", string(out)))
		return "", err
	}
	return fName, nil
}

func (s *StandbyService) importSQLFile(path string) error {
	var (
		conf = s.config.PostgreSQL
		args = []string{
			"-U",
			conf.Name,
			"-d",
			conf.DB,
			"-h",
			conf.Host,
			"-p",
			strconv.Itoa(conf.Port),
			"-f",
			path,
		}
	)

	psql, err := exec.LookPath("psql")
	if err != nil {
		return errors.New("必要的工具不存在，请联系技术人员。")
	}
	cmd := exec.Command(psql, args...)
	cmd.Env = append(cmd.Env, fmt.Sprintf("PGPASSWORD=%s", conf.Password))

	out, err := cmd.CombinedOutput()
	if err != nil {
		s.l.Warn("import sql file failure", zap.String("std", string(out)))
		return err
	}
	return nil
}
