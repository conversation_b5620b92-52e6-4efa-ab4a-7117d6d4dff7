package service

import (
	"crypto/tls"
	"fmt"
	"strings"
	"time"
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/system/config"
	"tw_platform/pkg/utils"

	"go.uber.org/zap"
	"gopkg.in/gomail.v2"
)

type NotificationGroupService struct {
	groupRepo *repository.NotificationGroupRepo
	config    *config.Config
	l         *zap.Logger
}

func NewNotificationGroupService(groupRepo *repository.NotificationGroupRepo, config *config.Config, log *zap.Logger) *NotificationGroupService {
	return &NotificationGroupService{
		groupRepo: groupRepo,
		config:    config,
		l:         log.Named("group_service:"),
	}
}
func (s *NotificationGroupService) Create(param request.CreateNotificationGroup) error {
	var (
		group = model.NotificationGroup{
			Name:             param.Name,
			NotificationType: param.NotificationType,
			Status:           param.Status,
			Config:           param.Config,
			Description:      param.Description,
		}
	)
	return s.groupRepo.Create(group, param.NotificationUsers)
}
func (s *NotificationGroupService) GetNotificationGroupList() ([]response.ListNotificationGroup, error) {
	return s.groupRepo.GetNotificationGroupList()
}
func (s *NotificationGroupService) GetNotificationGroupDetail(id int) (response.ListNotificationGroupDetail, error) {
	return s.groupRepo.GetNotificationGroupDetail(id)
}
func (d *NotificationGroupService) Delete(param request.DeleteNotificationGroup) error {
	return d.groupRepo.Delete(param.ID)
}
func (s *NotificationGroupService) Update(param request.UpdateNotificationGroup) error {
	var (
		group = model.NotificationGroup{
			Name:             param.Name,
			NotificationType: param.NotificationType,
			Status:           param.Status,
			Config:           param.Config,
			Description:      param.Description,
		}
	)
	return s.groupRepo.Update(group, param.NotificationUsers, param.ID)
}
func (s *NotificationGroupService) GetGprsConfig() (model.Gprs, error) {
	return s.groupRepo.GetGprsConfig()
}
func (s *NotificationGroupService) UpdateGprsConfig(gprs model.Gprs) error {
	return s.groupRepo.UpdateGprsConfig(gprs)
}
func (s *NotificationGroupService) GetEmailConfig() (model.EmailClient, error) {
	return s.groupRepo.GetEmailConfig()
}
func (s *NotificationGroupService) UpdateEmailConfig(email model.EmailClient) error {
	return s.groupRepo.UpdateEmailConfig(email)
}
func (s *NotificationGroupService) ExecuteNotification(id int, title, content string,
	device_name, unit_name, level, message, value string,
) error {
	now := time.Now()
	weekday := int(now.Weekday())
	if weekday == 0 {
		weekday = 7
	}
	group, err := s.GetNotificationGroupDetail(id)
	if err != nil {
		return err
	}
	if group.Status != "ON" {
		return nil
	}
	if !(len(group.Config.Week) < 1 || contains(group.Config.Week, int(weekday))) {
		s.l.Info(fmt.Sprintf("%s noticeficationgroup not int week", group.Name))
		return nil
	}
	gprs, err := s.GetGprsConfig()
	if err != nil {
		return err
	}
	email, err := s.GetEmailConfig()
	if err != nil {
		return err
	}
	notiTypes := strings.Split(group.NotificationType, ",")
	if utils.ContainsIgnoreCase(notiTypes, "SMS") {
		for _, user := range group.NotificationUsers {
			if user.Phone != "" && gprs.Gprsenable {
				if gprs.Type == model.GprsLocalType {
					err = utils.Send4gSMS(gprs.GprsIp, content, user.Phone)
					if err != nil {
						s.l.Warn(fmt.Sprintf("%s Send4gSMS", group.Name), zap.Error(err))
					}
				} else if gprs.Type == model.GprsCloudType {
					err = utils.SendCloudSms(gprs.GprsIp, user.Phone, device_name, unit_name, level, message, value, "")
					if err != nil {
						s.l.Warn(fmt.Sprintf("%s SendCloudSMS", group.Name), zap.Error(err))
					}
				}
			}
			if user.Phone != "" && gprs.Gprsenable_2 {
				if gprs.Type_2 == model.GprsLocalType {
					err = utils.Send4gSMS(gprs.GprsIp_2, content, user.Phone)
					if err != nil {
						s.l.Warn(fmt.Sprintf("%s Send4gSMS", group.Name), zap.Error(err))
					}
				} else if gprs.Type_2 == model.GprsCloudType {
					err = utils.SendCloudSms(gprs.GprsIp_2, user.Phone, device_name, unit_name, level, message, value, "")
					if err != nil {
						s.l.Warn(fmt.Sprintf("%s SendCloudSMS", group.Name), zap.Error(err))
					}
				}
			}
		}
	}
	if utils.ContainsIgnoreCase(notiTypes, "CALL") {
		for _, user := range group.NotificationUsers {
			if user.Phone != "" && gprs.Gprsenable {
				if gprs.Type == model.GprsLocalType {
					err = utils.Send4gCall(gprs.GprsIp, content, user.Phone)
					if err != nil {
						s.l.Warn(fmt.Sprintf("%s Send4gCall", group.Name), zap.Error(err))
					}
				} else if gprs.Type == model.GprsCloudType {
					err = utils.SendCloudCall(gprs.GprsIp, user.Phone, device_name, unit_name, level, message, value, "")
					if err != nil {
						s.l.Warn(fmt.Sprintf("%s SendCLoudCall", group.Name), zap.Error(err))
					}
				}
			}
			if user.Phone != "" && gprs.Gprsenable_2 {
				if gprs.Type_2 == model.GprsLocalType {
					err = utils.Send4gCall(gprs.GprsIp_2, content, user.Phone)
					if err != nil {
						s.l.Warn(fmt.Sprintf("%s Send4gCall", group.Name), zap.Error(err))
					}
				} else if gprs.Type_2 == model.GprsCloudType {
					err = utils.SendCloudCall(gprs.GprsIp_2, user.Phone, device_name, unit_name, level, message, value, "")
					if err != nil {
						s.l.Warn(fmt.Sprintf("%s SendCLoudCall", group.Name), zap.Error(err))
					}
				}
			}
		}
	}
	if utils.ContainsIgnoreCase(notiTypes, "EMAIL") {
		for _, user := range group.NotificationUsers {
			if user.Email != "" && email.IsOpen {
				d := gomail.NewDialer(email.ServerHost, email.ServerPort, email.FromEmail, email.FromPasswd)
				d.TLSConfig = &tls.Config{InsecureSkipVerify: email.InsecureSkipVerify}
				err = utils.SendEmailMessage(d, email.FromEmail, content, title, user.Email)
				if err != nil {
					s.l.Warn(fmt.Sprintf("%s SendEmailMessage", group.Name), zap.Error(err))
				}
			}
		}
	}
	if utils.ContainsIgnoreCase(notiTypes, "BroadCast") {
		if gprs.Gprsenable {
			utils.Send4gBroadcast(gprs.GprsIp, content)
		}
		if gprs.Gprsenable_2 {
			utils.Send4gBroadcast(gprs.GprsIp_2, content)
		}
	}
	if utils.ContainsIgnoreCase(notiTypes, "Wechat") {
		if gprs.Type == model.GprsLocalType {
			err = utils.SendWechatWebhookMsg(group.Config.Wechat, content)
			if err != nil {
				s.l.Warn(fmt.Sprintf("%s SendWechatWebhookMsg", group.Name), zap.Error(err))
			}
		} else if gprs.Type == model.GprsCloudType {
			err = utils.SendCloudWeichat(gprs.GprsIp, group.Config.Wechat, device_name, unit_name, level, message, value, "")
			if err != nil {
				s.l.Warn(fmt.Sprintf("%s SendCLoudCall", group.Name), zap.Error(err))
			}
		}
	}
	if utils.ContainsIgnoreCase(notiTypes, "DingDing") {
		if gprs.Type == model.GprsLocalType {
			err = utils.SendDingDIngWebhookMsg(group.Config.DingDIng, content)
			if err != nil {
				s.l.Warn(fmt.Sprintf("%s SendDingDIngWebhookMsg", group.Name), zap.Error(err))
			}
		} else if gprs.Type == model.GprsCloudType {
			err = utils.SendCloudDingDing(gprs.GprsIp, group.Config.DingDIng, device_name, unit_name, level, message, value, "")
			if err != nil {
				s.l.Warn(fmt.Sprintf("%s SendCLoudCall", group.Name), zap.Error(err))
			}
		}

	}
	if utils.ContainsIgnoreCase(notiTypes, "MyHook") {
		err = utils.SendMyWebhookMsg(group.Config.MyHook, content)
		if err != nil {
			s.l.Warn(fmt.Sprintf("%s SendMyWebhookMsg", group.Name), zap.Error(err))
		}

	}
	return nil
}
