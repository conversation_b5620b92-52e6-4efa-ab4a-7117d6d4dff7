package service

import (
	"fmt"
	"io"
	"strings"
	"time"
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/utils"

	"go.uber.org/zap"
)

type InspectService struct {
	inspectRepo *repository.InspectRepo
	l           *zap.Logger
}

func NewInspectService(
	inspectRepo *repository.InspectRepo,
	l *zap.Logger,
) *InspectService {
	return &InspectService{
		inspectRepo: inspectRepo,
		l:           l.Named("inspect_service:"),
	}
}

func (i *InspectService) Create(param request.CreateInspect) error {
	return i.inspectRepo.Create(param)
}

func (i *InspectService) List(param request.InspectList) ([]response.InspectList, int64, error) {
	return i.inspectRepo.List(param)
}

func (i *InspectService) PlanList(param request.InspectPlanList) ([]response.InspectPlanList, error) {
	return i.inspectRepo.PlanList(param)
}

func (i *InspectService) Execute(param request.ExecuteInspectPlan) error {
	var (
		images = make([]model.InspectPlanImage, 0, len(param.Images))
	)
	for _, image := range param.Images {
		nameArr := strings.Split(image, "/")
		if len(nameArr) != 3 {
			continue
		}
		images = append(images, model.InspectPlanImage{
			PlanID: param.PlanID,
			Path:   nameArr[len(nameArr)-1],
		})
	}
	t := time.Now()
	return i.inspectRepo.Execute(model.InspectPlan{
		ID:             param.PlanID,
		Status:         model.InspectPlanStatusFinished,
		Comment:        param.Comment,
		Suggestion:     param.Suggestion,
		ExecutedAt:     &t,
		ExecutedUserID: &param.UID,
	}, images)
}

func (i *InspectService) Upload(param request.InspectionUpload) (string, error) {
	fd, err := param.File.Open()
	if err != nil {
		return "", err
	}
	defer fd.Close()
	data, err := io.ReadAll(fd)
	if err != nil {
		return "", err
	}
	name, err := utils.SaveFile(param.File.Filename, model.StaticInspection, data)
	if err != nil {
		return "", err
	}
	return fmt.Sprint(model.StaticInspection, name), nil
}

func (i *InspectService) Delete(param request.InspectionDeleteFile) error {
	pathArr := strings.Split(param.Path, "/")
	if len(pathArr) != 3 {
		return fmt.Errorf("invalid path")
	}
	if fmt.Sprintf("%s/%s/", pathArr[0], pathArr[1]) != model.StaticInspection {
		return fmt.Errorf("invalid path")
	}
	return utils.DeleteFile(param.Path)
}
