package service

import (
	"errors"
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/utils"
)

type LogService struct {
	logRepo *repository.LogRepo
}

func NewLogService(logRepo *repository.LogRepo) *LogService {
	return &LogService{
		logRepo: logRepo,
	}
}

func (s *LogService) OperationList(param request.OperationList) ([]response.OperationList, int64, error) {
	return s.logRepo.OperationList(param)
}

func (s *LogService) AlarmList(param request.AlarmLogList) ([]response.AlarmLogList, int64, error) {
	return s.logRepo.AlarmList(param)
}

func (s *LogService) Usage(param request.TableUseage) (string, error) {
	var name string
	switch param.Type {
	case model.LogTableTypeOperation:
		name = model.OperationLog{}.TableName()
	case model.LogTableTypeAlarm:
		name = model.AlarmLog{}.TableName()
	default:
		return name, errors.New("type error")
	}
	result, err := s.logRepo.Usage(name)
	if err != nil {
		return "", err
	}

	return utils.HumanizeUnit(result.Usage), nil
}

func (s *LogService) Truncate(param request.TableTruncate) error {
	var name string
	switch param.Type {
	case model.LogTableTypeOperation:
		name = model.OperationLog{}.TableName()
	case model.LogTableTypeAlarm:
		name = model.AlarmLog{}.TableName()
	default:
		return errors.New("type error")
	}

	return s.logRepo.Truncate(name)
}
