package service

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"tw_platform/pkg/model/request"

	"context"

	"github.com/redis/go-redis/v9"
	lua "github.com/yuin/gopher-lua"
	"go.uber.org/zap"
)

type ScriptLuaService struct {
	l                      *zap.Logger
	redis                  *redis.Client
	deviceService          *DeviceService
	doorService            *DoorService
	noticeService          *NotificationGroupService
	linkageService         *LinkageService
	virtualVariableService *VirtualVariableService
}

func NewScriptLuaService(log *zap.Logger, redis *redis.Client, deviceService *DeviceService,
	doorService *DoorService, noticeService *NotificationGroupService,
	linkageService *LinkageService, virtualVariableService *VirtualVariableService) *ScriptLuaService {
	return &ScriptLuaService{
		l:                      log.Named("scriptlua_service:"),
		redis:                  redis,
		deviceService:          deviceService,
		doorService:            doorService,
		noticeService:          noticeService,
		linkageService:         linkageService,
		virtualVariableService: virtualVariableService,
	}
}
func (s *ScriptLuaService) Task(L *lua.LState) int {
	delay := L.CheckNumber(1)
	fn := L.CheckFunction(2)
	go func() {
		time.Sleep(time.Duration(delay) * time.Second)
		co, _ := L.NewThread()
		if err := co.CallByParam(lua.P{
			Fn:      fn,
			NRet:    0,
			Protect: true,
		}); err != nil {
			fmt.Println("Error executing Lua function:", err)
		}
	}()
	return 0
}
func (s *ScriptLuaService) GetDeviceData(L *lua.LState) int {
	arg1 := L.ToNumber(1)
	arg2 := L.ToNumber(2)
	ctx := context.Background()
	temp, err := s.redis.Get(ctx, fmt.Sprintf("device_data:%d", arg1)).Bytes()
	if err != nil {
		// L.Push(lua.LNumber(-999999))
		return 0
	}
	var msg request.DeviceData
	err = json.Unmarshal(temp, &msg)
	if err != nil {
		// L.Push(lua.LNumber(-999999))
		return 0
	}
	for _, val := range msg.Units {
		if val.Point == int(arg2) {
			f, err := strconv.ParseFloat(val.Value, 64)
			if err != nil {
				// L.Push(lua.LNumber(-999999))
				return 0
			}
			L.Push(lua.LNumber(f))
			return 1
		}
	}
	// L.Push(lua.LNumber(-999999))
	return 0
}
func (s *ScriptLuaService) GetDeviceStatus(L *lua.LState) int {
	arg1 := L.ToNumber(1)
	ctx := context.Background()
	temp, err := s.redis.Get(ctx, fmt.Sprintf("device_data:%d", arg1)).Bytes()
	if err != nil {
		// L.Push(lua.LNumber(-999999))
		return 0
	}
	var msg request.DeviceData
	err = json.Unmarshal(temp, &msg)
	if err != nil {
		// L.Push(lua.LNumber(-999999))
		return 0
	}
	L.Push(lua.LNumber(msg.Status))
	return 1
}
func formatNumber(input string) (string, error) {
	if strings.Contains(input, ".") {
		num, err := strconv.ParseFloat(input, 64)
		if err != nil {
			return "", err
		}
		return strings.TrimRight(strings.TrimRight(fmt.Sprintf("%.2f", num), "0"), "."), nil
	}
	return input, nil
}
func (s *ScriptLuaService) SetDeviceData(L *lua.LState) int {
	arg1 := L.ToNumber(1)
	arg2 := L.ToNumber(2)
	arg3 := L.ToString(3)
	formatarg3, err := formatNumber(arg3)
	if err != nil {
		return 0
	}
	ctx := context.Background()
	temp, err := s.redis.Get(ctx, fmt.Sprintf("device_data:%d", arg1)).Bytes()
	if err != nil {
		return 0
	}
	var msg request.DeviceData
	err = json.Unmarshal(temp, &msg)
	if err != nil {
		return 0
	}
	for index, val := range msg.Units {
		if val.Point == int(arg2) {
			msg.Units[index].Value = formatarg3
		}
	}
	s.deviceService.ReportData(msg)
	return 0
}
func (s *ScriptLuaService) ControlDevice(L *lua.LState) int {
	arg1 := L.ToNumber(1)
	arg2 := L.ToNumber(2)
	arg3 := L.ToString(3)
	var param request.DeviceControl
	param.DeviceID = int(arg1)
	param.UnitID = int(arg2)
	param.Value = arg3
	s.deviceService.Control(param)
	return 0
}
func (s *ScriptLuaService) GetDoorStatus(L *lua.LState) int {
	arg1 := L.ToNumber(1)
	arg2 := L.ToNumber(2)
	dinfo, err := s.doorService.GetDoorInfo(int(arg1))
	if err != nil {
		// L.Push(lua.LNumber(0))
		return 0
	}
	if dinfo.Type == "一体机" {
		status, err := s.doorService.GetDoorStatus(dinfo)
		if err != nil {
			// L.Push(lua.LNumber(0))
			return 0
		}
		L.Push(lua.LNumber(status))
		return 1
	}
	if dinfo.Type == "控制器" {
		status, err := s.doorService.GetDoorControllerStatus(dinfo)
		if err != nil {
			// L.Push(lua.LNumber(0))
			return 0
		}
		L.Push(lua.LNumber(status.DoorStatus[int(arg2)-1]))
		return 1
	}
	// L.Push(lua.LNumber(0))
	return 0
}
func (s *ScriptLuaService) ControlDoor(L *lua.LState) int {
	//一体机  close open alwaysOpen
	arg1 := L.ToNumber(1)
	arg2 := L.ToNumber(2)
	arg3 := L.ToString(3)
	dinfo, err := s.doorService.GetDoorInfo(int(arg1))
	if err != nil {
		return 0
	}
	if dinfo.Type == "一体机" {
		req := request.CtrDoorReq{
			Id:  int(arg1),
			Way: arg3,
		}
		if err := s.doorService.CtrlDoor(req, dinfo); err != nil {
			fmt.Println(err)
			return 0
		}
	}
	if dinfo.Type == "控制器" {
		ptrArg1 := int(arg1)
		ptrArg2 := int(arg2)
		req := request.CtrDoorControllerReq{
			DoorId: &ptrArg1,
			SubId:  &ptrArg2,
			Way:    arg3,
		}
		if err := s.doorService.CtrlDoorDoorController(dinfo, req); err != nil {
			fmt.Println(err)
			return 0
		}
	}
	return 0
}

// 传参 摄像头id 抓拍还是录像 抓拍几次 描述
func (s *ScriptLuaService) ControlVideo(L *lua.LState) int {
	arg1 := L.ToNumber(1)
	arg2 := L.ToString(2)
	arg3 := L.ToNumber(3)
	arg4 := L.ToString(4)
	param := request.DeviceDetail{}
	param.ID = int(arg1)
	config, err := s.deviceService.Config(param)
	if err != nil {
		return 0
	}
	config_withname, err := s.deviceService.ConfigWithName(param)
	if err != nil {
		return 0
	}
	// s.linkageService.LinkageVideoAction("抓拍", arg2, int(arg3), config.Config, int(arg1))
	s.linkageService.LinkageVideoAction(arg4, arg2, int(arg3), int(arg1), config_withname.DeviceInfo.Name,
		config.Config, int(config.Config.VideoDevice.ChannelID))
	return 0
}
func (s *ScriptLuaService) GetVirtualDeviceData(L *lua.LState) int {
	arg1 := L.ToNumber(1)
	arg2 := L.ToNumber(2)
	list, err := s.virtualVariableService.List()
	if err != nil {
		return 0
	}
	for _, v := range list {
		if v.ID == int(arg1) {
			for _, u := range v.Unit {
				if u.Id == int(arg2) {
					f, err := strconv.ParseFloat(u.Value, 64)
					if err != nil {
						return 0
					}
					L.Push(lua.LNumber(f))
					return 1
				}
			}
		}
	}
	return 1
}
func (s *ScriptLuaService) SetVirtualDeviceData(L *lua.LState) int {
	arg1 := L.ToNumber(1)
	arg2 := L.ToNumber(2)
	arg3 := L.ToString(3)
	var param request.UpdateVirtualUnit
	param.DeviceID = int(arg1)
	param.UnitID = int(arg2)
	param.Fields = make(map[string]interface{})
	param.Fields["value"] = arg3
	s.virtualVariableService.UpdateUnit(param)
	return 0
}
func (s *ScriptLuaService) ExecuteNotification(L *lua.LState) int {
	arg1 := L.ToNumber(1)
	arg2 := L.ToString(2)
	arg3 := L.ToString(3)
	s.noticeService.ExecuteNotification(int(arg1), arg2, arg3, "", "", "", "", "")
	return 0
}
func (s *ScriptLuaService) Delay(L *lua.LState) int {
	arg1 := L.ToNumber(1)
	time.Sleep(time.Duration(int(arg1)) * time.Second)
	return 0
}
func (s *ScriptLuaService) ScriptDeal(code string) error {
	L := lua.NewState()
	defer L.Close()
	L.SetGlobal("Task", L.NewFunction(s.Task))
	L.SetGlobal("GetDeviceData", L.NewFunction(s.GetDeviceData))
	L.SetGlobal("SetDeviceData", L.NewFunction(s.SetDeviceData))
	L.SetGlobal("GetDeviceStatus", L.NewFunction(s.GetDeviceStatus))
	L.SetGlobal("ControlDevice", L.NewFunction(s.ControlDevice))
	L.SetGlobal("ControlDoor", L.NewFunction(s.ControlDoor))
	L.SetGlobal("GetDoorStatus", L.NewFunction(s.GetDoorStatus))
	L.SetGlobal("ControlVideo", L.NewFunction(s.ControlVideo))
	L.SetGlobal("ExecuteNotification", L.NewFunction(s.ExecuteNotification))
	L.SetGlobal("GetVirtualDeviceData", L.NewFunction(s.GetVirtualDeviceData))
	L.SetGlobal("SetVirtualDeviceData", L.NewFunction(s.SetVirtualDeviceData))
	L.SetGlobal("Delay", L.NewFunction(s.Delay))
	err := L.DoString(code)
	if err != nil {
		return err
	}
	return nil
}
func (s *ScriptLuaService) DeviceScriptDeal(dev_id, point int, code string) error {
	L := lua.NewState()
	defer L.Close()
	L.SetGlobal("Task", L.NewFunction(s.Task))
	L.SetGlobal("DevId", lua.LNumber(dev_id))
	L.SetGlobal("Point", lua.LNumber(point))
	L.SetGlobal("GetDeviceData", L.NewFunction(s.GetDeviceData))
	L.SetGlobal("GetDeviceStatus", L.NewFunction(s.GetDeviceStatus))
	L.SetGlobal("SetDeviceData", L.NewFunction(s.SetDeviceData))
	L.SetGlobal("ControlDevice", L.NewFunction(s.ControlDevice))
	L.SetGlobal("ControlDoor", L.NewFunction(s.ControlDoor))
	L.SetGlobal("GetDoorStatus", L.NewFunction(s.GetDoorStatus))
	L.SetGlobal("ControlVideo", L.NewFunction(s.ControlVideo))
	L.SetGlobal("ExecuteNotification", L.NewFunction(s.ExecuteNotification))
	L.SetGlobal("GetVirtualDeviceData", L.NewFunction(s.GetVirtualDeviceData))
	L.SetGlobal("SetVirtualDeviceData", L.NewFunction(s.SetVirtualDeviceData))
	err := L.DoString(code)
	if err != nil {
		return err
	}
	return nil
}
func (s *ScriptLuaService) LinkScriptDeal(code string) (bool, error) {
	L := lua.NewState()
	defer L.Close()
	L.SetGlobal("Task", L.NewFunction(s.Task))
	L.SetGlobal("GetDeviceData", L.NewFunction(s.GetDeviceData))
	L.SetGlobal("SetDeviceData", L.NewFunction(s.SetDeviceData))
	L.SetGlobal("GetDeviceStatus", L.NewFunction(s.GetDeviceStatus))
	L.SetGlobal("ControlDevice", L.NewFunction(s.ControlDevice))
	L.SetGlobal("ControlDoor", L.NewFunction(s.ControlDoor))
	L.SetGlobal("GetDoorStatus", L.NewFunction(s.GetDoorStatus))
	L.SetGlobal("ControlVideo", L.NewFunction(s.ControlVideo))
	L.SetGlobal("GetVirtualDeviceData", L.NewFunction(s.GetVirtualDeviceData))
	L.SetGlobal("SetVirtualDeviceData", L.NewFunction(s.SetVirtualDeviceData))
	err := L.DoString(code)
	if err != nil {
		return false, err
	}
	luaResult := L.Get(-1)
	if luaResult.Type() == lua.LTBool {
		return luaResult == lua.LTrue, nil
	}

	return false, nil
}
