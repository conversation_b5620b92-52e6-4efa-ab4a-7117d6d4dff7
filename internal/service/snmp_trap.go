package service

import (
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/system/config"

	"go.uber.org/zap"
)

type SnmpTrapService struct {
	snmpRepo *repository.SnmpTrapRepo
	config   *config.Config
	l        *zap.Logger
}

func NewSnmpTrapService(snmpRepo *repository.SnmpTrapRepo, config *config.Config, log *zap.Logger) *SnmpTrapService {
	return &SnmpTrapService{
		snmpRepo: snmpRepo,
		config:   config,
		l:        log.Named("snmptrap_service:"),
	}
}
func (s *SnmpTrapService) Create(param request.CreateSnmpTrap) error {
	var (
		snmp = model.SnmpTrap{
			Name:   param.Name,
			Type:   param.Type,
			Port:   param.Port,
			Ip:     param.Ip,
			Config: param.Config,
		}
	)
	return s.snmpRepo.Create(snmp)
}
func (s *SnmpTrapService) GetSnmpTrapList() ([]model.SnmpTrap, error) {
	return s.snmpRepo.GetSnmpTrapList()
}
func (d *SnmpTrapService) Delete(id int) error {
	return d.snmpRepo.Delete(id)
}
func (s *SnmpTrapService) Update(param request.UpdateSnmpTrap) error {
	var (
		snmp = model.SnmpTrap{
			Name:   param.Name,
			Type:   param.Type,
			Port:   param.Port,
			Ip:     param.Ip,
			Config: param.Config,
		}
	)
	return s.snmpRepo.Update(snmp, param.ID)
}
