package service

import (
	"errors"
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
)

const ()

type BusinessTypeService struct {
	businessTypeRepo *repository.BusinessTypeRepo
	deviceRepo       *repository.DeviceRepo

	defaultPid int
}

func NewBusinessTypeService(
	businessTypeRepo *repository.BusinessTypeRepo,
	deviceRepo *repository.DeviceRepo,
) *BusinessTypeService {
	return &BusinessTypeService{
		businessTypeRepo: businessTypeRepo,
		deviceRepo:       deviceRepo,

		defaultPid: -1,
	}
}

func (b *BusinessTypeService) Create(param request.CreateBusinessType) error {
	var (
		businessType = model.BusinessType{
			Name:     param.Name,
			ParentID: param.ParentID,
			Sort:     param.Sort,
		}
		relations = make([]model.BusinessTypeRelation, 0)
		err       error
	)

	if param.ParentID != 0 {
		relations, err = b.businessTypeRepo.RelationListByChildID(param.ParentID)
		if err != nil {
			return err
		}
	}

	return b.businessTypeRepo.Create(businessType, relations)
}

func (b *BusinessTypeService) TreeOnly() ([]response.BusinessTypeTreeOnly, error) {
	var result []response.BusinessTypeTreeOnly
	list, err := b.businessTypeRepo.All()
	if err != nil {
		return result, err
	}

	result = b.treeOnly(list, b.defaultPid)

	return result, nil
}

func (b *BusinessTypeService) treeOnly(param []model.BusinessType, parentID int) []response.BusinessTypeTreeOnly {
	var result = make([]response.BusinessTypeTreeOnly, 0)
	for _, v := range param {
		if v.ParentID != parentID {
			continue
		}
		var temp = response.BusinessTypeTreeOnly{
			BusinessType: v,
		}
		temp.Children = b.treeOnly(param, v.ID)
		result = append(result, temp)
	}
	return result
}

func (b *BusinessTypeService) ListWithDevice(param request.BusinessTypeTreeWithDevice) ([]response.BusinessTypeListWithDevice, error) {
	var result = make([]response.BusinessTypeListWithDevice, 0)
	list, err := b.businessTypeRepo.ListWithDevicesByPID(param)
	if err != nil {
		return result, err
	}

	result = b.treeWithDevice(list, param.PID)
	return result, nil
}

func (b *BusinessTypeService) treeWithDevice(param []model.BusinessTypeWithDevice, parentID int) []response.BusinessTypeListWithDevice {
	var result = make([]response.BusinessTypeListWithDevice, 0)
	for _, v := range param {
		if v.ParentID != parentID {
			continue
		}
		var temp = response.BusinessTypeListWithDevice{
			BusinessTypeWithDevice: v,
		}
		temp.Children = b.treeWithDevice(param, v.ID)
		if len(temp.Children) == 0 && len(temp.Devices) == 0 {
			continue
		}
		temp.Count = len(v.Devices)
		for _, d := range v.Devices {
			if d.Status != model.DeviceStatusNormal {
				temp.Alarmed = true
				break
			}
		}
		result = append(result, temp)
	}
	return result
}

/*
 */
func (b *BusinessTypeService) Update(param request.UpdateBusinessType) error {
	var businessType = model.BusinessType{
		ID: param.ID,
	}
	if param.Name != "" {
		businessType.Name = param.Name
	}
	if param.Sort != 0 {
		businessType.Sort = param.Sort
	}
	return b.businessTypeRepo.Update(businessType)
}

func (b *BusinessTypeService) Delete(param request.DeleteBusinessType) error {
	_, err := b.deviceRepo.First(model.Device{BusinessTypeID: param.ID})
	if err == nil {
		return errors.New("该业务类型下存在设备")
	}

	_, err = b.businessTypeRepo.First(model.BusinessType{ParentID: param.ID})
	if err == nil {
		return errors.New("该业务类型下存在子业务类型")
	}

	return b.businessTypeRepo.Delete(param.ID)
}

func (b *BusinessTypeService) ListByPID(param request.FindBusinessTypeByPID) ([]model.BusinessType, error) {
	if param.PID == 0 {
		param.PID = b.defaultPid
	}

	return b.businessTypeRepo.FindByPID(param)
}

func (b *BusinessTypeService) ListHasDevice(param request.BusinessTypeListHasDevice) ([]model.BusinessType, error) {
	return b.businessTypeRepo.FindHasDevice(param.ID, param.AreaID, param.UID)
}

func (b *BusinessTypeService) ListByTopLevel(btID int, param request.BusinessTypeByTopLevel) ([]model.BusinessType, error) {
	var filter model.BusinessType
	if param.ID != 0 {
		filter.ID = param.ID
	}
	if param.ParentID != 0 {
		filter.ParentID = param.ParentID
	}

	return b.businessTypeRepo.ListByTopLevel(btID, filter)
}

func (b *BusinessTypeService) CreateByMandate(data []model.BusinessTypeTree, regStr string) error {
	var (
		bts  = make([]model.BusinessType, 0)
		btrs = make([]model.BusinessTypeRelation, 0)
	)
	bts, btrs = b.childNode(data, btrs)
	return b.businessTypeRepo.CreateByMandate(bts, btrs, regStr)
}

func (b *BusinessTypeService) childNode(param []model.BusinessTypeTree, relations []model.BusinessTypeRelation) ([]model.BusinessType, []model.BusinessTypeRelation) {
	var (
		bts  = make([]model.BusinessType, 0)
		btrs = make([]model.BusinessTypeRelation, 0)
	)
	for _, v := range param {
		bts = append(bts, v.BusinessType)
		btrs = append(btrs, model.BusinessTypeRelation{
			ParentID: v.ID,
			ChildID:  v.ID,
		})

		for i := 0; i < len(relations); i++ {
			btrs = append(btrs, model.BusinessTypeRelation{
				ParentID: relations[i].ParentID,
				ChildID:  v.ID,
				Depth:    relations[i].Depth + 1,
			})
		}

		if len(v.Children) == 0 {
			continue
		}

		for i := 0; i < len(relations); i++ {
			relations[i].ChildID = v.ID
			relations[i].Depth++
		}

		relations = append(relations, model.BusinessTypeRelation{
			ParentID: v.ID,
			ChildID:  v.ID,
		})

		nbts, nbtrs := b.childNode(v.Children, relations)

		relations = relations[0 : len(relations)-1]
		for i := 0; i < len(relations); i++ {
			relations[i].Depth--
		}

		bts = append(bts, nbts...)
		btrs = append(btrs, nbtrs...)
	}
	return bts, btrs
}

func (b *BusinessTypeService) TreeOnlyUser(param request.BusinessTypeTreeOnlyUser) ([]response.BusinessTypeTreeOnly, error) {
	list, err := b.businessTypeRepo.FindByUserID(param)
	if err != nil {
		return nil, err
	}

	return b.treeOnly(list, b.defaultPid), nil
}
