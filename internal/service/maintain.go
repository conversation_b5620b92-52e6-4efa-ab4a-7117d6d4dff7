package service

import (
	"io"
	"time"
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/utils"
)

type MaintainService struct {
	maintainRepo *repository.MaintainRepo
}

func NewMaintainService(maintainRepo *repository.MaintainRepo) *MaintainService {
	return &MaintainService{
		maintainRepo: maintainRepo,
	}
}

func (ms *MaintainService) Create(param request.CreateMaintain) error {
	t, err := time.Parse(time.DateTime, param.SignedAt)
	if err != nil {
		return err
	}
	var (
		maintain = model.Maintain{
			DeviceID: param.DeviceId,
			Title:    param.Title,
			Content:  param.Content,
			Amount:   param.Amount,
			Duration: param.Duration,
			SignedAt: t,
		}
		plans = make([]model.MaintainPlan, 0, len(param.ExecuteDate))
	)
	for _, date := range param.ExecuteDate {
		plans = append(plans, model.MaintainPlan{
			ExecuteDate: date,
			Status:      model.MaintainPlanStatusCreated,
		})
	}
	if param.File != nil {
		fd, err := param.File.Open()
		if err != nil {
			return err
		}
		defer fd.Close()
		data, err := io.ReadAll(fd)
		if err != nil {
			return err
		}
		name, err := utils.SaveFile(param.File.Filename, model.StaticContract, data)
		if err != nil {
			return err
		}

		maintain.Contract = &name
	}

	return ms.maintainRepo.Create(maintain, plans)
}

func (ms *MaintainService) List(param request.MaintainList) ([]model.Maintain, int64, error) {
	return ms.maintainRepo.List(param)
}

func (ms *MaintainService) PlanList(param request.MaintainPlanList) ([]response.MaintainPlanList, error) {
	var filter = model.MaintainPlan{
		MaintainID: param.ID,
	}
	if param.Status != 0 {
		filter.Status = param.Status
	}

	return ms.maintainRepo.PlanList(filter)
}

func (ms *MaintainService) Execute(param request.ExecuteMaintainPlan) error {
	now := time.Now()
	var update = model.MaintainPlan{
		ID:            param.PlanID,
		ExecuteUserID: &param.UID,
		Status:        model.MaintainPlanStatusCompanied,
		ExecutedAt:    &now,
	}

	if param.Result != "" {
		update.Result = &param.Result
	}
	if param.Suggestion != "" {
		update.Suggestion = &param.Suggestion
	}
	if param.Comment != "" {
		update.Comment = &param.Comment
	}

	return ms.maintainRepo.Execute(update)
}
