package service

import (
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
)

type TicketService struct {
	ticketRepo *repository.TicketRepo
	alarmRepo  *repository.AlarmRepo
}

func NewTicketService(
	ticketRepo *repository.TicketRepo,
	alarmRepo *repository.AlarmRepo,
) *TicketService {
	return &TicketService{
		ticketRepo: ticketRepo,
		alarmRepo:  alarmRepo,
	}
}

func (t *TicketService) Create(param request.CreateTicket) error {
	var ticket = model.Ticket{
		Title:          param.Title,
		Detail:         param.Detail,
		Priority:       param.Priority,
		CreateUserID:   param.UID,
		AssignedUserID: param.AssignedUserID,
		Phone:          param.Phone,
		Status:         model.TicketStatusCreated,
		Type:           param.Type,
		FlowID:         param.FlowID,
	}
	return t.ticketRepo.Create(param.UID, ticket, param.AlarmID)
}

func (t *TicketService) List(param request.TicketList) ([]model.TicketWithUser, int64, error) {
	var filter model.Ticket
	switch param.Filter {
	case request.TicketListFilterFromMe:
		filter.CreateUserID = param.UID
	case request.TicketListFilterToMe:
		filter.AssignedUserID = param.UID
	}
	if param.Priority != 0 {
		filter.Priority = param.Priority
	}
	if param.ID != 0 {
		filter.ID = param.ID
	}
	return t.ticketRepo.List(param.Page, param.Size, filter)
}

func (t *TicketService) Timeline(param request.TicketTimeline) ([]model.TicketTimelineWithUser, error) {
	return t.ticketRepo.Timeline(param.ID)
}

func (t *TicketService) Update(param request.UpdateTicket) error {
	return t.ticketRepo.Update(param)
}

func (t *TicketService) Detail(param request.TicketDetail) (model.TicketWithUser, error) {
	return t.ticketRepo.Detail(param.ID)
}

func (t *TicketService) UpdateAsset(param request.UpdateTicketAsset) error {
	return t.ticketRepo.UpdateAsset(param)
}
