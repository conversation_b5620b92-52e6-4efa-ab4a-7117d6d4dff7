package service

import (
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/system/permission"
)

type PermissionService struct {
	permissionRepo *repository.PermissionRepo
	permissions    *permission.Permissions
}

func NewPermissionService(
	permissionRepo *repository.PermissionRepo,
	permissions *permission.Permissions,
) *PermissionService {
	return &PermissionService{
		permissionRepo: permissionRepo,
		permissions:    permissions,
	}
}
func (p *PermissionService) Create(permission request.CreatePermission) error {
	id, err := p.permissionRepo.Create(model.Permission{
		Name:   permission.Name,
		Path:   permission.Path,
		Method: permission.Method,
	})
	if err != nil {
		return err
	}

	p.permissions.Add(id, permission.Method, permission.Path)
	return nil
}

func (p *PermissionService) List(param request.PermissionList) ([]model.Permission, int64, error) {
	return p.permissionRepo.List(param)
}

func (p *PermissionService) Update(param request.UpdatePermission) error {
	if param.Method == 0 || param.Path == "" {
		return p.permissionRepo.Update(param.ID, model.Permission{
			Name:   param.Name,
			Path:   param.Path,
			Method: param.Method,
		})
	}
	err := p.permissionRepo.Update(param.ID, model.Permission{
		Name:   param.Name,
		Path:   param.Path,
		Method: param.Method,
	})
	if err != nil {
		return err
	}
	info, err := p.permissionRepo.First(param.ID)
	if err != nil {
		return err
	}

	p.permissions.Delete(info.Method, info.Path)
	p.permissions.Add(param.ID, param.Method, param.Path)
	return nil
}

func (p *PermissionService) Delete(param request.DeletePermission) error {
	info, err := p.permissionRepo.First(param.ID)
	if err != nil {
		return err
	}
	err = p.permissionRepo.Delete(param.ID)
	if err != nil {
		return err
	}
	p.permissions.Delete(info.Method, info.Path)
	return nil
}
