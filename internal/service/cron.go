package service

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os/exec"
	"strconv"
	"time"
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/system/config"
	"tw_platform/pkg/utils"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

type CronService struct {
	scriptService    *ScriptLuaService
	cronTaskService  *CronTaskService
	eventTaskService *EventTaskService
	linkageService   *LinkageService
	videoService     *VideoService
	alarmService     *AlarmService
	alarmRepo        *repository.AlarmRepo
	standbyService   *StandbyService
	r                *redis.Client
	l                *zap.Logger
	dataCacheKey     string
	config           *config.Config
}

func NewCronService(scriptService *ScriptLuaService,
	cronTaskService *CronTaskService,
	eventTaskService *EventTaskService,
	linkageService *LinkageService,
	videoService *VideoService,
	alarmService *AlarmService,
	alarmRepo *repository.AlarmRepo,
	standbyService *StandbyService,
	r *redis.Client,
	config *config.Config,
	log *zap.Logger) *CronService {
	return &CronService{
		scriptService:    scriptService,
		cronTaskService:  cronTaskService,
		eventTaskService: eventTaskService,
		linkageService:   linkageService,
		videoService:     videoService,
		alarmService:     alarmService,
		alarmRepo:        alarmRepo,
		standbyService:   standbyService,
		r:                r,
		config:           config,
		l:                log.Named("cron_service:"),
		dataCacheKey:     "device_data:%d",
	}
}
func (s *CronService) OnceTaskExecute() {
	tasks, err := s.cronTaskService.GetCronTaskAll()
	if err != nil {
		return
	}
	now := time.Now()
	for _, task := range tasks {

		if task.Enabled == "OFF" {
			continue
		}
		//todo 完善
		if task.Condition == nil {
			continue
		}
		if task.Condition.TimeType == "DAY" {
			s.DayTask(now, task)
		}
		if task.Condition.TimeType == "ALWAYS" {
			s.AlwaysTask(now, task)
		}
	}
}
func (s *CronService) NoticeTask(task *model.CronTaskAll) {
	now := time.Now().Format("15:04:02")
	alarmTotal, err := s.alarmRepo.RealtimeTotal()
	if err != nil {
		return
	}
	var (
		text = ""
	)
	for _, notice_id := range task.Action.NoticeGroups {
		title := "系统信息通报"
		if alarmTotal == 0 {
			text += fmt.Sprintf("当前时间%s,任务名称:%s,系统无报警,平台运行正常", now, task.Name)
		} else {
			text += fmt.Sprintf("当前时间%s,任务名称:%s,系统报警数量:%d,系统存在报警,请及时排查", now, task.Name, alarmTotal)
		}

		script := fmt.Sprintf("ExecuteNotification(%d,\"%s\",\"%s\")", notice_id, title, text)
		err := s.scriptService.ScriptDeal(script)
		if err != nil {
			s.l.Error("系统信息通报", zap.Error(err))
		}
	}
}
func (s *CronService) LinkTask(taskid int) {
	linkData, err := s.linkageService.GetLinkageDetail(taskid)
	if err != nil {
		return
	}
	unquotedString, err := strconv.Unquote(linkData.Script)
	if err != nil {
		return
	}
	err = s.scriptService.ScriptDeal(unquotedString)
	if err != nil {
		fmt.Println(err)
		// s.l.Error("", zap.Error(err))
	}
	// now := time.Now().Format("15:04:02")
	// for _, notice_id := range task.Action.NoticeGroups {
	// 	title := "联动任务"
	// 	text := fmt.Sprintf("当前时间%s,任务名称:%s,发生联动操作", now, task.Name)
	// 	script := fmt.Sprintf("ExecuteNotification(%d,\"%s\",\"%s\")", notice_id, title, text)
	// 	err := s.scriptService.ScriptDeal(script)
	// 	if err != nil {
	// 		s.l.Error("", zap.Error(err))
	// 	}
	// }
}
func (s *CronService) DayTask(now time.Time, task model.CronTaskAll) {
	weekday := int(now.Weekday())
	if weekday == 0 {
		weekday = 7
	}
	executiontime, err := parseTimeForToday(task.Condition.Day.ExecutionTime)
	if err != nil {
		return
	}
	if task.Condition.NextExecutionTime.Before(now) {
		if !executiontime.Before(now) {
			return
		}
		task.Condition.NextExecutionTime = time.Date(
			now.Year(),
			now.Month(),
			now.Day(),
			executiontime.Hour(),
			executiontime.Minute(),
			0, 0, now.Location(),
		).Add(24 * time.Hour)
		s.cronTaskService.UpdateTaskNextExecutionTime(task.ID, task.Condition.NextExecutionTime)
		if task.Condition.Day.ExpirationTime > 0 &&
			executiontime.Add(time.Duration(task.Condition.Day.ExpirationTime)*time.Minute).Before(now) {
			return
		}
		//执行任务
		if len(task.Condition.Week) < 1 || contains(task.Condition.Week, int(weekday)) {
			if task.Action.ActionType == "NOTICE" {
				s.NoticeTask(&task)
			}
			if task.Action.ActionType == "LINK" {
				s.LinkTask(task.Action.LinkId)
			}
		}
	} else {
		if !task.Condition.NextExecutionTime.Before(now) {
			return
		}
		if task.Condition.NextExecutionTime.Add(time.Duration(task.Condition.Day.ExpirationTime) * time.Minute).Before(now) {
			task.Condition.NextExecutionTime = time.Date(
				now.Year(),
				now.Month(),
				now.Day(),
				task.Condition.NextExecutionTime.Hour(),
				task.Condition.NextExecutionTime.Minute(),
				0, 0, now.Location(),
			).Add(24 * time.Hour)
			s.cronTaskService.UpdateTaskNextExecutionTime(task.ID, task.Condition.NextExecutionTime)
			return
		}
		//执行任务
		if len(task.Condition.Week) < 1 || contains(task.Condition.Week, int(weekday)) {
			if task.Action.ActionType == "NOTICE" {
				s.NoticeTask(&task)
			}
			if task.Action.ActionType == "LINK" {
				s.LinkTask(task.Action.LinkId)
			}
		}
	}
}
func parseTimeForToday(t string) (time.Time, error) {
	layout := "15:04:05"
	now := time.Now()
	parsedTime, err := time.Parse(layout, t)
	if err != nil {
		return time.Time{}, err
	}
	return time.Date(now.Year(), now.Month(), now.Day(), parsedTime.Hour(), parsedTime.Minute(), parsedTime.Second(), 0, now.Location()), nil
}
func isNextExecutionInTimeSlot(nextExecutionTime time.Time, startTime, endTime time.Time) bool {
	if startTime.Before(endTime) {
		return nextExecutionTime.After(startTime) && nextExecutionTime.Before(endTime)
	} else {
		midnight := time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 23, 59, 59, 0, startTime.Location())
		nextDayStart := time.Date(endTime.Year(), endTime.Month(), endTime.Day(), 0, 0, 0, 0, endTime.Location())
		return (nextExecutionTime.After(startTime) && nextExecutionTime.Before(midnight)) ||
			(nextExecutionTime.After(nextDayStart) && nextExecutionTime.Before(endTime))
	}
}
func contains(s []int, e int) bool {
	for _, i := range s {
		if e == i {
			return true
		}
	}
	return false
}
func (s *CronService) AlwaysTask(now time.Time, task model.CronTaskAll) {
	weekday := int(now.Weekday())
	if weekday == 0 {
		weekday = 7
	}
	if task.Condition.NextExecutionTime.Before(now) {
		if task.Condition.Always.Type == 0 {
			//全时间执行任务
			if len(task.Condition.Week) < 1 || contains(task.Condition.Week, int(weekday)) {
				if task.Action.ActionType == "LINK" {
					s.LinkTask(task.Action.LinkId)
				}
			}
		} else if task.Condition.Always.Type == 1 {
			//时间段执行任务
			startTime, err := parseTimeForToday(task.Condition.Always.TimeSlot.StartTime)
			if err != nil {
				s.l.Error("", zap.Error(err))
			}

			endTime, err := parseTimeForToday(task.Condition.Always.TimeSlot.EndTime)
			if err != nil {
				s.l.Error("", zap.Error(err))
			}
			if isNextExecutionInTimeSlot(now, startTime, endTime) {
				if len(task.Condition.Week) < 1 || contains(task.Condition.Week, int(weekday)) {
					if task.Action.ActionType == "LINK" {
						s.LinkTask(task.Action.LinkId)
					}
				}
			}

		}
		task.Condition.NextExecutionTime = time.Date(
			now.Year(),
			now.Month(),
			now.Day(),
			now.Hour(),
			now.Minute(),
			0, 0, now.Location(),
		).Add(time.Duration(task.Condition.Always.Cycle) * time.Minute)
		s.cronTaskService.UpdateTaskNextExecutionTime(task.ID, task.Condition.NextExecutionTime)
	}
}
func (s *CronService) EventExecute() {
	tasks, err := s.eventTaskService.GetEventTaskAll()
	if err != nil {
		return
	}
	for _, task := range tasks {

		if task.Enabled == "OFF" {
			continue
		}
		if task.Condition == nil {
			//直接执行动作
		} else {
			if len(task.Condition.InputSignals) != len(task.Condition.Options)+1 {
				s.l.Error("事件条件和运算符不对等?")
				continue
			}
			scriptContent := "return "
			//判断条件
			for index, input := range task.Condition.InputSignals {
				if input.Type == "device" {
					if input.Argid >= 0 {
						scriptContent = scriptContent + fmt.Sprintf(" GetDeviceData(%d,%d)%s%d ",
							input.Devid,
							input.Argid,
							input.Option,
							input.CompareValue,
						)
					} else {
						if input.Argid == -1 {
							//设备正常状态
							scriptContent = scriptContent + fmt.Sprintf(" GetDeviceStatus(%d)%s%d ",
								input.Devid,
								"==",
								1,
							)
						} else if input.Argid == -2 {
							//设备报警状态
							scriptContent = scriptContent + fmt.Sprintf(" GetDeviceStatus(%d)%s%d ",
								input.Devid,
								"==",
								2,
							)
						}
					}

				} else if input.Type == "time" {
					//todo 增加时间条件判断
					scriptContent = scriptContent + " true "
				}
				if len(task.Condition.Options) > 0 && index != len(task.Condition.InputSignals)-1 {
					scriptContent = scriptContent + task.Condition.Options[index]
				}

			}
			result, err := s.scriptService.LinkScriptDeal(scriptContent)
			if err != nil {
				s.l.Error(err.Error())
				continue
			}
			if result {
				//条件满足 没有执行过 执行联动
				if !task.Condition.HasActioned {
					s.LinkTask(task.Action.LinkId)
					s.eventTaskService.UpdateTaskStatus(task.ID, true)
					s.l.Info(fmt.Sprintf("%s,执行联动", task.Name))
				}
			} else {
				//条件不满足 事件复位
				if task.Condition.HasActioned {
					s.eventTaskService.UpdateTaskStatus(task.ID, false)
					s.l.Info(fmt.Sprintf("%s,条件不满足，事件转到恢复状态", task.Name))
				}
			}
		}
	}
}
func (c *CronService) Fortest() {
	fmt.Println(time.Now().Format(time.DateTime))
}

// VideoCaptureTaskExecute 执行摄像头抓拍任务 图片通过socket传输到前端首页显示
func (s *CronService) VideoCaptureTaskExecute() {
	var (
		deviceDataList []request.DeviceData

		rtsp      string
		err       error
		videoList []response.VideoListResponse
	)
	//rtsp := ""
	params := []string{}
	videoList, err = s.videoService.GetVideoList()
	if err != nil {
		return
	}

	for i := 0; i < len(videoList); i++ {
		var unit []request.DeviceDataUnit
		videoConfig := model.VideoDevice{
			Brand:     videoList[i].Brand,
			ChannelID: videoList[i].ChannelID,
			IP:        videoList[i].IP,
			Name:      videoList[i].Name,
			Password:  videoList[i].Password,
			Stream:    videoList[i].Stream,
		}

		expr := model.DeviceConfig{}
		expr.VideoDevice = &videoConfig
		switch videoList[i].Type {
		case "camera":
			rtsp = GenerateRtsp(expr, videoList[i].ChannelID)
		case "nvr":
			channelId := videoList[i].ChannelID
			switch videoList[i].Brand {
			case "海康":
				//使用主码流
				subChStr := ""
				if videoList[i].Stream == "main" {
					subChStr = strconv.Itoa(channelId) + "01"
				} else {
					subChStr = strconv.Itoa(channelId) + "02"
				}
				rtsp = fmt.Sprintf(`rtsp://%s:%s@%s/%s`, videoList[i].Name, videoList[i].Password, videoList[i].IP+":554", "Streaming/Channels/"+subChStr)
			case "大华":
				// rtsp://admin:dahua123@***********:554/cam/realmonitor?channel=2&subtype=0
				//username: 用户名。例如admin。
				//password: 密码。例如admin。
				//ip: 为设备IP。例如 **********。
				//port: 端口号默认为554，若为默认可不填写。
				//channel: 通道号，起始为1。例如通道2，则为channel=2。
				//subtype: 码流类型，主码流为0（即subtype=0），辅码流为1（即subtype=1）
				suffix := ""
				if videoList[i].Stream == "main" {
					suffix = fmt.Sprintf(`cam/realmonitor?channel=%d&subtype=0`, channelId)
				} else {
					suffix = fmt.Sprintf(`cam/realmonitor?channel=%d&subtype=1`, channelId)
				}
				rtsp = fmt.Sprintf(`rtsp://%s:%s@%s/%s`, videoList[i].Name, videoList[i].Password, videoList[i].IP+":554", suffix)
			case "华为":
				//rtsp://admin:HuaWei123@************:443/rtsp/streaming?channel=3&subtype=0&metadata=true
				suffix := ""
				if videoList[i].Stream == "main" {
					suffix = fmt.Sprintf(`rtsp/streaming?channel=%d&subtype=0&metadata=true`, channelId)
				} else {
					suffix = fmt.Sprintf(`rtsp/streaming?channel=%d&subtype=1&metadata=true`, channelId)
				}
				rtsp = fmt.Sprintf(`rtsp://%s:%s@%s/%s`, videoList[i].Name, videoList[i].Password, videoList[i].IP+":443", suffix)
			case "宇视":
				//rtsp://用户名:密码@ip:port/unicast/c<channel number>/s<stream type>/live
				//<channel number>: 1-n
				//<stream type>: 0（主流），1（辅流）
				//通道1主码流示例： rtsp://admin:admin@***********:554/unicast/c1/s0/live
				suffix := ""
				if videoList[i].Stream == "main" {
					suffix = "0"
				} else {
					suffix = "1"
				}
				rtsp = fmt.Sprintf(`rtsp://%s:%s@%s:554/unicast/c%d/s%s/live`, videoList[i].Name, videoList[i].Password, videoList[i].IP, channelId, suffix)
			}
		default:
			rtsp = " "
		}
		//s.l.Debug("rtsp", zap.String("rtsp", rtsp))
		params = []string{
			"-i",
			rtsp,
			"-r",
			"1",
			"-ss",
			"00:00:00",
			"-t",
			"00:00:01",
			"-s", s.config.VideoResolution.Resolution,
			"-f",
			"image2",
			"-",
		}

		cmd := exec.Command("ffmpeg", params...)
		var out bytes.Buffer
		cmd.Stdout = &out
		err = cmd.Start()
		if err != nil {
			continue
		}
		err = cmd.Wait()
		if err != nil {
			continue
		}
		//s.l.Debug("cmd", zap.Any("params", params))
		imgBase64 := base64.StdEncoding.EncodeToString(out.Bytes())
		unit = append(unit, request.DeviceDataUnit{
			Point:  1,
			Value:  imgBase64,
			Status: 1,
		})
		deviceDataList = append(deviceDataList, request.DeviceData{
			Type:   videoList[i].Type,
			ID:     videoList[i].ID,
			Status: 1,
			Units:  unit,
		})

	}
	ctx, cancel := utils.RedisCtx()
	defer cancel()

	_, err = s.r.TxPipelined(ctx, func(pipe redis.Pipeliner) error {
		for k := 0; k < len(deviceDataList); k++ {
			cache, errs := json.Marshal(deviceDataList[k])
			if errs != nil {
				continue
			}
			pipe.Set(ctx, fmt.Sprintf(s.dataCacheKey, deviceDataList[k].ID), cache,
				time.Hour*24)
		}
		return nil
	})
	if err != nil {
		return
	}

}

func (s *CronService) ExportAlarm() {
	err := s.alarmService.Generate()
	if err != nil {
		s.l.Warn("export alarm failure", zap.Error(err))
	}
}

func (s *CronService) StandbyDeviceData() {
	err := s.standbyService.FetchDeviceData()
	if err != nil {
		s.l.Warn("fetch standby device data failure", zap.Error(err))
	}
}
