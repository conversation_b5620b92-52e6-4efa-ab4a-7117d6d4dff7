package service

import (
	"fmt"
	"os"
	"strconv"

	"go.uber.org/zap"
	"gopkg.in/yaml.v3"
)

type MediaMtxService struct {
	videoService *VideoService
	l            *zap.Logger
}

func NewMediaMtxService(videoService *VideoService, log *zap.Logger) *MediaMtxService {
	return &MediaMtxService{
		videoService: videoService,
		l:            log.Named("mediamtx_service:"),
	}
}

// 生成视频流通道
func (m *MediaMtxService) GenerateMedia() error {
	list, err := m.videoService.GetVideoList()
	if err != nil {
		return err
	}

	confPath := "mediamtx/mediamtx.yml"

	data, err := os.ReadFile(confPath)
	if err != nil {
		return err
	}
	var rootNode yaml.Node
	err = yaml.Unmarshal(data, &rootNode)
	if err != nil {
		return err
	}
	var pathsNode *yaml.Node
	for i := 0; i < len(rootNode.Content[0].Content); i += 2 {
		key := rootNode.Content[0].Content[i]
		value := rootNode.Content[0].Content[i+1]
		if key.Value == "paths" {
			pathsNode = value
			break
		}
	}
	if pathsNode == nil {
		pathsNode = &yaml.Node{
			Kind: yaml.MappingNode,
			Tag:  "!!map",
		}
		rootNode.Content[0].Content = append(rootNode.Content[0].Content,
			&yaml.Node{Kind: yaml.ScalarNode, Value: "paths"},
			pathsNode,
		)
	}
	pathsNode.Content = nil
	for _, video := range list {
		if video.Type == "camera" {
			rtsp := ""
			streamStr := ""
			subid := video.ChannelID + 1
			switch video.Brand {
			case "海康":
				if video.Stream == "main" {
					streamStr = "main"
				} else {
					streamStr = "sub"
				}
				rtsp = fmt.Sprintf(`rtsp://%s:%s@%s/h264/ch%d/%s/av_stream`, video.Name, video.Password, video.IP+":554", subid, streamStr)
			case "大华":
				if video.Stream == "main" {
					streamStr = "subtype=0"
				} else {
					streamStr = "subtype=1"
				}
				rtsp = fmt.Sprintf(`rtsp://%s:%s@%s/cam/realmonitor?channel=%d&%s`, video.Name, video.Password, video.IP+":554", subid, streamStr)
			case "华为":
				if video.Stream == "main" {
					streamStr = "Media1"
				} else {
					streamStr = "Media2"
				}
				rtsp = fmt.Sprintf(`rtsp://%s:%s@%s/LiveMedia/ch%d/%s`, video.Name, video.Password, video.IP, subid, streamStr)
			case "宇视":
				//rtsp://admin:admin@***********:554/video1
				if video.Stream == "main" {
					streamStr = "video1"
				} else {
					streamStr = "video2"
				}
				rtsp = fmt.Sprintf(`rtsp://%s:%s@%s:554/%s`, video.Name, video.Password, video.IP, streamStr)
			default:
				rtsp = ""
			}
			newPathKey := &yaml.Node{
				Kind:  yaml.ScalarNode,
				Value: fmt.Sprintf("media/%d", video.ID),
				Tag:   "!!str",
			}
			newPathValue := &yaml.Node{
				Kind: yaml.MappingNode,
				Tag:  "!!map",
				Content: []*yaml.Node{
					{Kind: yaml.ScalarNode, Value: "source"},
					{Kind: yaml.ScalarNode, Value: rtsp},
				},
			}
			pathsNode.Content = append(pathsNode.Content, newPathKey, newPathValue)
		} else if video.Type == "nvr" {
			channelId := video.ChannelID
			rtsp := ""
			switch video.Brand {

			case "海康":
				//使用主码流
				subChStr := ""
				if video.Stream == "main" {
					subChStr = strconv.Itoa(channelId) + "01"
				} else {
					subChStr = strconv.Itoa(channelId) + "02"
				}
				rtsp = fmt.Sprintf(`rtsp://%s:%s@%s/%s`, video.Name, video.Password, video.IP+":554", "Streaming/Channels/"+subChStr)
			case "大华":
				// rtsp://admin:dahua123@***********:554/cam/realmonitor?channel=2&subtype=0
				//username: 用户名。例如admin。
				//password: 密码。例如admin。
				//ip: 为设备IP。例如 **********。
				//port: 端口号默认为554，若为默认可不填写。
				//channel: 通道号，起始为1。例如通道2，则为channel=2。
				//subtype: 码流类型，主码流为0（即subtype=0），辅码流为1（即subtype=1）
				suffix := ""
				if video.Stream == "main" {
					suffix = fmt.Sprintf(`cam/realmonitor?channel=%d&subtype=0`, channelId)
				} else {
					suffix = fmt.Sprintf(`cam/realmonitor?channel=%d&subtype=1`, channelId)
				}
				rtsp = fmt.Sprintf(`rtsp://%s:%s@%s/%s`, video.Name, video.Password, video.IP+":554", suffix)
			case "华为":
				//rtsp://admin:HuaWei123@************:443/rtsp/streaming?channel=3&subtype=0&metadata=true
				suffix := ""
				if video.Stream == "main" {
					suffix = fmt.Sprintf(`rtsp/streaming?channel=%d&subtype=0&metadata=true`, channelId)
				} else {
					suffix = fmt.Sprintf(`rtsp/streaming?channel=%d&subtype=1&metadata=true`, channelId)
				}
				rtsp = fmt.Sprintf(`rtsp://%s:%s@%s/%s`, video.Name, video.Password, video.IP+":443", suffix)
			case "宇视":
				//rtsp://用户名:密码@ip:port/unicast/c<channel number>/s<stream type>/live
				//<channel number>: 1-n
				//<stream type>: 0（主流），1（辅流）
				//通道1主码流示例： rtsp://admin:admin@***********:554/unicast/c1/s0/live
				suffix := ""
				if video.Stream == "main" {
					suffix = "0"
				} else {
					suffix = "1"
				}
				rtsp = fmt.Sprintf(`rtsp://%s:%s@%s:554/unicast/c%d/s%s/live`, video.Name, video.Password, video.IP, channelId, suffix)
			}
			newPathKey := &yaml.Node{
				Kind:  yaml.ScalarNode,
				Value: fmt.Sprintf("%d", video.ID),
				Tag:   "!!str",
			}
			newPathValue := &yaml.Node{
				Kind: yaml.MappingNode,
				Tag:  "!!map",
				Content: []*yaml.Node{
					{Kind: yaml.ScalarNode, Value: "source"},
					{Kind: yaml.ScalarNode, Value: rtsp},
				},
			}
			pathsNode.Content = append(pathsNode.Content, newPathKey, newPathValue)
		}
	}
	newData, err := yaml.Marshal(&rootNode)
	if err != nil {
		return err
	}

	err = os.WriteFile(confPath, newData, 0644)
	if err != nil {
		return err
	}
	return nil
}

// 配置turn
func (m *MediaMtxService) SetTurn(url, username, password string) error {
	data, err := os.ReadFile("mediamtx.yml")
	if err != nil {
		return fmt.Errorf("error reading file: %w", err)
	}

	var node yaml.Node
	if err := yaml.Unmarshal(data, &node); err != nil {
		return fmt.Errorf("error unmarshaling YAML: %w", err)
	}
	found := false

	if len(node.Content) > 0 {
		root := node.Content[0]
		for i := 0; i < len(root.Content); i += 2 {
			if i+1 >= len(root.Content) {
				break
			}

			keyNode := root.Content[i]
			valueNode := root.Content[i+1]

			if keyNode.Value == "webrtcICEServers2" {
				found = true

				serverNode := &yaml.Node{
					Kind: yaml.MappingNode,
					Tag:  "!!map",
				}

				serverNode.Content = append(serverNode.Content,
					&yaml.Node{Kind: yaml.ScalarNode, Value: "url"},
					&yaml.Node{
						Kind:  yaml.ScalarNode,
						Tag:   "!!str",
						Value: url,
					},
				)
				serverNode.Content = append(serverNode.Content,
					&yaml.Node{Kind: yaml.ScalarNode, Value: "username"},
					&yaml.Node{
						Kind:  yaml.ScalarNode,
						Tag:   "!!str",
						Value: username,
					},
				)

				serverNode.Content = append(serverNode.Content,
					&yaml.Node{Kind: yaml.ScalarNode, Value: "password"},
					&yaml.Node{
						Kind:  yaml.ScalarNode,
						Tag:   "!!str",
						Value: password,
					},
				)

				sequenceNode := &yaml.Node{
					Kind:    yaml.SequenceNode,
					Tag:     "!!seq",
					Content: []*yaml.Node{serverNode},
				}
				valueNode.Content = sequenceNode.Content
				break
			}
		}
	}

	if !found {
		return fmt.Errorf("webrtcICEServers2 not found in config")
	}

	outputFile, err := os.Create("mediamtx.yml")
	if err != nil {
		return fmt.Errorf("error creating output file: %w", err)
	}
	defer outputFile.Close()

	encoder := yaml.NewEncoder(outputFile)
	encoder.SetIndent(2)
	if err := encoder.Encode(&node); err != nil {
		return fmt.Errorf("error encoding YAML: %w", err)
	}

	return nil
}
