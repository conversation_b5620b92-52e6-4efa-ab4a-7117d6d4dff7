package service

import (
	"archive/zip"
	"fmt"
	"os"
	"path"
	"path/filepath"
	"sync"
	"time"
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/system/version"

	"golang.org/x/sync/errgroup"
)

type SystemService struct {
	systemRepo   *repository.SystemRepo
	deviceRepo   *repository.DeviceRepo
	alarmRepo    *repository.AlarmRepo
	ticketRepo   *repository.TicketRepo
	btRepo       *repository.BusinessTypeRepo
	gatewayRepoo *repository.GatewayRepo
	inspectRepo  *repository.InspectRepo
	startAt      time.Time
	defaultID    int
	firstBTPID   int
	version      *version.Version
}

func NewSystemService(
	systemRepo *repository.SystemRepo,
	devicerepo *repository.DeviceRepo,
	alarmRepo *repository.AlarmRepo,
	ticketRepo *repository.TicketRepo,
	btRepo *repository.BusinessTypeRepo,
	gatewayRepo *repository.GatewayRepo,
	inspectRepo *repository.InspectRepo,
	version *version.Version,
) *SystemService {
	return &SystemService{
		systemRepo:   systemRepo,
		deviceRepo:   devicerepo,
		alarmRepo:    alarmRepo,
		ticketRepo:   ticketRepo,
		btRepo:       btRepo,
		gatewayRepoo: gatewayRepo,
		inspectRepo:  inspectRepo,
		startAt:      time.Now(),
		defaultID:    1,
		firstBTPID:   -1,
		version:      version,
	}
}

func (s *SystemService) SystemInfo() (model.SystemInfo, error) {
	return s.systemRepo.First(s.defaultID)
}

func (s *SystemService) Update(param request.UpdateSystem) error {
	var info = model.SystemInfo{
		ID:        s.defaultID,
		Name:      param.Name,
		Copyright: param.Copyright,
	}

	return s.systemRepo.Update(info)
}

func (s *SystemService) UpgradePages(param request.UpdateWebPage) error {
	var (
		eg = new(errgroup.Group)
	)
	fd, err := param.File.Open()
	if err != nil {
		return err
	}
	reader, err := zip.NewReader(fd, param.File.Size)
	if err != nil {
		return err
	}
	eg.SetLimit(10)
	for _, f := range reader.File {
		temp := f
		eg.Go(func() error {
			return s.createFile(temp, param.Path)
		})
	}
	err = eg.Wait()
	if err != nil {
		return err
	}
	return err
}
func (s *SystemService) createFile(fd *zip.File, dstDir string) error {
	filePath := path.Join(dstDir, fd.Name)

	if fd.FileInfo().IsDir() {
		return os.MkdirAll(filepath.Join(dstDir, fd.Name), 0755)
	}

	err := os.MkdirAll(filepath.Dir(filePath), 0755)
	if err != nil {
		return err
	}

	f, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer f.Close()

	err = f.Chmod(0644)
	if err != nil {
		return err
	}

	rc, err := fd.Open()
	if err != nil {
		return err
	}
	defer rc.Close()

	_, err = f.ReadFrom(rc)

	return err
}

func (s *SystemService) SSEUpgradePages(param request.UpdateWebPage) (chan response.UpdateWebPage, int, error) {
	var (
		ch    = make(chan response.UpdateWebPage, 10)
		limit = make(chan struct{}, 10)
		total int
		wg    sync.WaitGroup
	)
	fd, err := param.File.Open()
	if err != nil {
		return ch, total, err
	}
	reader, err := zip.NewReader(fd, param.File.Size)
	if err != nil {
		return ch, total, err
	}
	total = len(reader.File)
	go func() {
		for _, f := range reader.File {
			wg.Add(1)
			limit <- struct{}{}
			go s.sseCreateFile(ch, limit, &wg, f, param.Path)
		}
		wg.Wait()
		close(ch)
	}()

	return ch, total, nil
}

func (s *SystemService) sseCreateFile(
	ch chan<- response.UpdateWebPage,
	limit <-chan struct{},
	wg *sync.WaitGroup,
	fd *zip.File,
	path string,
) {
	var (
		resp response.UpdateWebPage
	)
	defer func() {
		wg.Done()
		<-limit
	}()

	if fd.FileInfo().IsDir() {
		err := os.MkdirAll(fmt.Sprint(path, fd.Name), os.ModePerm&1755)
		resp.Err = err
		ch <- resp
		return
	}

	f, err := os.Create(fmt.Sprint(path, fd.Name))
	if err != nil {
		resp.Err = err
		ch <- resp
		return
	}
	defer f.Close()

	err = f.Chmod(os.ModePerm & 0644)
	if err != nil {
		resp.Err = err
		ch <- resp
		return
	}

	rc, err := fd.Open()
	if err != nil {
		resp.Err = err
		ch <- resp
		return
	}
	defer rc.Close()

	_, err = f.ReadFrom(rc)
	if err != nil {
		resp.Err = err
		ch <- resp
		return
	}

	ch <- resp
}

func (s *SystemService) UpgradeLogo(param request.UpgradeLogo) error {
	fd, err := param.File.Open()
	if err != nil {
		return err
	}
	defer fd.Close()

	f, err := os.Create(fmt.Sprint(model.StaticSystem, "logo.png"))
	if err != nil {
		return err
	}
	defer f.Close()
	err = f.Chmod(os.ModePerm & 0644)
	if err != nil {
		return err
	}

	_, err = f.ReadFrom(fd)

	return nil
}

func (s *SystemService) Statistic(param request.SystemStatistic) (response.Statistic, error) {
	var (
		result response.Statistic
	)
	deviceTotal, err := s.deviceRepo.Total(param.UID, param.BusinessTypeID)
	if err != nil {
		return result, err
	}
	alarmTotal, err := s.alarmRepo.RealtimeTotalByUser(param.TokenInfo)
	if err != nil {
		return result, err
	}
	ticketTotal, err := s.ticketRepo.Count(model.Ticket{
		AssignedUserID: param.UID,
		Status:         model.TicketStatusCreated,
	})
	if err != nil {
		return result, err
	}
	result.DeviceTotal = deviceTotal
	result.AlarmTotal = alarmTotal
	result.TicketTotal = ticketTotal
	result.Duration = time.Duration(time.Since(s.startAt).Seconds())

	return result, nil
}

func (s *SystemService) VersionInfo() response.VersionInfo {
	return response.VersionInfo{
		CommitID:  s.version.CommitID,
		BuildDate: s.version.BuildDate,
	}
}

func (s *SystemService) StatisticApp(param request.SystemStatisticApp) (response.SystemStatisticApp, error) {
	var (
		result response.SystemStatisticApp
	)
	deviceTotal, err := s.deviceRepo.Total(param.UID, 0)
	if err != nil {
		return result, err
	}
	alarmTotal, err := s.alarmRepo.RealtimeTotalByUser(param.TokenInfo)
	if err != nil {
		return result, err
	}
	ticketTotal, err := s.ticketRepo.Count(model.Ticket{
		AssignedUserID: param.UID,
		Status:         model.TicketStatusCreated,
	})
	if err != nil {
		return result, err
	}

	btTotal, err := s.btRepo.CountByPID(param.UID, s.firstBTPID)
	if err != nil {
		return result, err
	}

	gatewayTotal, err := s.gatewayRepoo.CountByUID(param.UID)
	if err != nil {
		return result, err
	}
	inspectTotal, err := s.inspectRepo.TotalByUser(param.UID)
	if err != nil {
		return result, err
	}
	result.DeviceTotal = deviceTotal
	result.AlarmTotal = alarmTotal
	result.TicketTotal = ticketTotal
	result.SubSystemTotal = btTotal
	result.GatewayTotal = gatewayTotal
	result.InspectTotal = inspectTotal
	return result, nil
}
