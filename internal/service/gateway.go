package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net"
	"regexp"
	"strings"
	"sync"
	"time"
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/opcua/scanner"
	"tw_platform/pkg/system/mqtt"
	"tw_platform/pkg/utils"

	"github.com/gopcua/opcua/ua"
	jsoniter "github.com/json-iterator/go"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type GatewayService struct {
	gatewayRepo   *repository.GatewayRepo
	deviceRepo    *repository.DeviceRepo
	alarmService  *AlarmService
	alarmGroup    *AlarmGroupService
	deviceService *DeviceService
	alarmLogRepo  *repository.LogRepo
	driverRepo    *repository.DriverRepo
	btRepo        *repository.BusinessTypeRepo
	mqttModule    *mqtt.MqttModule
	l             *zap.Logger
	scanPort      int
	ntpTopic      string
}

func NewGatewayService(
	gatewayRepo *repository.GatewayRepo,
	deviceRepo *repository.DeviceRepo,
	alarmService *AlarmService,
	alarmGroup *AlarmGroupService,
	DeviceService *DeviceService,
	alarmLogRepo *repository.LogRepo,
	dirverRepo *repository.DriverRepo,
	btRepo *repository.BusinessTypeRepo,
	mqttModule *mqtt.MqttModule,
	l *zap.Logger,
) *GatewayService {
	return &GatewayService{
		gatewayRepo:   gatewayRepo,
		deviceRepo:    deviceRepo,
		alarmService:  alarmService,
		alarmGroup:    alarmGroup,
		deviceService: DeviceService,
		alarmLogRepo:  alarmLogRepo,
		driverRepo:    dirverRepo,
		btRepo:        btRepo,
		mqttModule:    mqttModule,
		l:             l.Named("gateway_service"),
		scanPort:      45454,
		ntpTopic:      "tw/%s/ntp/sub",
	}
}

func (g *GatewayService) Create(param request.CreateGateway) error {
	var (
		gateway = model.GatewayWithPorts{
			Gateway: model.Gateway{
				Name:    param.Name,
				IP:      &param.IP,
				Version: param.Version,
				Type:    param.Type,
				Model:   param.Model,
				Status:  model.GatewayStatusOnline,
			},
			Ports: make([]model.GatewayPort, 0, len(param.Ports)),
		}
	)
	for i := 0; i < len(param.Ports); i++ {
		gateway.Ports = append(gateway.Ports, model.GatewayPort{
			Name: param.Ports[i].Name,
			Port: param.Ports[i].Port,
			Type: param.Ports[i].Type,
		})
	}
	id, err := g.gatewayRepo.Create(gateway)
	if err != nil {
		return err
	}
	if param.Type == model.GatewayTypeOPC {
		go func() {
			err = g.OPCCreate(id, param.IP, param.Ports[0].Port)
			if err != nil {
				return
			}
		}()
	}

	return err
}

func (g *GatewayService) Update(param request.UpdateGateway) error {
	var gateway = model.Gateway{
		Name:  param.Name,
		IP:    &param.IP,
		Model: param.Model,
	}
	return g.gatewayRepo.Update(
		model.Gateway{ID: param.ID},
		gateway,
	)
}

func (g *GatewayService) RegisterPceM(param request.RegisterPceM) error {
	var (
		gateway = model.Gateway{
			Name:    fmt.Sprintf("网关_%s", param.FlashID[len(param.FlashID)-4:]),
			FlashID: &param.FlashID,
			Type:    model.GatewayTypePceM,
			Model:   param.Model,
			Status:  model.GatewayStatusOnline,
			Version: param.Version,
			IP:      &param.IP,
		}
		gwp = model.GatewayWithPorts{
			Gateway: gateway,
			Ports:   make([]model.GatewayPort, 0, len(param.Ports)),
		}
	)

	for _, port := range param.Ports {
		gwp.Ports = append(gwp.Ports, model.GatewayPort{
			Port: port.Port,
			Name: port.Name,
			Type: port.Type,
		})
	}

	_, err := g.gatewayRepo.Create(gwp)
	if !errors.Is(err, gorm.ErrDuplicatedKey) && err != nil {
		return err
	}
	gateway = model.Gateway{
		Version: param.Version,
		IP:      &param.IP,
	}
	return g.gatewayRepo.Update(
		model.Gateway{FlashID: &param.FlashID},
		gateway,
	)
}

func (g *GatewayService) List(page, size int, param request.GatewayList) ([]model.GatewayWithPorts, int64, error) {
	var filter model.Gateway
	if param.Type != 0 {
		filter.Type = param.Type
	}
	if param.Model != "" {
		filter.Model = param.Model
	}
	list, total, err := g.gatewayRepo.ListWithPortsPage(page, size, filter)
	if err != nil {
		return nil, 0, err
	}

	for i := 0; i < len(list); i++ {
		list[i].PortTotal = len(list[i].Ports)
	}

	return list, total, nil
}

func (g *GatewayService) ListWithDevices() ([]model.GatewayPortDevices, error) {
	return g.gatewayRepo.ListWithDevices(model.Gateway{Type: model.GatewayTypeModbus})
}
func (g *GatewayService) ListWithOpcDevices() ([]model.GatewayPortDevices, error) {
	return g.gatewayRepo.ListWithDevices(model.Gateway{Type: model.GatewayTypeOPC})
}
func (g *GatewayService) IsAcceptMulti(param request.IsAcceptMulti) []response.IsAcceptMulti {
	var (
		result    = make(map[string]response.IsAcceptMulti, len(param.Ports))
		resultArr = make([]response.IsAcceptMulti, 0, len(param.Ports))
		wg        sync.WaitGroup
		mutex     sync.Mutex
	)

	for _, port := range param.Ports {
		result[port] = response.IsAcceptMulti{
			Port: port,
		}
	}
	wg.Add(len(param.Ports))

	for _, port := range param.Ports {
		go func(port string) {
			defer wg.Done()
			temp := response.IsAcceptMulti{
				Port:      port,
				Connected: utils.IsAccept(param.IP, port),
			}
			mutex.Lock()
			result[port] = temp
			mutex.Unlock()
		}(port)
	}

	wg.Wait()

	for _, v := range result {
		resultArr = append(resultArr, v)
	}

	return resultArr
}

func (g *GatewayService) UpdatePort(param request.UpdateGatewayPort) error {
	var port = model.GatewayPort{
		Name: param.Name,
		Port: param.Port,
		Type: param.Type,
	}

	return g.gatewayRepo.UpdatePort(param.ID, port)
}

func (g *GatewayService) AddPort(param request.AddGatewayPort) error {
	var port = model.GatewayPort{
		GatewayID: param.ID,
		Name:      param.Name,
		Port:      param.Port,
		Type:      param.Type,
	}
	return g.gatewayRepo.AddPort(port)
}

func (g *GatewayService) DeletePort(param request.DeleteGatewayPort) error {
	_, err := g.deviceRepo.First(model.Device{PortID: &param.ID})
	if err == nil {
		return errors.New("该端口下存在设备")
	}
	if err != gorm.ErrRecordNotFound {
		return err
	}
	return g.gatewayRepo.DeletePort(param.ID)
}

func (g *GatewayService) PortList(param request.GatewayPortList) ([]model.GatewayPort, error) {
	return g.gatewayRepo.PortList(param.ID)
}

func (g *GatewayService) Delete(param request.DeleteGateway) error {
	_, err := g.deviceRepo.FirstByGateway(param.ID)
	if err == nil {
		return errors.New("该网关下存在设备")
	}
	if err != gorm.ErrRecordNotFound {
		return err
	}
	return g.gatewayRepo.Delete(param.ID)
}

func (g *GatewayService) SetStatus(id int, status model.GatewayStatus) error {
	var (
		err      error
		alarmErr error
	)

	ok := g.gatewayRepo.GetAlarmLock(id, status)
	if !ok {
		return nil
	}

	switch status {
	case model.GatewayStatusOffline:
		gateway, err := g.gatewayRepo.First(model.Gateway{ID: id})
		if err != nil {
			return err
		}
		alarm := model.Alarm{
			Type:        model.AlarmTypeGateway,
			Status:      model.AlarmStatusHappened,
			GatewayID:   &id,
			GatewayName: &gateway.Name,
			Level:       "严重",
			Message:     fmt.Sprintf("网关[%s]离线", gateway.Name),
		}
		alarmErr = g.alarmService.Create(alarm, "")
		if alarmErr != nil {
			err = g.alarmService.Publish(response.AlarmListPayload{
				Alarm: alarm,
				Type:  model.AlarmStatusHappened,
				Path:  "",
			})
			if err != nil {
				g.l.Warn("publish gateway alarm happened error", zap.Error(err))
			}
		}

	case model.GatewayStatusOnline:
		alarm := model.Alarm{
			GatewayID: &id,
		}
		alarmErr = g.alarmService.Recovery1(alarm, "")
		if alarmErr != nil {
			err = g.alarmService.Publish(response.AlarmListPayload{
				Alarm: alarm,
				Type:  model.AlarmStatusRecovered,
				Path:  "",
			})
			if err != nil {
				g.l.Warn("publish gateway alarm recovered error", zap.Error(err))
			}
		}
	default:
		return errors.New("不支持的网关状态")
	}

	//更新错误时不影响发送提醒
	if alarmErr != nil {
		g.alarmLogRepo.CreateAlarmLog(
			[]model.AlarmLog{{
				GatewayID: &id,
				Event:     "网关状态更新错误",
				Message:   err.Error(),
			}},
		)
	}

	g.alarmGroup.SendGateWayAlarm(id, status)

	if status == model.GatewayStatusOnline {
		return nil
	}

	binding, err := g.gatewayRepo.GetAlarmBinding(id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil
		}
		zap.L().Error("get gateway alarm binding error", zap.Error(err))
	}

	bindingCache, err := g.deviceRepo.DOUnit(binding.BindingID)
	if err != nil {
		g.l.Error("get binding cache error", zap.Error(err))
		return nil
	}

	err = g.deviceService.callDOAlarm(binding, bindingCache)
	if err != nil {
		g.l.Error("call do alarm error", zap.Error(err), zap.Int("gateway", id))
	}
	return nil
}
func (g *GatewayService) OPCCreate(id int, ip string, port string) error {
	scannerCtx := scanner.NewNodeScanner(g.l)
	var serverUrl = fmt.Sprintf("opc.tcp://%s:%s", ip, port)
	result, err := scannerCtx.Scan(serverUrl, "", "", 0, 85, uint32(ua.NodeClassObject)) // 0 83 是根节点
	if err != nil {
		g.l.Error("opcua scan error", zap.Error(err))
		return err
	}
	if result == nil {
		return errors.New("opcua scan result is nil")
	}
	return g.gatewayRepo.OPCCreate(id, *result)
}

func (g *GatewayService) OPCNodeList(param request.GatewayOPCNodeList) (model.Opcua, error) {
	return g.gatewayRepo.OPCNodeList(param.ID)
}
func (g *GatewayService) OPCNodeVariable(param request.GetOPCNodeVariable) (response.OPCNodeVariable, error) {
	scannerCtx := scanner.NewNodeScanner(g.l)
	var serverUrl = fmt.Sprintf("opc.tcp://%s:%s", param.IP, param.Port)
	result, err := scannerCtx.Scan(serverUrl, "", "", param.NS, param.ID, uint32(ua.NodeClassVariable))
	if err != nil {
		g.l.Error("opcua scan error", zap.Error(err))
		return response.OPCNodeVariable{}, err
	}
	jsonData, err := json.Marshal(result)
	if err != nil {
		return response.OPCNodeVariable{}, err
	}
	var variable = response.OPCNodeVariable{
		VariableInfo: string(jsonData),
	}
	return variable, err
}

func (g *GatewayService) ModelList() ([]string, error) {
	return g.gatewayRepo.ModelList()
}

func (g *GatewayService) Scan(param request.ScanGateway) ([]response.ScanGateway, error) {
	var (
		addrMap = make(map[string]struct{})
		addrArr = make([]net.IP, 0)
		nameMap = map[string]struct{}{
			"以太网":    {},
			"WLAN":   {},
			"eth0":   {},
			"enp1s0": {},
			"enp2s0": {},
			"enp3s0": {},
			"en0":    {},
			"en1":    {},
		}
	)

	var (
		result = make([]response.ScanGateway, 0)
		ift    *net.Interface
		err    error
	)

	if param.Name != "" {
		nameMap = map[string]struct{}{
			param.Name: {},
		}
	}

	for name := range nameMap {
		ift, err = net.InterfaceByName(name)
		if err != nil {
			continue
		}
		break
	}
	if ift == nil {
		return result, errors.New("未找到合适的网卡")
	}

	addrs, err := ift.Addrs()
	if err != nil {
		return result, err
	}

	for _, addr := range addrs {
		ipAddr, ok := addr.(*net.IPNet)
		if !ok {
			g.l.Warn("invalid ip address", zap.String("addr", addr.String()))
			continue
		}
		if !ipAddr.IP.IsGlobalUnicast() {
			continue
		}

		addrMap[ipAddr.IP.String()] = struct{}{}
		addrArr = append(addrArr, ipAddr.IP)
	}

	var (
		resultCh    = make(chan response.ScanGateway, 10)
		ctx, cancel = context.WithCancel(context.Background())
		existMap    = make(map[string]struct{})
		errCh       = make(chan error, 1)
	)
	defer cancel()

	ips, err := g.gatewayRepo.IPList()
	if err != nil {
		return result, err
	}

	for _, ip := range ips {
		existMap[ip] = struct{}{}
	}

	go g.listenUDP(ctx, addrMap, existMap, resultCh, errCh)

	var (
		data = make([]byte, 512)
		addr = &net.UDPAddr{
			IP:   net.IPv4(255, 255, 255, 255),
			Port: g.scanPort,
		}
	)
	copy(data, []byte{'T', 'W', 'F', 'I', 'N', 'D', 'P', 'R'})

	for _, item := range addrArr {
		conn, err := net.DialUDP(
			"udp",
			&net.UDPAddr{
				IP: item,
			},
			addr,
		)

		_, err = conn.Write(data)
		if err != nil {
			return result, err
		}
		conn.Close()
	}

	for {
		select {
		case <-time.NewTicker(3 * time.Second).C:
			goto End
		case tmp := <-resultCh:
			result = append(result, tmp)
		case err := <-errCh:
			return result, err
		}

	}
End:
	cancel()

	return result, nil
}

func (g *GatewayService) listenUDP(
	ctx context.Context,
	addrMap map[string]struct{},
	existMap map[string]struct{},
	resultCh chan<- response.ScanGateway,
	errCh chan<- error,
) {
	var (
		addr = &net.UDPAddr{
			IP:   net.IPv4(0, 0, 0, 0),
			Port: g.scanPort,
		}
		data    = make([]byte, 512)
		unique  = make(map[string]struct{})
		skipMap = map[string]struct{}{
			"PCE-M":    {},
			"TW_YUNHE": {},
		}
	)

	conn, err := net.ListenUDP("udp", addr)
	if err != nil {
		errCh <- err
		return
	}

	go func() {
		var (
			reg = regexp.MustCompile("\u0000+$")
		)
		for {
			n, addr, err := conn.ReadFromUDP(data)
			if err != nil {
				return
			}

			_, ok := addrMap[addr.String()]
			if ok || n != 512 {
				continue
			}

			ipField := fmt.Sprintf("%d.%d.%d.%d", data[61], data[60], data[59], data[58])

			_, ok = existMap[ipField]
			if ok {
				continue
			}

			_, ok = unique[ipField]
			if ok {
				continue
			}
			typeField := reg.ReplaceAllString(string(data[10:26]), "")

			_, ok = skipMap[typeField]
			if ok {
				continue
			}

			resultCh <- response.ScanGateway{
				IP: ipField,
				Mac: fmt.Sprintf(
					"%02X:%02X:%02X:%02X:%02X:%02X",
					data[62],
					data[63],
					data[64],
					data[65],
					data[66],
					data[67],
				),
				Version: strings.TrimSuffix(reg.ReplaceAllString(string(data[26:58]), ""), "\n"),
				Type:    typeField,
			}

			unique[ipField] = struct{}{}
		}
	}()

	<-ctx.Done()
	conn.Close()
}

func (g *GatewayService) AdapterList() ([]response.AdapterList, error) {
	var (
		result = make([]response.AdapterList, 0)
	)
	ifts, err := net.Interfaces()
	if err != nil {
		return result, err
	}

	for _, ift := range ifts {
		addrs, err := ift.Addrs()
		if err != nil {
			g.l.Warn("invalid ip address", zap.Error(err))
			continue
		}
		if ift.Name == "lo" {
			continue
		}
		var ips = make([]string, 0, len(addrs))
		for _, addr := range addrs {
			ipAddr, ok := addr.(*net.IPNet)
			if !ok {
				continue
			}
			if !ipAddr.IP.IsGlobalUnicast() {
				continue
			}
			ips = append(ips, addr.String())
		}
		if len(ips) == 0 {
			continue
		}
		result = append(result, response.AdapterList{
			Name:  ift.Name,
			Addrs: ips,
		})
	}

	return result, nil
}

func (g *GatewayService) AlarmBinding(param request.GatewayAlarmBinding) error {
	var (
		data = make([]model.GatewayAlarmBinding, 0, len(param.GatewayIDs))
	)

	for _, id := range param.GatewayIDs {
		data = append(data, model.GatewayAlarmBinding{
			BindingID: param.BindingID,
			GatewayID: id,
		})
	}

	return g.gatewayRepo.AlarmBinding(param.BindingID, data)
}

func (g *GatewayService) AlarmBindingList(param request.GetGatewayAlarmBinding) ([]int, error) {
	return g.gatewayRepo.IDsByBindingID(param.BindingID)
}

func (g *GatewayService) CreateGatewayBatch(param []request.CreateGateway) error {
	var (
		gateways = make([]model.GatewayWithPorts, 0, len(param))
	)

	for _, p := range param {
		gateway := model.GatewayWithPorts{
			Gateway: model.Gateway{
				Name:    p.Name,
				IP:      &p.IP,
				Version: p.Version,
				Type:    p.Type,
				Model:   p.Model,
				Status:  model.GatewayStatusOnline,
			},
		}
		for i := 0; i < len(p.Ports); i++ {
			gateway.Ports = append(gateway.Ports, model.GatewayPort{
				Name: p.Ports[i].Name,
				Port: p.Ports[i].Port,
				Type: p.Ports[i].Type,
			})
		}
		gateways = append(gateways, gateway)
	}

	return g.gatewayRepo.BatchCreate(gateways)
}

func (g *GatewayService) Statistic(token request.TokenInfo) (response.GatewayStatistic, error) {
	return g.gatewayRepo.Statistic(token)
}

func (g *GatewayService) Replace(param request.ReplaceGateway) error {
	//old
	g1, err := g.gatewayRepo.FirstWithDevices(model.Gateway{FlashID: &param.FID})
	if err != nil {
		return err
	}
	//new
	g2, err := g.gatewayRepo.FirstWithPorts(model.Gateway{FlashID: &param.NewFID})
	if err != nil {
		return err
	}
	if len(g1.Ports) != len(g2.Ports) {
		return errors.New("端口数量不匹配")
	}

	var (
		newMap        = make(map[string]model.GatewayPort, len(g2.Ports))
		updateDevices = make([]model.Device, 0)
		requests      = make([]request.MqttCreatePceDevice, 0)
	)

	for _, port := range g2.Ports {
		newMap[port.Port] = port
	}

	for _, port := range g1.Ports {
		var (
			request = request.MqttCreatePceDevice{
				Config: make([]string, 0),
				IDs:    make([]int, 0),
			}
		)
		for _, device := range port.Devices {
			portTemp, ok := newMap[port.Port]
			if !ok {
				continue
			}
			updateDevices = append(updateDevices, model.Device{
				PortID: &portTemp.ID,
				ID:     device.ID,
			})

			conf, err := g.deviceRepo.Config(model.Config{ID: device.ID})
			if err != nil {
				g.l.Warn("config get error", zap.Error(err), zap.Int("device id", device.ID))
				continue
			}
			confStr, err := jsoniter.MarshalToString(conf.Config.CommonDevice)
			if err != nil {
				g.l.Warn("json marshal error", zap.Error(err))
				continue
			}
			request.Config = append(request.Config, confStr)
			request.IDs = append(request.IDs, device.ID)
			if conf.DriverID != nil && *conf.DriverID != 0 {
				driver, err := g.driverRepo.GetByID(*conf.DriverID)
				if err != nil {
					g.l.Warn("driver get error", zap.Error(err), zap.Int("driver id", *conf.DriverID))
					continue
				}
				request.Driver = driver.Content.DriverInfo.Driver
			}
		}
		requests = append(requests, request)
	}

	err = g.gatewayRepo.Replace(g1.ID, g2.ID, updateDevices)
	if err != nil {
		g.l.Warn(
			"gateway replace error",
			zap.Error(err),
			zap.Int("old gateway id", g1.ID),
			zap.Int("new gateway id", g2.ID))
		return err
	}

	for _, request := range requests {
		err = g.mqttModule.Publish(
			fmt.Sprintf(model.TopicCreateDevice, *g2.FlashID),
			request,
		)
		if err != nil {
			g.l.Warn("mqtt publish error", zap.Error(err), zap.String("flash id", *g2.FlashID))
		}
	}

	return nil
}
func (g *GatewayService) Replace1(param request.ReplaceGateway1) error {
	//old
	g1, err := g.gatewayRepo.FirstWithDevices(model.Gateway{ID: param.ID})
	if err != nil {
		return err
	}
	//new
	g2, err := g.gatewayRepo.FirstWithPorts(model.Gateway{ID: param.NewID})
	if err != nil {
		return err
	}
	if len(g1.Ports) != len(g2.Ports) {
		return errors.New("端口数量不匹配")
	}

	if g2.Status != model.GatewayStatusOnline {
		return errors.New("新网关不在线")
	}

	var (
		newMap        = make(map[string]model.GatewayPort, len(g2.Ports))
		updateDevices = make([]model.Device, 0)
		requests      = make([]request.MqttCreatePceDevice, 0)
	)

	for _, port := range g2.Ports {
		newMap[port.Port] = port
	}

	for _, port := range g1.Ports {
		var (
			request = request.MqttCreatePceDevice{
				Config: make([]string, 0),
				IDs:    make([]int, 0),
			}
		)
		for _, device := range port.Devices {
			portTemp, ok := newMap[port.Port]
			if !ok {
				g.l.Warn("port not found", zap.String("port", port.Port))
				continue
			}
			updateDevices = append(updateDevices, model.Device{
				PortID: &portTemp.ID,
				ID:     device.ID,
			})

			conf, err := g.deviceRepo.Config(model.Config{ID: device.ID})
			if err != nil {
				g.l.Warn("config get error", zap.Error(err), zap.Int("device id", device.ID))
				continue
			}
			confStr, err := jsoniter.MarshalToString(conf.Config.CommonDevice)
			if err != nil {
				g.l.Warn("json marshal error", zap.Error(err))
				continue
			}
			request.Config = append(request.Config, confStr)
			request.IDs = append(request.IDs, device.ID)

			if conf.DriverID != nil && *conf.DriverID != 0 {
				driver, err := g.driverRepo.GetByID(*conf.DriverID)
				if err != nil {
					g.l.Warn("driver get error", zap.Error(err), zap.Int("driver id", *conf.DriverID))
					continue
				}
				request.Driver = driver.Content.DriverInfo.Driver
			}
		}
		if len(request.IDs) == 0 {
			continue
		}
		request.Port = port.Port
		requests = append(requests, request)
	}

	err = g.gatewayRepo.Replace(g1.ID, g2.ID, updateDevices)
	if err != nil {
		g.l.Warn(
			"gateway replace error",
			zap.Error(err),
			zap.Int("old gateway id", g1.ID),
			zap.Int("new gateway id", g2.ID))
		return err
	}

	for _, request := range requests {
		err = g.mqttModule.Publish(
			fmt.Sprintf(model.TopicCreateDevice, *g2.FlashID),
			request,
		)
		if err != nil {
			g.l.Warn("mqtt publish error", zap.Error(err), zap.String("flash id", *g2.FlashID))
		}
	}

	return nil
}

func (g *GatewayService) Register(param request.Register) error {
	var (
		gateway = model.Gateway{
			Name:    param.GWName,
			FlashID: &param.FID,
			Type:    model.GatewayTypeReport,
			Model:   param.Model,
			Status:  model.GatewayStatusOnline,
			Version: param.Version,
			IP:      &param.IP,
		}
		devices = make([]model.Device, 0, len(param.DeviceInfo))
		configs = make([]model.Config, 0, len(param.DeviceInfo))
		btMap   = make(map[string]int)
	)

	bts, err := g.btRepo.AllBySelect([]string{"id", "name"})
	if err != nil {
		return err
	}

	for _, bt := range bts {
		btMap[bt.Name] = bt.ID
	}

	for i := 0; i < len(param.DeviceInfo); i++ {
		btID, _ := btMap[param.DeviceInfo[i].DeviceType]
		device := param.DeviceInfo[i]

		devices = append(devices, model.Device{
			Name:           device.Name,
			Type:           device.DeviceType,
			Activity:       model.DeviceActivityEnable,
			Status:         model.DeviceStatusNormal,
			InternalID:     &device.ID,
			AreaID:         0,
			BusinessTypeID: btID,
		})

		var config = model.Config{
			Type: model.DeviceTypeReport,
			Config: model.DeviceConfig{
				ReportDevice: &model.ReportDevice{
					Unit: make([]model.ReportDeviceUnit, 0, len(device.Unit)),
				},
			},
		}
		for _, unit := range device.Unit {
			config.Config.ReportDevice.Unit = append(config.Config.ReportDevice.Unit, model.ReportDeviceUnit{
				ID:          unit.ID,
				Name:        unit.Name,
				Flag:        unit.Flag,
				AlarmStatus: model.DeviceStatusNormal,
				Level:       unit.Level,
			})
		}
		configs = append(configs, config)
	}

	err = g.gatewayRepo.CreateWithDevices(gateway, devices, configs)

	if err == nil {
		return nil
	}
	if !errors.Is(err, gorm.ErrDuplicatedKey) {
		return err
	}

	err = g.gatewayRepo.Update(model.Gateway{FlashID: &param.FID}, gateway)
	if err != nil {
		return err
	}

	info, err := g.gatewayRepo.First(model.Gateway{FlashID: &param.FID})
	if err != nil {
		return err
	}
	total, err := g.deviceRepo.Count(model.Device{GatewayID: &info.ID})
	if err != nil {
		return err
	}

	if total == int64(len(param.DeviceInfo)) {
		return nil
	}

	for i := 0; i < len(devices); i++ {
		devices[i].GatewayID = &info.ID
	}

	return g.deviceRepo.CreateReportDevices(info.ID, devices, configs)
}

func (g *GatewayService) NTP(fid string) error {
	return g.mqttModule.Publish(
		fmt.Sprintf(g.ntpTopic, fid),
		struct {
			Timestamp int64 `json:"timestamp"`
		}{
			Timestamp: time.Now().Unix(),
		},
	)
}
