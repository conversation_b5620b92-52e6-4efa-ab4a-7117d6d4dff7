package service

import (
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
)

type LinkageCaptureService struct {
	linkRepo *repository.LinkAgeCaptureRepo
}

func NewLinkageCaptureService(linkRepo *repository.LinkAgeCaptureRepo) *LinkageCaptureService {
	return &LinkageCaptureService{
		linkRepo: linkRepo,
	}
}

func (s *LinkageCaptureService) GetList(param request.LinkageCaptureList) ([]model.LinkageCapture, int64, error) {
	return s.linkRepo.GetList(param)
}
