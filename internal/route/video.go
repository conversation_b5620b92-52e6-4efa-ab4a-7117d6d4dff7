package route

import (
	"tw_platform/internal/controller"

	"github.com/gin-gonic/gin"
)

func NewVideoRouter(r *gin.RouterGroup, uc *controller.VideoController) {
	var (
		v1    = r.Group("/v1")
		video = v1.Group("/video")
	)

	video.GET("/list", uc.GetVideoList)  // 获取视频列表
	video.POST("/list", uc.SetVideoList) // 新增视频
	video.PUT("/list", uc.ModifyVideo)   // 编辑视频

	video.POST("/play", uc.PlayVideo) // 获取实时视频url
	video.POST("/preview", uc.PreviewVideo)
	video.POST("/playback", uc.PlaybackVideo)         // 获取回放url
	video.POST("/stop_playing", uc.StopPlayingFFMPEG) // 停止播放并销毁ffmpeg进程
	video.GET("/keep_alive", uc.KeepAliveFFMPEG)      // 保持播放

	video.GET("/capture_image", uc.CaptureImage) //

}
