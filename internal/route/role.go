package route

import (
	"tw_platform/internal/controller"

	"github.com/gin-gonic/gin"
)

func NewRoleRouter(r *gin.RouterGroup, rc *controller.RoleController) {
	var (
		v1   = r.Group("v1")
		role = v1.Group("/role")
	)

	role.POST("create", rc.Create)
	role.GET("list", rc.List)
	role.POST("update", rc.Update)
	role.POST("delete", rc.Delete)
	role.POST("menus", rc.SetMenus)
	role.GET("menus", rc.GetMenus)
	role.GET("allocable_list", rc.AllocableList)
	role.GET("permissions", rc.Permissions)
	role.POST("permissions", rc.SetPermissions)
}
