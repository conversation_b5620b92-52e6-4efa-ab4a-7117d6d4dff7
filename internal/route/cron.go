package route

import (
	"tw_platform/internal/service"
	"tw_platform/pkg/system/crontab"
)

func NewCronJobs(c *crontab.Crontab, cs *service.CronService) {
	//add a cron job

	//e.g.
	//每分钟打印一次当前时间
	// c.AddFunc("* * * * *", cs.Fortest)
	//每秒执行
	// c.CronWithSeconds.AddFunc("* * * * * *", cs.Fortest)

	// c.CronWithSeconds.AddFunc("*/10 * * * * *", cs.OnceTaskExecute)
	// c.CronWithSeconds.AddFunc("* * * * * *", cs.EventExecute)

	//每月1号生成上月报警记录excel
	c.Cron.AddFunc("20 0 1 * *", cs.ExportAlarm)

	c.Cron.AddFunc("* * * * *", cs.StandbyDeviceData)
}
