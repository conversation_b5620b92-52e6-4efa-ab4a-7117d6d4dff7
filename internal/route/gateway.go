package route

import (
	"tw_platform/internal/controller"

	"github.com/gin-gonic/gin"
)

func NewGatewayRouter(r *gin.RouterGroup, ac *controller.GatewayController) {
	var (
		v1      = r.Group("v1")
		gateway = v1.Group("/gateway")
		port    = gateway.Group("/port")
		opc     = gateway.Group("/opc")
	)

	gateway.POST("create", ac.Create)
	gateway.GET("is_accept", ac.IsAccept)
	gateway.POST("update", ac.Update)
	gateway.GET("list", ac.List)
	gateway.POST("is_accept_multi", ac.IsAcceptMulti)
	gateway.POST("delete", ac.Delete)
	gateway.GET("models", ac.ModelList)
	gateway.GET("scan", ac.Scan)
	gateway.GET("adapters", ac.AdapterList)
	gateway.POST("binding_alarmed", ac.AlarmBinding)
	gateway.GET("binding_alarmed", ac.AlarmBindingList)
	gateway.POST("batch_create", ac.CreateGatewayBatch)
	gateway.GET("statistic", ac.Statistic)
	gateway.POST("replace", ac.Replace1)

	port.POST("update", ac.UpdatePort)
	port.POST("add", ac.AddPort)
	port.POST("delete", ac.DeletePort)
	port.GET("list", ac.PortList)

	opc.GET("list", ac.OPCNodeList)
	opc.GET("variable", ac.OPCNodeVariable)

}
