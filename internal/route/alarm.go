package route

import (
	"tw_platform/internal/controller"

	"github.com/gin-gonic/gin"
)

func NewAlarmRouter(r, noAuth, outside *gin.RouterGroup, dc *controller.AlarmController) {
	var (
		v1    = r.Group("v1")
		alarm = v1.Group("/alarm")
		no    = noAuth.Group("v1/alarm")
	)

	alarm.GET("history_list", dc.HistoryList)
	alarm.GET("realtime_list", dc.RealtimeAlarmList)
	alarm.POST("handle", dc.Handle)
	alarm.POST("status", dc.SetStatus)
	alarm.GET("total_of_time", dc.TotalOfTime)
	alarm.GET("export", dc.ExportHistoryList)
	alarm.GET("realtime_ws", dc.RealtimeListWS)
	alarm.GET("export_list", dc.ExportList)
	alarm.GET("subscribe", dc.PushToClient)
	alarm.POST("remove", dc.Remove)

	alarm.POST("sync", dc.Sync)

	no.POST("apply_publish", dc.ApplyPublish)

	outside.POST("/v1/alarm/create", dc.Create)
	outside.POST("/v1/alarm/recovery", dc.Recovery)
}
