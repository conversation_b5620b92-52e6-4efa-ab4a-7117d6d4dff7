package route

import (
	"tw_platform/internal/controller"

	"github.com/gin-gonic/gin"
)

func NewRegisterRouter(r *gin.RouterGroup, uc *controller.RegisterController) {
	var (
		video = r.Group("register")
		v1    = video.Group("/v1")
	)
	// 获取功能授权码
	v1.GET("/funcode", uc.GetFunCode) //弃用
	// 获取授权功能项列表
	v1.GET("/function", uc.GetFuncPermissionInfo) //弃用
	// 项目功能授权
	v1.POST("/function", uc.SetFuncPermission)
	// 获取激活信息
	v1.GET("/registe", uc.GetRegisterInfo)
	// 项目激活
	v1.POST("/registe", uc.PostRegister) //弃用
	//3D授权
	v1.GET("/3d_permission", uc.Get3DPermission) //弃用
}
