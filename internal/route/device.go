package route

import (
	"tw_platform/internal/controller"
	"tw_platform/pkg/middleware"
	"tw_platform/pkg/system/device_capture"

	"github.com/gin-gonic/gin"
)

func NewDeviceRouter(
	r *gin.RouterGroup,
	outside *gin.RouterGroup,
	uc *controller.DeviceController,
	dc *device_capture.DeviceCapture,
) {
	var (
		v1     = r.Group("/v1")
		device = v1.Group("/device", middleware.CheckExpireMiddle(dc))
	)

	device.POST("create_pce_m", uc.CreatePceMDevice)
	device.POST("create", uc.CreateDevice)
	device.POST("create_custom", uc.CreateCustomDevice)
	device.POST("batch_create_custom", uc.BatchCreateCustomDevice)
	device.POST("create_opc", uc.CreateOpcDevice)
	device.POST("delete", uc.Delete)
	device.GET("config", uc.Config)
	device.POST("update", uc.Update)
	device.POST("unit", uc.UpdateUnit)
	device.POST("config", uc.UpdateConfig)
	device.GET("list", uc.List)
	device.GET("door_list", uc.DoorList) //todo 临时使用，适配后删除
	device.GET("list_by_business_type_id", uc.ListByFirstType)
	device.POST("history", uc.HistoryData)
	device.GET("realtime_data", uc.RealtimeData)
	device.POST("realtime_data_for_3d", uc.RealTimeDataFor3D)
	device.GET("realtime_status", uc.RealtimeStatus)
	device.POST("control", uc.Control)
	device.POST("name_list", uc.NameList)
	device.GET("list_by_area_id", uc.ListByAreaId)
	device.GET("list_with_config", uc.ListWithConfig)
	device.GET("custom_list", uc.ListCustom)
	device.GET("list_by_port", uc.ListByPort)
	device.GET("list_by_binding_alarmed", uc.AlarmBinding)
	device.POST("binding_alarmed", uc.SetAlarmBinding)
	device.GET("list_by_area_id_recursive", uc.ListByAreaIDRecursive)
	device.GET("detail", uc.Detail)
	device.POST("unit_by_driver", uc.UpdateUnitByDriver)
	device.GET("names_by_driver", uc.NameListByDriver)
	device.POST("sound_light", uc.CloseSoundLight)
	device.POST("create_asset", uc.CreateAsset)
	device.POST("update_asset", uc.UpdateAsset)
	device.GET("report_list", uc.ReportList)
	device.POST("report_info", uc.SetReportInfo)

	outside.GET("/v1/device/detail_asset", uc.Detail)
}
