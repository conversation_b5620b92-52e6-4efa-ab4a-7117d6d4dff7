package route

import (
	"tw_platform/internal/controller"

	"github.com/gin-gonic/gin"
)

func NewVirtualVariableRouter(r *gin.RouterGroup, dc *controller.VirtualVariableController) {
	var (
		v1    = r.Group("v1")
		group = v1.Group("/virtual_variable")
	)

	group.POST("create", dc.Create)
	group.GET("list", dc.List)
	group.POST("update", dc.Update)
	group.POST("unit", dc.UpdateUnit)
	group.POST("delete", dc.Delete)
	group.GET("detail", dc.Detail)
}
