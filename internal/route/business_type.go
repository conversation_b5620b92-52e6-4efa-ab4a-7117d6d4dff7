package route

import (
	"tw_platform/internal/controller"

	"github.com/gin-gonic/gin"
)

func NewBusinessTypeRouter(r *gin.RouterGroup, ac *controller.BusinessTypeController) {
	var (
		v1       = r.Group("v1")
		business = v1.Group("/business_type")
	)

	business.POST("create", ac.Create)
	business.GET("tree_only", ac.TreeOnly)
	business.GET("list_with_info", ac.ListWithDevices)
	business.POST("update", ac.Update)
	business.POST("delete", ac.Delete)
	business.GET("list_by_pid", ac.ListByPID)
	business.GET("list_has_device", ac.ListHasDevice)
	business.GET("tree_only_user", ac.TreeOnlyUser)
}
