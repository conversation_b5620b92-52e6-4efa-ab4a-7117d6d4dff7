package route

import (
	"tw_platform/internal/controller"

	"github.com/gin-gonic/gin"
)

func NewLinkageRouter(r *gin.RouterGroup, lc *controller.LinkageController) {
	var (
		v1      = r.Group("/v1")
		linkage = v1.Group("/linkage")
	)

	linkage.GET("/list", lc.GetLinkageList)            // 获取联动列表
	linkage.GET("/name", lc.GetLinkageName)            // 获取联动列表
	linkage.GET("/detail", lc.GetLinkageDetail)        // 获取联动信息
	linkage.POST("/linkage", lc.CreateLinkage)         // 创建联动
	linkage.DELETE("/linkage", lc.DeleteLinkage)       // 删除联动
	linkage.PUT("/linkage-info", lc.UpdateLinkageInfo) // 更新联动信息
	linkage.PUT("/linkage-data", lc.UpdateLinkageData) // 更新联动数据

}
