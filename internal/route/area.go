package route

import (
	"tw_platform/internal/controller"

	"github.com/gin-gonic/gin"
)

func NewAreaRouter(r *gin.RouterGroup, ac *controller.AreaController) {
	var (
		v1    = r.Group("v1")
		area  = v1.Group("/area")
		scene = area.Group("/scene")
	)

	area.POST("create", ac.Create)
	area.GET("tree", ac.Tree)
	area.GET("tree_only", ac.TreeOnlyByScene)
	area.GET("tree_only_by_bt_id", ac.TreeOnly)
	area.GET("all", ac.All)
	area.GET("all_with_counts", ac.AllWithTypeCounts)
	area.GET("all_with_devices", ac.AllWithDevices)
	area.GET("tree_with_device_configs", ac.TreeWithDeviceConfigs)
	area.POST("delete", ac.Delete)
	area.POST("update", ac.Update)
	area.GET("pue", ac.PUE)
	area.GET("list_by_pid", ac.ListByPID)
	area.GET("list_by_pid_recursive", ac.ListByPIDRecursive)
	area.GET("pue_every_hour", ac.PUEEveryHour)
	area.GET("tree_with_alarm_counts", ac.TreeWithAlarmCounts)
	area.GET("list_with_alarmed_by_pid", ac.ListWithAlarmedByPid)
	area.GET("tree_with_devices_by_btid", ac.TreeWithDevicesByBtID)
	area.GET("ids", ac.IDs)
	area.GET("realtime_status", ac.RealTimeAreaStatus)

	scene.POST("create", ac.CreateScene)
	scene.GET("detail", ac.SceneDetail)
	scene.POST("update", ac.UpdateScene)
	scene.POST("default", ac.DefaultSceneType)
	scene.GET("default", ac.DefaultSceneTypeList)
	scene.POST("delete", ac.DeleteScene)
}
