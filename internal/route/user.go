package route

import (
	"tw_platform/internal/controller"

	"github.com/gin-gonic/gin"
)

func NewUserRouter(r *gin.RouterGroup, uc *controller.UserController) {
	var (
		v1   = r.Group("v1")
		user = v1.Group("/user")
	)

	user.POST("/create", uc.Create)
	user.GET("list", uc.List)
	user.POST("areas", uc.SetArea)
	user.GET("areas", uc.GetArea)
	user.POST("departments", uc.SetDepartment)
	user.GET("departments", uc.GetDepartment)
	user.POST("business_types", uc.SetBusinessType)
	user.GET("business_types", uc.GetBusinessType)
	user.POST("update", uc.Update)
	user.POST("change_password", uc.ChangePassword)
	user.POST("reset_password", uc.ResetPassword)
	user.GET("list_simplify", uc.ListSimplify)
	user.POST("delete", uc.Delete)
	user.POST("list_by_id", uc.ListByID)
	user.GET("list_by_menu_id", uc.ListByMenuID)
	user.POST("qy_fetch", uc.QyFetch)
	user.POST("self_update", uc.SelfUpdate)
	user.GET("info", uc.Info)
	user.GET("list_without_page", uc.ListWithoutPage)
}
