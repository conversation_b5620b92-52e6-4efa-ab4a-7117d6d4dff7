package route

import (
	"tw_platform/internal/controller"

	"github.com/gin-gonic/gin"
)

func NewDoorRouter(r *gin.RouterGroup, uc *controller.DoorController) {
	var (
		v1                    = r.Group("v1")
		door                  = v1.Group("/door")
		door_controller       = v1.Group("/door_controller")
		door_controller_dahua = v1.Group("/door_controller_dahua")
	)
	// 获取门禁列表 √
	door.GET("/list", uc.GetDoorList)
	// 新增门禁一体机 √
	door.POST("/list", uc.NewDoorList)
	// 编辑门禁一体机 √
	door.PUT("/list", uc.EditDoor)
	//开关门 √
	door.POST("/operation", uc.CtrlDoor)
	//获取门的状态 √
	door.GET("/status", uc.GetDoorStatus)
	//获取门禁人员列表  √
	door.GET("/user", uc.GetDoorUserList)
	//添加人员信息     √
	door.POST("/user", uc.NewDoorUser)
	//修改人员信息     √
	door.PUT("/user", uc.EditDoorUser)
	//删除人员信息     √
	door.DELETE("/user", uc.DelDoorUser)
	//获取指定人员卡号  √aaaaa
	door.GET("/card", uc.GetCard)
	//添加人员卡号 √
	door.POST("/card", uc.AddCard)
	//删除卡号   √
	door.DELETE("/card", uc.DelCard)
	//获取指纹数据  √
	door.GET("/fingerprint", uc.GetFingerPrint)
	//添加指纹 √
	door.POST("/fingerprint", uc.AddFingerPrint)
	//获取添加指纹进度 √
	door.GET("/fingerprint/addprocess", uc.GetFingerAddProcess)
	//删除指纹进度 √
	door.GET("/fingerprint/deleteprocess", uc.GetFingerDelProcess)
	//删除指纹 √
	door.DELETE("/fingerprint", uc.DelFinger)
	//获取人脸    √
	door.GET("/face", uc.GetFace)
	//人脸删除     √
	door.DELETE("/face", uc.DelFace)
	//人脸上传     √
	door.POST("/face", uc.UpFace)
	//获取历史记录   √
	door.GET("/record", uc.GetRecord)
	//开始同步人员信息
	door.POST("/syncinfo", uc.StartSyncInfo)
	//获取同步进度
	door.GET("/syncinfo", uc.GetSyncProcess)
	//门禁控制器
	//获取门的状态 √
	door_controller.GET("/status", uc.GetDoorControllerStatus)
	//开关门 √
	door_controller.POST("/operation", uc.CtrlDoorController)
	//获取门禁卡列表
	door_controller.GET("/card", uc.GetDoorControllerCard)
	//新增门禁卡
	door_controller.POST("/card", uc.AddDoorControllerCard)
	//删除门禁卡
	door_controller.DELETE("/card", uc.DelDoorControllerCard)
	//获取历史记录
	door_controller.GET("/record", uc.GetDoorControllerRecord)
	//同步人员信息
	door_controller.POST("/syncinfo", uc.DoorControllerStartSyncInfo)
	//获取同步进度
	door_controller.GET("/syncinfo", uc.GetControllerSyncProcess)
	//大华门禁控制器
	//获取门的状态 √
	door_controller_dahua.GET("/status", uc.GetDoorControlleStatus_Dahua)
	//开关门 √
	door_controller_dahua.POST("/operation", uc.CtrlDoorController_Dahua)
	//获取人员列表
	door_controller_dahua.GET("/user", uc.AccessUser_Dahua)
	//添加人员
	door_controller_dahua.POST("/user", uc.InsertUser_Dahua)
	//删除人员
	door_controller_dahua.POST("/remove_user", uc.RemoveUser_Dahua)
	//添加卡片
	door_controller_dahua.POST("/card", uc.InsertCard_Dahua)
	//获取卡片
	door_controller_dahua.GET("/card", uc.AccessCard_Dahua)
	//删除卡片
	door_controller_dahua.POST("/remove_card", uc.RemoveCard_Dahua)
	//获取开门记录
	door_controller_dahua.GET("/record", uc.GetRecord_Dahua)
}
