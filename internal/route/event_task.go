package route

import (
	"tw_platform/internal/controller"

	"github.com/gin-gonic/gin"
)

func NewEventTaskRouter(r *gin.RouterGroup, dc *controller.EventTaskController) {
	var (
		v1    = r.Group("v1")
		group = v1.Group("/event_task")
	)

	group.POST("create", dc.Create)
	group.GET("list", dc.GetEventTaskList)
	group.GET("detail", dc.GetEventTaskDetail)
	group.POST("delete", dc.Delete)
	group.POST("update", dc.Update)
}
