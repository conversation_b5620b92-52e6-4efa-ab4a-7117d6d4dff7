package route

import (
	"tw_platform/internal/controller"

	"github.com/gin-gonic/gin"
)

func NewTicketRouter(r, outside *gin.RouterGroup, rc *controller.TicketController) {
	var (
		v1     = r.Group("v1")
		ticket = v1.Group("ticket")
	)

	ticket.POST("create", rc.Create)
	ticket.GET("list", rc.List)
	ticket.GET("timeline", rc.Timeline)
	ticket.POST("update", rc.Update)
	ticket.GET("detail", rc.Detail)

	outside.POST("/v1/ticket/create_asset", rc.Create)
	outside.POST("/v1/ticket/update_asset", rc.UpdateAsset)
}
