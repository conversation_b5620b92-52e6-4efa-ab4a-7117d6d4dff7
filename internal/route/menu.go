package route

import (
	"tw_platform/internal/controller"

	"github.com/gin-gonic/gin"
)

func NewMenuRouter(r *gin.RouterGroup, mc *controller.MenuController) {
	var (
		v1   = r.Group("/v1")
		menu = v1.Group("/menu")
	)

	menu.POST("create", mc.Create)
	menu.GET("tree", mc.Tree)
	menu.POST("delete", mc.Delete)
	menu.GET("detail", mc.Detail)
	menu.POST("update", mc.Update)
	menu.GET("list_by_role", mc.ListByRole)
	menu.GET("list_by_pid", mc.ListByPID)
}
