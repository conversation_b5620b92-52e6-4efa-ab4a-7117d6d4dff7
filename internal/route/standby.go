package route

import (
	"tw_platform/internal/controller"

	"github.com/gin-gonic/gin"
)

func NewStandbyRouter(auth, api *gin.RouterGroup, dc *controller.StandbyController) {
	var (
		standbyAuth = auth.Group("/v1/standby")
		standby     = api.Group("/v1/standby")
	)

	standbyAuth.GET("stats", dc.Stats)
	standbyAuth.POST("info", dc.SetStandby)
	standbyAuth.GET("info", dc.GetStandby)

	standbyAuth.GET("sync", dc.Sync)
	standby.GET("sync_tables", dc.SyncTables)
	standbyAuth.POST("push_tables", dc.PushTables)
	standby.POST("fetch_tables", dc.FetchPush)

	standby.GET("device_data", dc.DeviceData)
}
