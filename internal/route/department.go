package route

import (
	"tw_platform/internal/controller"

	"github.com/gin-gonic/gin"
)

func NewDepartmentRouter(r *gin.RouterGroup, dc *controller.DepartmentController) {
	var (
		v1   = r.Group("v1")
		dept = v1.Group("/department")
	)

	dept.POST("create", dc.Create)
	dept.GET("all", dc.All)
	dept.GET("all_with_counts", dc.AllWithTypeCounts)
	dept.GET("tree", dc.Tree)
	dept.GET("tree_only", dc.TreeOnly)
	dept.POST("update", dc.Update)
	dept.POST("delete", dc.Delete)
}
