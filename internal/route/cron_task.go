package route

import (
	"tw_platform/internal/controller"

	"github.com/gin-gonic/gin"
)

func NewCronTaskRouter(r *gin.RouterGroup, dc *controller.CronTaskController) {
	var (
		v1    = r.Group("v1")
		group = v1.Group("/cron_task")
	)

	group.POST("create", dc.Create)
	group.GET("list", dc.GetCronTaskList)
	group.GET("detail", dc.GetCronTaskDetail)
	group.POST("delete", dc.Delete)
	group.POST("update", dc.Update)
	group.GET("all", dc.GetCronTaskAll)
}
