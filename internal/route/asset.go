package route

import (
	"tw_platform/internal/controller"

	"github.com/gin-gonic/gin"
)

func NewAssetRouter(r *gin.RouterGroup, ac *controller.AssetController) {
	var (
		v1      = r.Group("v1")
		asset   = v1.Group("/asset")
		cabinet = asset.Group("/cabinet")
	)

	cabinet.POST("create", ac.CreateCabinet)
	cabinet.POST("update", ac.UpdateCabinet)
	cabinet.GET("list", ac.CabinetList)
	cabinet.POST("delete", ac.DeleteCabinet)

	asset.POST("create", ac.CreateAsset)
	asset.POST("update", ac.UpdateAsset)
	asset.GET("list", ac.AssetList)
	asset.POST("delete", ac.DeleteAsset)
}
