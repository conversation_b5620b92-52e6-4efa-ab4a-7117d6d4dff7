package route

import (
	"tw_platform/internal/controller"

	"github.com/gin-gonic/gin"
)

func NewLightScheduleRouter(r *gin.RouterGroup, dc *controller.LightScheduleController) {
	var (
		v1 = r.Group("v1")
		l  = v1.Group("/light_schedule")
	)

	l.POST("create", dc.Create)
	l.POST("update", dc.Update)
	l.GET("detail", dc.Detail)
	l.GET("list", dc.List)
	l.POST("delete", dc.Delete)
	l.POST("control_by_area", dc.ControlByArea)
	l.GET("statistic", dc.Statistic)
	l.GET("statistic_all", dc.StatisticAll)
	l.POST("control_all", dc.ControlAll)
}
