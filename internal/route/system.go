package route

import (
	"tw_platform/internal/controller"

	"github.com/gin-gonic/gin"
)

func NewSystemRouter(r *gin.RouterGroup, rc *controller.SystemController) {
	var (
		v1     = r.Group("v1")
		system = v1.Group("system")
	)
	system.POST("info", rc.Update)
	system.POST("sse_upgrade_pages", rc.SSEUpgradePages)
	system.POST("upgrade_pages", rc.UpgradePages)
	system.POST("upgrade_logo", rc.UpgradeLogo)
	system.GET("statistic", rc.Statistic)
	system.GET("version", rc.VersionInfo)
	system.GET("statistic_app", rc.StatisticApp)
}
