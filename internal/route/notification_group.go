package route

import (
	"tw_platform/internal/controller"

	"github.com/gin-gonic/gin"
)

func NewNotificationGroupRouter(r *gin.RouterGroup, dc *controller.NotifacationGroupController) {
	var (
		v1    = r.Group("v1")
		group = v1.Group("/notification_group")
	)

	group.POST("create", dc.Create)
	group.GET("list", dc.GetNotifacationGroupList)
	group.POST("delete", dc.Delete)
	group.POST("update", dc.Update)
	group.GET("gprs_config", dc.GetGprsConfig)
	group.GET("email_config", dc.GetEmailConfig)
	group.POST("update_gprs_config", dc.UpdateGprsConfig)
	group.POST("update_email_config", dc.UpdateEmailConfig)
	group.GET("4gdetail", dc.Get4GDetail)
	group.GET("4gye", dc.Get4GYE)
}
