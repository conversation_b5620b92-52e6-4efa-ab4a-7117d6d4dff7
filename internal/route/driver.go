package route

import (
	"tw_platform/internal/controller"

	"github.com/gin-gonic/gin"
)

func NewDriverRouter(r *gin.RouterGroup, dc *controller.DriverController) {
	var (
		v1     = r.Group("v1")
		driver = v1.Group("/driver")
	)

	driver.POST("create", dc.Create)
	driver.GET("list", dc.List)
	driver.GET("detail", dc.Detail)
	driver.POST("delete", dc.Delete)
	driver.GET("type_list", dc.TypeList)
	driver.GET("manu_facturer_list", dc.ManuFacturerList)
	driver.GET("agree_list", dc.AgreeList)
}
