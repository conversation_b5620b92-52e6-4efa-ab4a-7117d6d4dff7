package route

import (
	"tw_platform/internal/controller"

	"github.com/gin-gonic/gin"
)

func NewInspectRouter(r *gin.RouterGroup, dc *controller.InspectController) {
	var (
		v1      = r.Group("v1")
		inspect = v1.Group("/inspection")
	)

	inspect.POST("create", dc.Create)
	inspect.GET("list", dc.List)
	inspect.GET("plan_list", dc.PlanList)
	inspect.POST("execute", dc.Execute)
	inspect.POST("upload", dc.Upload)
	inspect.POST("delete_file", dc.Delete)
}
