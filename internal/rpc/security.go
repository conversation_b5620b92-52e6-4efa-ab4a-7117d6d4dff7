package rpc

import (
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
)

type SecurityRpc struct {
	videoService *service.VideoService
}

func NewSecurityHandler(videoService *service.VideoService) *SecurityRpc {
	return &SecurityRpc{
		videoService: videoService,
	}
}

func (sr *SecurityRpc) CreateVideoDevice(c *gin.Context) {
	var param request.CreateVideoDevice
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	err = sr.videoService.Create(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	response.Ok(c)
}

func (sr *SecurityRpc) Detail(c *gin.Context) {
	var param request.VideoDetail

	err := c.ShouldBind(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	detail, err := sr.videoService.Detail(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	response.OkWithData(c, detail)
}
