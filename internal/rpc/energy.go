package rpc

import (
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
)

type EnergyRpc struct {
	btService         *service.BusinessTypeService
	deviceService     *service.DeviceService
	departmentService *service.DepartmentService

	btID int
}

func NewEnergyRpc(
	btService *service.BusinessTypeService,
	deviceService *service.DeviceService,
	departmentService *service.DepartmentService,
) *EnergyRpc {
	return &EnergyRpc{
		btService:         btService,
		deviceService:     deviceService,
		departmentService: departmentService,

		btID: 2,
	}
}

func (e *EnergyRpc) BusinessTypeList(c *gin.Context) {
	var param request.BusinessTypeByTopLevel
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := e.btService.ListByTopLevel(e.btID, param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (e *EnergyRpc) DepartmentList(c *gin.Context) {
	var param request.DepartmentListByFilter
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := e.departmentService.ListByFilter(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (e *EnergyRpc) DeviceList(c *gin.Context) {
	var param request.DeviceListByTopLevel
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := e.deviceService.ListByTopLevel(e.btID, param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}
func (e *EnergyRpc) DeviceConfigList(c *gin.Context) {
	var param request.DeviceConfigListByTopLevel
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := e.deviceService.ConfigListByTopLevel(e.btID, param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}
