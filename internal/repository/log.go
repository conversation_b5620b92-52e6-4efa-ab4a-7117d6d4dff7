package repository

import (
	"fmt"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"gorm.io/gorm"
)

type LogRepo struct {
	db *gorm.DB
}

func NewLogRepo(db *gorm.DB) *LogRepo {
	return &LogRepo{db: db}
}

func (l *LogRepo) OperationList(param request.OperationList) ([]response.OperationList, int64, error) {
	var (
		list    []response.OperationList
		total   int64
		builder = l.db.Model(model.OperationLog{}).
			Joins("left join permissions as p on p.id = operation_logs.permission_id")
	)
	if param.UserId != 0 {
		builder = builder.Where("user_id = ?", param.UserId)
	}
	if param.PermissionName != "" {
		builder = builder.Where("p.name like ?", fmt.Sprintf("%s%%", param.PermissionName))
	}
	if param.BeginAt != "" {
		builder = builder.Where("created_at >= ?", param.BeginAt)
	}
	if param.EndAt != "" {
		builder = builder.Where("created_at <= ?", param.EndAt)
	}
	err := builder.
		Count(&total).
		Select(
			"operation_logs.id",
			"operation_logs.user_id",
			"operation_logs.param",
			"operation_logs.permission_id",
			"operation_logs.created_at",
			"p.name as permission_name",
			"p.path",
		).
		Limit(param.Size).
		Offset((param.Page-1)*param.Size).
		Order("operation_logs.id desc").
		Preload("UserInfo", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "nickname")
		}).
		Find(&list).
		Error
	return list, total, err
}

func (l *LogRepo) CreateAlarmLog(alarms []model.AlarmLog) error {
	return l.db.Model(model.AlarmLog{}).
		Create(&alarms).
		Error
}

func (l *LogRepo) AlarmList(param request.AlarmLogList) ([]response.AlarmLogList, int64, error) {
	var (
		list    []response.AlarmLogList
		total   int64
		builder = l.db.Model(model.AlarmLog{})
	)
	if param.DeviceID != 0 {
		builder = builder.Where("device_id = ?", param.DeviceID)
	}
	if param.BeginAt != "" {
		builder = builder.Where("created_at >= ?", param.BeginAt)
	}
	if param.EndAt != "" {
		builder = builder.Where("created_at <= ?", param.EndAt)
	}
	err := builder.
		Count(&total).
		Limit(param.Size).
		Offset((param.Page - 1) * param.Size).
		Order("id desc").
		Find(&list).
		Error
	return list, total, err
}

func (l *LogRepo) Usage(name string) (model.TableUseage, error) {
	var result model.TableUseage
	err := l.db.Raw("SELECT pg_total_relation_size(?) as usage", name).
		Scan(&result).
		Error
	return result, err
}

func (l *LogRepo) Truncate(name string) error {
	return l.db.Debug().Exec(fmt.Sprintf("TRUNCATE TABLE %s RESTART IDENTITY", name)).Error
}
