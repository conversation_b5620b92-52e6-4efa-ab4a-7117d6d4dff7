package repository

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/system/influxdb"
	"tw_platform/pkg/utils"

	"github.com/WinterYukky/gorm-extra-clause-plugin/exclause"
	"github.com/influxdata/influxdb-client-go/v2/api/write"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type DeviceRepo struct {
	db       *gorm.DB
	r        *redis.Client
	influxdb *influxdb.Client

	areaRepo       *AreaRepo
	departmentRepo *DepartmentRepo

	l *zap.Logger

	dataCacheKey          string
	controlDeviceKey      string
	batchContrlDeviceKey  string
	DeviceDiffKey         string
	alarmBlockedKey       string
	deviceUnits           string
	deviceDataIntervalKey string
	doUnitCache           string
	doType                string
	doUnitLock            string
}

func NewDeviceRepo(
	db *gorm.DB,
	r *redis.Client,
	influxdb *influxdb.Client,
	l *zap.Logger,
	areaRepo *AreaRepo,
	departmentRepo *DepartmentRepo,
) *DeviceRepo {
	return &DeviceRepo{
		db:       db,
		r:        r,
		influxdb: influxdb,
		l:        l.Named("device_repository"),

		areaRepo:       areaRepo,
		departmentRepo: departmentRepo,

		dataCacheKey:          "device_data:%d",
		controlDeviceKey:      "control_device",
		batchContrlDeviceKey:  "batch_control_device",
		DeviceDiffKey:         "device_diff",
		alarmBlockedKey:       "device_alarm_blocked:%d:%d",
		deviceUnits:           "device_units:%d",
		deviceDataIntervalKey: "device_data_interval:%d:%d",
		doUnitCache:           "do_unit:%d",
		doType:                "do",
		doUnitLock:            "do_unit_lock:%d",
	}
}

func (d *DeviceRepo) Create(devices []model.Device, confs []model.Config, param request.CreateDeviceBasic) ([]int, error) {
	var (
		departments = make([]model.DepartmentDevice, 0)
		ids         = make([]int, 0, len(devices))
		port        model.GatewayPort
		dgs         = make([]model.DepartmentGateway, 0)
		ag          model.AreaGateway
	)

	if len(devices) != len(confs) || len(devices) == 0 {
		return ids, errors.New("参数有误")
	}

	// 业务保证批量创建的设备拥有同一个区域/类型/部门
	typeID, err := d.secondTypeID(param.BusinessTypeID)
	if err != nil {
		d.l.Warn("get second type id failure", zap.Error(err))
		return ids, errors.New("不允许归属于顶层业务类型")
	}

	if len(devices) > 0 && devices[0].PortID != nil && *devices[0].PortID != 0 {
		err = d.db.Model(port).
			Select("gateway_id").
			Where("id = ?", *devices[0].PortID).
			Find(&port).
			Error
		if err != nil {
			return nil, err
		}
		ag = model.AreaGateway{
			AreaID:    param.AreaID,
			GatewayID: port.GatewayID,
		}
	}

	err = d.db.Transaction(func(tx *gorm.DB) error {
		err := d.areaRepo.countIncrease(tx, param.AreaID, typeID, len(devices))
		if err != nil {
			return err
		}

		err = d.departmentRepo.countIncrease(tx, param.DepartmentIDs, typeID, len(devices))
		if err != nil {
			return err
		}

		err = tx.Model(model.Device{}).Create(&devices).Error
		if err != nil {
			return err
		}

		for i := 0; i < len(devices); i++ {
			confs[i].ID = devices[i].ID
			for _, v := range param.DepartmentIDs {
				departments = append(departments, model.DepartmentDevice{
					DepartmentID: v,
					DeviceID:     devices[i].ID,
				})
				if port.GatewayID == 0 {
					continue
				}
				dgs = append(dgs, model.DepartmentGateway{
					DepartmentID: v,
					GatewayID:    port.GatewayID,
				})
			}

			ids = append(ids, devices[i].ID)
			if port.GatewayID == 0 {
				continue
			}
		}
		err = tx.Model(model.Config{}).Create(&confs).Error
		if err != nil {
			return err
		}
		err = tx.Model(model.DepartmentDevice{}).Create(&departments).Error
		if err != nil {
			return err
		}
		if ag.AreaID != 0 {
			err = tx.Clauses(
				clause.OnConflict{
					Columns:   []clause.Column{{Name: "area_id"}, {Name: "gateway_id"}},
					DoNothing: true,
				},
			).
				Model(model.AreaGateway{}).
				Create(&ag).
				Error
			if err != nil {
				return err
			}
		}
		if len(dgs) > 0 {
			err = tx.Clauses(
				clause.OnConflict{
					Columns:   []clause.Column{{Name: "department_id"}, {Name: "gateway_id"}},
					DoNothing: true,
				},
			).
				Model(model.DepartmentGateway{}).
				Create(&dgs).
				Error
			if err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return ids, err
	}

	data, err := json.Marshal(model.DeviceDiff{
		Event:     model.DeviceDiffAdd,
		DeviceIDs: ids,
	})
	if err != nil {
		return ids, err
	}
	return ids, d.RedisNotify(d.DeviceDiffKey, data)
}

func (d *DeviceRepo) Detail(id int) (model.DeviceWithConfig, error) {
	var (
		device model.DeviceWithConfig
	)

	err := d.db.Model(device).
		Where("id = ?", id).
		Preload("Config").
		First(&device).
		Error
	return device, err
}

func (d *DeviceRepo) Config(filter model.Config) (model.Config, error) {
	var config model.Config
	err := d.db.Model(config).
		Where(filter).
		First(&config).
		Error
	return config, err
}

func (d *DeviceRepo) ConfigWithName(filter model.Config) (response.DeviceConfigWithName, error) {
	var config response.DeviceConfigWithName
	err := d.db.Model(config).
		Where(filter).
		Preload("DeviceInfo", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "name")
		}).
		First(&config).
		Error
	return config, err
}

func (d *DeviceRepo) Configs(filter model.Config) ([]model.Config, error) {
	var configs []model.Config
	err := d.db.Model(configs).
		Where(filter).
		Find(&configs).
		Error
	return configs, err
}

func (d *DeviceRepo) First(filter model.Device) (model.Device, error) {
	var device model.Device
	err := d.db.Where(filter).
		First(&device).
		Error
	return device, err
}

func (d *DeviceRepo) Delete(device model.Device) error {
	typeID, err := d.secondTypeID(device.BusinessTypeID)
	if err != nil {
		return err
	}

	departmentIDs, err := d.departmentRepo.IDsByDevice(device.ID)
	if err != nil {
		return err
	}

	var (
		alarm model.Alarm
		port  model.GatewayPort

		areaTotal int64
		filterIds = make([]int, 0)
	)

	//存在网关信息
	if device.PortID != nil && *device.PortID != 0 {
		err := d.db.Model(port).
			Select("gateway_id").
			Where("id = ?", device.PortID).
			First(&port).
			Error
		if err != nil {
			return err
		}
		areaTotal, filterIds, err = d.areaDepartmentCount(port.GatewayID, device.AreaID)
		if err != nil {
			return err
		}
	}
	err = d.db.Transaction(func(tx *gorm.DB) error {
		err := d.areaRepo.countReduce(tx, device.AreaID, typeID, 1)
		if err != nil {
			return err
		}

		err = d.departmentRepo.countReduce(tx, departmentIDs, typeID, 1)
		if err != nil {
			return err
		}

		err = tx.Model(model.DepartmentDevice{}).
			Where("device_id = ?", device.ID).
			Delete(&model.DepartmentDevice{}).
			Error
		if err != nil {
			return err
		}

		err = tx.Where("id = ?", device.ID).
			Delete(&model.Device{}).
			Error
		if err != nil {
			return err
		}

		err = tx.Model(alarm).
			Where("device_id = ?", device.ID).
			Delete(&alarm).
			Error
		if err != nil {
			return err
		}

		err = tx.Model(model.DeviceAlarmBinding{}).
			Where("binding_id = ?", device.ID).
			Delete(&model.DeviceAlarmBinding{}).
			Error
		if err != nil {
			return err
		}

		if areaTotal <= 1 {
			err = tx.Model(model.AreaGateway{}).
				Where("area_id = ? and gateway_id = ?", device.AreaID, port.GatewayID).
				Delete(&model.AreaGateway{}).
				Error
			if err != nil {
				return err
			}
		}

		if len(filterIds) > 0 {
			err = tx.Model(model.DepartmentGateway{}).
				Where(
					"gateway_id = ? and department_id in (?)",
					port.GatewayID,
					filterIds,
				).
				Delete(&model.DepartmentGateway{}).
				Error
			if err != nil {
				return err
			}
		}

		return tx.Model(model.Config{}).
			Where("id = ?", device.ID).
			Delete(&model.Config{}).
			Error
	})
	return err
}

func (d *DeviceRepo) areaDepartmentCount(gatewayID, areaID int) (int64, []int, error) {
	var (
		areaCount       int64
		departmentIDs   = make([]int, 0)
		departmentCount = make([]struct {
			DepartmentID int `gorm:"department_id"`
			DeviceTotal  int `gorm:"device_total"`
		}, 0, len(departmentIDs))
	)

	err := d.db.Model(model.Device{}).
		Where(
			"EXISTS (?) and area_id = ?",
			d.db.Model(model.GatewayPort{}).
				Where("gateway_id = ? and devices.port_id = id", gatewayID),
			areaID,
		).
		Count(&areaCount).
		Error
	if err != nil {
		return areaCount, departmentIDs, err
	}

	err = d.db.Model(model.Device{}).
		Select(
			"dd.department_id",
			"count(dd.device_id) as device_total",
		).
		Where(
			"EXISTS (?)",
			d.db.Model(model.GatewayPort{}).
				Select("1").
				Where(
					"gateway_id = ? and devices.port_id = id",
					gatewayID,
				),
		).
		Joins(fmt.Sprintf("inner join %s dd on dd.device_id = devices.id", model.DepartmentDevice{}.TableName())).
		Group("dd.department_id").
		Find(&departmentCount).
		Error

	if err != nil {
		return areaCount, departmentIDs, err
	}
	for _, item := range departmentCount {
		if item.DeviceTotal <= 1 {
			departmentIDs = append(departmentIDs, item.DepartmentID)
		}
	}

	return areaCount, departmentIDs, nil
}

func (d *DeviceRepo) Update(id int, device model.Device) (int64, error) {
	builder := d.db.Model(device).
		Where("id = ?", id).
		Updates(device)

	return builder.RowsAffected, builder.Error
}

func (d *DeviceRepo) UpdateConfig(id int, expr *datatypes.JSONSetExpression) (int64, error) {
	builder := d.db.Model(model.Config{}).
		Where("id = ?", id).
		Update("config", expr)

	return builder.RowsAffected, builder.Error
}

func (d *DeviceRepo) FirstWithDriver(id int) (model.DeviceWithDriver, error) {
	var device model.DeviceWithDriver
	err := d.db.Model(device).
		Where("id = ?", id).
		Preload("Config", func(db *gorm.DB) *gorm.DB {
			return db.Select(
				"device_configs.id as id",
				"device_configs.driver_id",
				"device_configs.config",
				"drivers.content->'driver'->>'driver' as script",
			).
				Joins("inner join drivers on device_configs.driver_id = drivers.id")
		}).
		First(&device).
		Error
	return device, err
}

func (d *DeviceRepo) SetDataCache(data request.DeviceData) error {
	ctx, cancel := utils.RedisCtx()
	defer cancel()

	cache, err := json.Marshal(data)
	if err != nil {
		return err
	}
	return d.r.Set(
		ctx,
		fmt.Sprintf(d.dataCacheKey, data.ID),
		cache,
		time.Hour*24,
	).
		Err()
}

func (d *DeviceRepo) StoreData(data request.DeviceData) error {
	var (
		tags = map[string]string{
			"id": strconv.Itoa(data.ID),
		}
		fields      = make(map[string]interface{}, len(data.Units))
		ctx, cancel = context.WithTimeout(context.Background(), 5*time.Second)
		now         = time.Now().Unix()
	)
	defer cancel()

	units, err := d.AlarmLimit(data.ID)
	if err != nil {
		return err
	}

	for _, unit := range data.Units {
		temp := units.Units[unit.Point]
		if !temp.SaveEnable || temp.SaveTime == 0 {
			continue
		}
		last, _ := d.r.Get(
			ctx,
			fmt.Sprintf(d.deviceDataIntervalKey, data.ID, unit.Point),
		).Int64()
		if now-last < int64(temp.SaveTime) && !data.IsForceRecord {
			continue
		}

		d.r.Set(
			ctx,
			fmt.Sprintf(d.deviceDataIntervalKey, data.ID, unit.Point),
			now,
			24*time.Hour,
		)
		var value float64
		value, err = strconv.ParseFloat(unit.Value, 64)
		if err != nil {
			return err
		}

		fields[strconv.Itoa(unit.Point)] = utils.Round(value, 3)
	}
	if len(fields) == 0 {
		return nil
	}
	point := write.NewPoint(data.Type, tags, fields, time.Now())
	return d.influxdb.Write.WritePoint(ctx, point)
}

func (d *DeviceRepo) FirstByGateway(gatewayID int) (model.Device, error) {
	var device model.Device
	err := d.db.Model(model.Device{}).
		Where(
			"exists (?)",
			d.db.Model(model.GatewayPort{}).
				Select("1").
				Where("gateway_id = ? and devices.port_id = id", gatewayID)).
		First(&device).
		Error
	return device, err
}

func (d *DeviceRepo) List(businessTypeID, areaID, departmentID int) ([]response.DeviceList, error) {
	var (
		devices []response.DeviceList
		builder = d.db.Model(model.Device{}).
			Select(
				"devices.id",
				"devices.name",
				"devices.type",
				"devices.activity",
				"devices.status",
				"devices.created_at",
				"devices.business_type_id",
				"gp.id as port_id",
				"gp.name as port_name",
				"gp.port as port",
				"g.name as gateway_name",
				"g.ip as gateway_ip",
			)
	)

	if areaID != 0 {
		builder = builder.Where("area_id = ?", areaID)
	}
	if departmentID != 0 {
		builder = builder.Where(
			"exists (?)",
			d.db.Model(model.DepartmentDevice{}).
				Select("1").
				Where(
					"department_id = ? and devices.id = department_devices.device_id",
					departmentID,
				),
		)
	}

	err := builder.Where(
		"exists (?)",
		d.db.Model(model.BusinessTypeRelation{}).
			Select("1").
			Where(
				"parent_id = ? and devices.business_type_id = child_id",
				businessTypeID,
			),
	).
		Joins("left join gateway_ports as gp on devices.port_id = gp.id").
		Joins("left join gateways as g on gp.gateway_id = g.id").
		Order("id").
		Find(&devices).
		Error

	return devices, err
}
func (d DeviceRepo) CustomList() ([]response.DeviceList, error) {
	var (
		devices []response.DeviceList
		builder = d.db.Model(model.Device{}).
			Select(
				"devices.*",
			).
			Joins("JOIN device_configs ON devices.id = device_configs.id").
			Where("device_configs.type = ?", model.DeviceTypeCustom).
			Order("id")
	)
	err := builder.Scan(&devices).Error
	return devices, err
}
func (d *DeviceRepo) DoorList(businessTypeID, areaID, departmentID int) ([]response.DoorDeviceList, error) {
	var (
		devices []response.DoorDeviceList
		builder = d.db.Model(model.Device{}).
			Select(
				"devices.id",
				"devices.name",
				"devices.type",
				"devices.activity",
				"devices.status",
				"devices.created_at",
				"dc.config->'door_device'->>'type' as device_type",
			)
	)

	if areaID != 0 {
		builder = builder.Where("area_id = ?", areaID)
	} else {
		builder = builder.Where(
			"exists (?)",
			d.db.Model(model.DepartmentDevice{}).
				Select("1").
				Where(
					"department_id = ? and devices.id = department_devices.device_id",
					departmentID,
				),
		)
	}

	err := builder.Where(
		"exists (?)",
		d.db.Model(model.BusinessTypeRelation{}).
			Select("1").
			Where(
				"parent_id = ? and devices.business_type_id = child_id",
				businessTypeID,
			),
	).
		Joins("inner join device_configs as dc on devices.id = dc.id").
		Order("id").
		Find(&devices).
		Error

	return devices, err
}

func (d *DeviceRepo) secondTypeID(btID int) (int, error) {
	var relation model.BusinessTypeRelation

	err := d.db.Model(relation).
		Where("child_id = ?", btID).
		Order("depth desc").
		Offset(1).
		First(&relation).
		Error
	return relation.ParentID, err
}

func (d *DeviceRepo) ListByFirstType(typeID int) ([]model.Device, error) {
	var devices []model.Device
	err := d.db.Model(model.Device{}).
		Select("id", "name", "type").
		Where(
			"exists (?)",
			d.db.Model(model.BusinessTypeRelation{}).
				Where("parent_id = ? and devices.business_type_id = child_id", typeID),
		).
		Find(&devices).
		Error
	return devices, err
}

func (d *DeviceRepo) HistoryData(param request.DeviceHistoryData) (map[string]response.DeviceHistoryData, error) {
	var (
		sb          strings.Builder
		ctx, cancel = context.WithTimeout(context.Background(), 5*time.Second)
		// list        = make([]response.DeviceHistoryData, 0)
		resultMap = make(map[string]response.DeviceHistoryData)
	)
	defer cancel()

	for _, unitID := range param.UnitIDs {
		resultMap[unitID] = response.DeviceHistoryData{
			UnitID: unitID,
			Data:   make([]response.DeviceHistoryDataUnit, 0),
		}
	}
	location, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return resultMap, err
	}
	sb.WriteString(fmt.Sprintf(
		`from(bucket:"tw_platform") |> range(start: %s, stop: %s)`,
		param.Begin,
		param.End,
	))
	sb.WriteString(fmt.Sprintf(
		`|> filter(fn: (r) => r.id=="%d")`,
		param.DeviceID,
	))

	sb.WriteString("|> filter(fn: (r) => ")

	for i := 0; i < len(param.UnitIDs); i++ {
		if i != 0 {
			sb.WriteString(" or ")
		}
		sb.WriteString(fmt.Sprintf(
			`r._field == "%s"`,
			param.UnitIDs[i],
		))
	}

	sb.WriteString(")")

	result, err := d.influxdb.Query.Query(ctx, sb.String())
	if err != nil {
		return resultMap, err
	}

	for result.Next() {
		var (
			record = result.Record()
			tmp    = record.Value()
			value  float64
			ok     bool
		)
		if tmp != nil {
			switch v := tmp.(type) {
			case string:
				value, err = strconv.ParseFloat(v, 64)
				if err != nil {
					d.l.Warn("history data conversion failure", zap.Any("value", tmp))
				}
			case float64:
				value = v
			default:
				d.l.Warn("history data unknow types", zap.Any("value", tmp), zap.Any("type", reflect.TypeOf(tmp)))
			}
		}
		data, ok := resultMap[record.Field()]
		if !ok {
			d.l.Warn("influxdb data is not exist", zap.Any("field", record.Field()))
			continue
		}
		data.Data = append(data.Data, response.DeviceHistoryDataUnit{
			Value: value,
			Time:  record.Time().In(location).Format(time.DateTime),
		})
		resultMap[record.Field()] = data
	}

	return resultMap, nil
}

func (d *DeviceRepo) Control(payload []byte) error {
	return d.RedisNotify(d.controlDeviceKey, payload)
}
func (d *DeviceRepo) BatchControl(payload []byte) error {
	return d.RedisNotify(d.batchContrlDeviceKey, payload)
}

func (d *DeviceRepo) RealtimeData(param request.RealtimeData) ([]response.DeviceData, error) {
	var (
		ctx, cancel = utils.RedisCtx()
		results     = make([]response.DeviceData, 0, len(param.DeviceIDs))
		err         error
		ids         []int
		devices     []model.Device
	)
	defer cancel()
	for _, id := range param.DeviceIDs {
		var data response.DeviceData
		err = d.r.Get(ctx, fmt.Sprintf(d.dataCacheKey, id)).Scan(&data)
		if err != nil {
			ids = append(ids, id)
			continue
		}
		results = append(results, data)
	}

	//目前可以认为保证redis会存在所有设备的数据
	if len(ids) > 0 {
		d.db.Model(model.Device{}).
			Select("id", "type").
			Where("id in (?)", ids).
			Find(&devices)
	}

	for _, device := range devices {
		temp := request.DeviceData{
			ID:     device.ID,
			Type:   device.Type,
			Status: model.DeviceStatusOffline,
			Units:  make([]request.DeviceDataUnit, 0, 0),
		}

		results = append(results, response.DeviceData{
			DeviceData: temp,
		})
	}
	return results, nil
}

func (d *DeviceRepo) RealtimeStatus(param request.RealtimeData) ([]response.RealtimeDeviceStatus, error) {
	var (
		ctx, cancel = utils.RedisCtx()
		results     = make([]response.RealtimeDeviceStatus, 0, len(param.DeviceIDs))
		err         error
		ids         []int
		devices     []model.Device
	)
	defer cancel()
	for _, id := range param.DeviceIDs {
		var data response.RealtimeDeviceStatus
		err = d.r.Get(ctx, fmt.Sprintf(d.dataCacheKey, id)).Scan(&data)
		if err != nil {
			ids = append(ids, id)
			continue
		}
		results = append(results, data)
	}

	//目前可以认为保证redis会存在所有设备的数据
	if len(ids) > 0 {
		d.db.Model(model.Device{}).
			Select("id", "type").
			Where("id in (?)", ids).
			Find(&devices)
	}

	for _, device := range devices {
		results = append(results, response.RealtimeDeviceStatus{
			ID:     device.ID,
			Type:   device.Type,
			Status: model.DeviceStatusOffline,
		})
	}
	return results, nil
}

func (d *DeviceRepo) UnitName1(ids []int) ([]response.DeviceUnitName, error) {
	var (
		list  = make([]response.DeviceUnitName, 0)
		table = "units"
	)
	err := d.db.Clauses(exclause.NewWith(
		table,
		`select 
			id,
			jsonb_array_elements(config->'common_device'->'unit') as common_unit,
			jsonb_array_elements(config->'custom_device'->'unit') as custom_unit
		from device_configs 
		where id in (?)`,
		ids,
	)).
		Table(table).
		Select(
			"id",
			"coalesce(common_unit->>'id', custom_unit->>'id') as unit_id",
			"coalesce(common_unit->>'name', custom_unit->>'name') as name",
			"coalesce(common_unit->>'subValue', custom_unit->>'subValue') as sub_value",
			"coalesce(common_unit->>'activity', custom_unit->>'activity') as activity",
			"coalesce(common_unit->>'flag', custom_unit->>'flag') as flag",
		).
		Find(&list).
		Error
	return list, err
}

func (d *DeviceRepo) UnitName(ids []int) ([]response.DeviceUnitName, error) {
	var (
		list    = make([]response.DeviceUnitName, 0)
		configs = make([]model.Config, 0)
	)

	err := d.db.Model(model.Config{}).
		Where("id in (?)", ids).
		Find(&configs).
		Error
	if err != nil {
		return nil, err
	}

	for _, config := range configs {
		switch {
		case config.Config.CommonDevice != nil:
			for _, unit := range config.Config.CommonDevice.Unit {
				list = append(list, response.DeviceUnitName{
					ID:       config.ID,
					UnitID:   unit.ID,
					Name:     unit.Name,
					SubValue: unit.SubValue,
					Activity: unit.Activity,
					Flag:     unit.Flag,
				})
			}
		case config.Config.CustomDevice != nil:
			for _, unit := range config.Config.CustomDevice.Unit {
				list = append(list, response.DeviceUnitName{
					ID:       config.ID,
					UnitID:   unit.ID,
					Name:     unit.Name,
					SubValue: unit.SubValue,
					Activity: unit.Activity,
					Flag:     unit.Flag,
				})
			}
		default:
			return nil, errors.New("device config type not support")
		}
	}

	return list, nil
}

func (d *DeviceRepo) NameList(ids []int) ([]model.Device, error) {
	var list []model.Device
	err := d.db.Model(model.Device{}).
		Select("id", "name", "business_type_id").
		Where("id in (?)", ids).
		Find(&list).
		Error
	return list, err
}

func (d *DeviceRepo) Total(userID, btID int) (int64, error) {
	var total int64
	builder := d.db.Model(model.Device{})
	if btID != 0 {
		builder = builder.Where(
			"exists (?)",
			d.db.Model(model.BusinessTypeRelation{}).
				Select("1").
				Where(fmt.Sprintf("parent_id = ? and child_id = %s.business_type_id", model.Device{}.TableName()), btID),
		)
	}
	err := builder.
		Scopes(d.filterByUserID(userID)).
		Count(&total).
		Error
	return total, err
}

func (d *DeviceRepo) ListByAreaId(userId, areaId int) ([]response.DeviceList, error) {
	var list = make([]response.DeviceList, 0)
	err := d.db.Model(model.Device{}).
		Select(
			"devices.id",
			"devices.name",
			"devices.type",
			"devices.activity",
			"devices.status",
			"devices.area_id",
			"devices.business_type_id",
			"devices.created_at",
			"gp.id as port_id",
			"gp.name as port_name",
			"gp.port as port",
			"g.name as gateway_name",
			"g.ip as gateway_ip",
		).
		Where(
			"exists (?)",
			d.db.Model(model.UserArea{}).
				Select("1").
				Where("user_id = ? and area_id = ? and area_id = devices.area_id", userId, areaId),
		).
		Where(
			"exists (?)",
			d.db.Model(model.UserBusinessType{}).
				Select("1").
				Where("user_id = ? and business_type_id = devices.business_type_id", userId),
		).
		Joins("left join gateway_ports as gp on devices.port_id = gp.id").
		Joins("left join gateways as g on gp.gateway_id = g.id").
		Preload("Departments", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("device_id", "department_id")
		}).
		Find(&list).
		Error
	return list, err
}

func (d *DeviceRepo) ListByAreaIDRecursive(userID, areaID int) ([]model.Device, error) {
	var list []model.Device
	err := d.db.Model(model.Device{}).
		Select("id", "name", "type").
		Where(
			"exists (?) and exists (?)",
			d.db.Model(model.UserArea{}).
				Select("1").
				Where("user_id = ? and area_id = devices.area_id", userID),
			d.db.Model(model.AreaRelation{}).
				Select("1").
				Where("parent_id = ? and child_id = devices.area_id", areaID),
		).
		Find(&list).
		Error

	return list, err
}

func (d *DeviceRepo) DataCache(id int) (response.DeviceData, error) {
	var (
		result      response.DeviceData
		ctx, cancel = utils.RedisCtx()
	)
	defer cancel()
	err := d.r.Get(ctx, fmt.Sprintf(d.dataCacheKey, id)).Scan(&result)
	return result, err
}

func (d *DeviceRepo) ListWithConfig(areaID, btID, departmentID int) ([]response.DeviceListWithConfig, error) {
	var (
		list    = make([]response.DeviceListWithConfig, 0)
		builder = d.db.Model(model.Device{}).
			Where("business_type_id = ?", btID)
	)

	if departmentID != 0 {
		builder = builder.Where(
			"exists (?)",
			d.db.Model(model.DepartmentDevice{}).
				Select("1").
				Where("department_id = ? and devices.id = department_devices.device_id", departmentID),
		)
	}
	if areaID != 0 {
		builder = builder.Where("area_id = ?", areaID)
	}
	err := builder.
		Preload("Config", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "type", "config")
		}).
		Preload("Departments").
		Find(&list).
		Error

	return list, err
}

func (d *DeviceRepo) DetailWithArea(id int) (model.DeviceWithArea, error) {
	var result model.DeviceWithArea
	err := d.db.Model(model.Device{}).
		Where("id = ?", id).
		Preload("Config").
		Preload("Area", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "name")
		}).
		Find(&result).
		Error
	return result, err
}

func (d *DeviceRepo) BlockAlarm(id, unitID int) error {
	ctx, cancel := utils.RedisCtx()
	defer cancel()

	return d.r.Set(ctx, fmt.Sprintf(d.alarmBlockedKey, id, unitID), 1, 0).Err()
}

func (d *DeviceRepo) UnBlockAlarm(id, unitID int) error {
	ctx, cancel := utils.RedisCtx()
	defer cancel()

	return d.r.Del(ctx, fmt.Sprintf(d.alarmBlockedKey, id, unitID)).Err()
}

func (d *DeviceRepo) IsAlarmBlocked(id, unitID int) bool {

	ctx, cancel := utils.RedisCtx()
	defer cancel()

	total, err := d.r.Exists(ctx, fmt.Sprintf(d.alarmBlockedKey, id, unitID)).Result()
	if err != nil {
		d.l.Warn("check alarm blocked failure", zap.Error(err))
		return false
	}
	return total > 0
}

func (d *DeviceRepo) AlarmLimit(deviceID int) (response.DeviceAlarmLimit, error) {
	var (
		result response.DeviceAlarmLimit
		conf   = model.Config{
			ID: deviceID,
		}
		err         error
		max         int
		ctx, cancel = utils.RedisCtx()
	)
	defer cancel()
	result.Units = make(map[int]response.DeviceAlarmUnit, 0)

	err = d.r.Get(
		ctx,
		fmt.Sprintf(d.deviceUnits, deviceID),
	).
		Scan(&result)
	if err == nil {
		return result, nil
	}

	conf, err = d.Config(conf)
	if err != nil {
		return result, err
	}

	result.ID = conf.ID

	switch {
	case conf.Config.CommonDevice != nil:
		max = len(conf.Config.CommonDevice.Unit)
	case conf.Config.CustomDevice != nil:
		max = len(conf.Config.CustomDevice.Unit)
	}
	for i := 0; i < max; i++ {
		unit, err := conf.GetConfigUint(i)
		if err != nil {
			zap.L().Warn(
				"get device config unit failure",
				zap.Int("device id", conf.ID),
				zap.Int("unit id", i),
				zap.Error(err),
			)
		}
		if unit.HighName == "" {
			unit.HighName = "上限报警"
		}
		if unit.LowName == "" {
			unit.LowName = "下限报警"
		}
		if unit.HighHighName == "" {
			unit.HighHighName = "上上限报警"
		}
		if unit.LowLowName == "" {
			unit.LowLowName = "下下限报警"
		}

		result.Units[unit.ID] = response.DeviceAlarmUnit{
			ID:           unit.ID,
			Name:         unit.Name,
			Activity:     unit.Activity,
			AlarmEnable:  unit.Alarmenable,
			AlarmLevel:   unit.AlarmLevel,
			FilterCnt:    unit.FilterCnt,
			High:         unit.High,
			HighName:     unit.HighName,
			HighHyse:     unit.HighHyse,
			HighHigh:     unit.HighHigh,
			HighHighName: unit.HighHighName,
			Low:          unit.Low,
			LowName:      unit.LowName,
			LowHyse:      unit.LowHyse,
			LowLow:       unit.LowLow,
			LowLowName:   unit.LowLowName,
			Priority:     unit.Priority,
			SaveEnable:   unit.Saveenable,
			SaveTime:     unit.Savetime,
			EffectTime:   unit.EffectTime,
			Action:       unit.Action,
			Type:         unit.Type,
		}
	}

	d.r.Set(
		ctx,
		fmt.Sprintf(d.deviceUnits, deviceID),
		result,
		6*time.Hour,
	)

	return result, err
}

func (d *DeviceRepo) RemoveDeviceUnits(id int) error {
	ctx, cancel := utils.RedisCtx()
	defer cancel()
	return d.r.Del(ctx, fmt.Sprintf(d.deviceUnits, id)).Err()
}

func (d *DeviceRepo) BatchRemoveDeviceUnits(ids []int) {
	var (
		ctx, cancel = utils.RedisCtx()
		err         error
	)
	defer cancel()

	for _, id := range ids {
		err = d.r.Del(ctx, fmt.Sprintf(d.deviceUnits, id)).Err()
		if err != nil {
			d.l.Warn(
				"batch remove device units cache failure",
				zap.Error(err),
				zap.Int("id", id),
			)
		}
	}
}

func (d *DeviceRepo) ConfigType(id int) (model.Config, error) {
	var result model.Config
	err := d.db.Model(result).
		Select("id", "type").
		Where("id = ?", id).
		First(&result).
		Error
	return result, err
}

func (d *DeviceRepo) ListByPort(portID, userID int) ([]model.DeviceWithAreaName, error) {
	var list []model.DeviceWithAreaName
	err := d.db.Model(model.Device{}).
		Select(
			"devices.id",
			"devices.port_id",
			"devices.name",
			"devices.type",
			"areas.name as area_name",
		).
		Where("port_id = ?", portID).
		Scopes(d.filterByUserID(userID)).
		Joins("inner join areas on areas.id = devices.area_id").
		Find(&list).
		Error

	return list, err
}

func (d *DeviceRepo) ListByBindingAlarmed(bingdingID int) ([]model.Device, error) {
	var list []model.Device
	err := d.db.Model(model.Device{}).
		Where(
			"exists (?)",
			d.db.Model(model.DeviceAlarmBinding{}).
				Select("1").
				Where(
					"binding_id = ? and devices.id = device_alarm_bindings.id",
					bingdingID,
				),
		).
		Select("id", "name", "type").
		Find(&list).
		Error
	return list, err
}

func (d *DeviceRepo) SetBindingAlarmed(bindingID int, dabs []model.DeviceAlarmBinding) error {
	return d.db.Transaction(func(tx *gorm.DB) error {
		var dab model.DeviceAlarmBinding
		err := tx.Model(dab).
			Where("binding_id = ?", bindingID).
			Delete(&dab).
			Error
		if err != nil {
			return err
		}
		return tx.Model(dab).
			Create(&dabs).
			Error
	})
}

func (d *DeviceRepo) DOUnit(id int) (model.DeviceConfigUnit, error) {
	var (
		result      model.DeviceConfigUnit
		ctx, cancel = utils.RedisCtx()
		relpoly     model.DeviceUnitRelpoly
		key         = fmt.Sprintf(d.doUnitCache, id)
	)
	defer cancel()

	err := d.r.Get(ctx, key).Scan(&result)

	if err == nil {
		return result, nil
	}

	config, err := d.Config(model.Config{ID: id})
	if err != nil {
		return result, err
	}

	if config.Config.CommonDevice == nil {
		return result, errors.New("绑定设备暂不支持声光报警")
	}

	for i := 0; i < len(config.Config.CommonDevice.Unit); i++ {
		if config.Config.CommonDevice.Unit[i].Type != d.doType {
			continue
		}
		result.ID = config.Config.CommonDevice.Unit[i].ID
		result.Type = config.Config.CommonDevice.Unit[i].Type
		result.Name = config.Config.CommonDevice.Unit[i].Name
		relpoly = model.DeviceUnitRelpoly{
			OnceTime: config.Config.CommonDevice.Unit[i].Relpoly.OnceTime,
			Type:     config.Config.CommonDevice.Unit[i].Relpoly.Type,
		}
		result.Relpoly = &relpoly
		break
	}

	if result.Type == "" {
		return result, errors.New("DO unit not found")
	}

	err = d.r.Set(ctx, key, result, 6*time.Hour).Err()

	return result, err
}

func (d *DeviceRepo) DeleteDOUnitCache(id int) error {
	ctx, cancel := utils.RedisCtx()
	defer cancel()
	return d.r.Del(ctx, fmt.Sprintf(d.doUnitCache, id)).Err()
}

func (d *DeviceRepo) DOUnitLock(id int, duration int64) bool {
	ctx, cancel := utils.RedisCtx()
	defer cancel()
	return d.r.SetNX(
		ctx,
		fmt.Sprintf(d.doUnitLock, id),
		1,
		time.Duration(duration)*time.Second,
	).Val()
}

func (d *DeviceRepo) AlarmedBinding(id int) (model.DeviceAlarmBinding, error) {
	var result model.DeviceAlarmBinding
	err := d.db.Model(result).
		Where("id = ?", id).
		First(&result).
		Error
	return result, err
}

func (d *DeviceRepo) TotalByType(t string) (int64, error) {
	var total int64
	err := d.db.Model(model.Device{}).
		Where("type = ?", t).
		Count(&total).
		Error
	return total, err
}

func (d *DeviceRepo) DeleteCache(id int) error {
	ctx, cancel := utils.RedisCtx()
	defer cancel()
	return d.r.Del(ctx, fmt.Sprintf(d.dataCacheKey, id)).Err()
}

func (d *DeviceRepo) RedisNotify(key string, data []byte) error {
	ctx, cancel := utils.RedisCtx()
	defer cancel()

	return d.r.Publish(ctx, key, data).Err()
}

func (d *DeviceRepo) ResumeStoreData(params []request.ResumeDeviceData) error {
	var points = make([]*write.Point, 0, len(params))
	for _, param := range params {
		var (
			tags = map[string]string{
				"id": strconv.Itoa(param.ID),
			}
			fields = make(map[string]interface{}, len(param.Units))
		)

		for _, unit := range param.Units {
			value, err := strconv.ParseFloat(unit.Value, 64)
			if err != nil {
				d.l.Warn("resume data conversion failure", zap.Error(err))
				continue
			}
			fields[strconv.Itoa(unit.Point)] = value
			points = append(
				points,
				write.NewPoint(param.Type, tags, fields, time.Unix(param.Timestamp, 0)),
			)
		}
	}
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	return d.influxdb.Write.WritePoint(ctx, points...)
}

func (d *DeviceRepo) UpdateDetail(param request.UpdateDevice) (int64, error) {
	var (
		info   model.Device
		typeID int
		rows   int64
		err    error
		device = model.Device{
			AreaID:   param.AreaID,
			Name:     param.Name,
			Activity: param.Activity,
			ID:       param.ID,
		}
		ids             = make([]int, 0)
		areaCount       int64
		port            model.GatewayPort
		departmentCount []int
	)

	if device.AreaID != 0 || len(param.DepartmentID) > 0 {
		info, err = d.First(model.Device{ID: device.ID})
		if err != nil {
			return 0, err
		}
		typeID, err = d.secondTypeID(info.BusinessTypeID)
		if err != nil {
			return 0, err
		}
		if info.PortID != nil && *info.PortID != 0 {
			err = d.db.Model(port).
				Select("gateway_id").
				Where("id = ?", *info.PortID).
				First(&port).
				Error
			if err != nil {
				return 0, err
			}

			areaCount, departmentCount, err = d.areaDepartmentCount(port.GatewayID, info.AreaID)
		}

	}

	if len(param.DepartmentID) > 0 {
		ids, err = d.departmentRepo.IDsByDevice(device.ID)
		if err != nil {
			return 0, err
		}
	}

	err = d.db.Transaction(func(tx *gorm.DB) error {
		if device.AreaID != 0 {
			err = d.areaRepo.countReduce(tx, info.AreaID, typeID, 1)
			if err != nil {
				return err
			}
			err = d.areaRepo.countIncrease(tx, device.AreaID, typeID, 1)
			if err != nil {
				return err
			}

			if areaCount <= 1 {
				err = tx.Model(model.AreaGateway{}).
					Where("area_id = ? and gateway_id = ?", info.AreaID, port.GatewayID).
					Delete(&model.AreaGateway{}).
					Error
				if err != nil {
					return err
				}
			}
			err = tx.Clauses(
				clause.OnConflict{
					Columns:   []clause.Column{{Name: "area_id"}, {Name: "gateway_id"}},
					DoNothing: true,
				},
			).
				Model(model.AreaGateway{}).
				Create(&model.AreaGateway{
					AreaID:    device.AreaID,
					GatewayID: port.GatewayID,
				}).
				Error
			if err != nil {
				return err
			}

		}
		if len(param.DepartmentID) > 0 {
			err = d.departmentRepo.countReduce(tx, ids, typeID, 1)
			if err != nil {
				return err
			}

			err = d.departmentRepo.countIncrease(tx, param.DepartmentID, typeID, 1)
			if err != nil {
				return err
			}
			var dd model.DepartmentDevice
			err = tx.Model(dd).
				Where("device_id = ?", device.ID).
				Delete(&dd).
				Error
			if err != nil {
				return err
			}

			var (
				dds []model.DepartmentDevice
				dgs = make([]model.DepartmentGateway, 0, len(param.DepartmentID))
			)
			for _, id := range param.DepartmentID {
				dds = append(dds, model.DepartmentDevice{
					DepartmentID: id,
					DeviceID:     device.ID,
				})
				dgs = append(dgs, model.DepartmentGateway{
					DepartmentID: id,
					GatewayID:    port.GatewayID,
				})
			}
			err = tx.Model(dd).Create(dds).Error
			if err != nil {
				return err
			}

			if len(departmentCount) > 0 {
				err = tx.Model(model.DepartmentGateway{}).
					Where(
						"gateway_id = ? and department_id in (?)",
						port.GatewayID,
						departmentCount,
					).
					Delete(&model.DepartmentGateway{}).
					Error
				if err != nil {
					return err
				}
			}

			if len(dgs) > 0 {
				err = tx.Clauses(
					clause.OnConflict{
						Columns:   []clause.Column{{Name: "department_id"}, {Name: "gateway_id"}},
						DoNothing: true,
					},
				).
					Model(model.DepartmentGateway{}).
					Create(dgs).
					Error
				if err != nil {
					return err
				}
			}
		}
		builder := tx.Model(device).
			Where("id = ?", device.ID).
			Updates(device)
		if builder.Error != nil {
			return builder.Error
		}
		rows = builder.RowsAffected
		return nil
	})
	return rows, err
}

func (d *DeviceRepo) FirstWithDepartments(device model.Device, selects ...string) (response.DeviceDetail, error) {
	var (
		result response.DeviceDetail
	)
	err := d.db.Model(device).
		Select(selects).
		Where(device).
		First(&result).
		Error
	if err != nil {
		return result, err
	}
	err = d.db.Model(model.DepartmentDevice{}).
		Where("device_id = ?", device.ID).
		Pluck("department_id", &result.DepartmentIDs).
		Error
	return result, err
}

func (d *DeviceRepo) UpdateConfigByDriver(driverID int, expr *datatypes.JSONSetExpression) error {
	builder := d.db.Model(model.Config{}).Debug().
		Where("driver_id = ?", driverID).
		Update("config", expr)

	return builder.Error
}

func (d *DeviceRepo) IDsByDriverID(driverID int) ([]int, error) {
	var ids []int
	err := d.db.Model(model.Config{}).
		Where("driver_id = ?", driverID).
		Pluck("id", &ids).
		Error
	return ids, err
}

func (d *DeviceRepo) NameListByDriverID(driverID int) ([]string, error) {
	var (
		names []string
	)

	err := d.db.Model(model.Device{}).Debug().
		Where(
			"exists (?)",
			d.db.Model(model.Config{}).
				Where("driver_id = ? and devices.id = device_configs.id", driverID),
		).
		Pluck("name", &names).
		Error
	return names, err
}

func (d *DeviceRepo) ListWithConfigByBusinessTypeID(btID int) ([]model.DeviceWithConfig, error) {
	var (
		list []model.DeviceWithConfig
	)

	err := d.db.Model(&model.Device{}).
		Select("id").
		Where("business_type_id = ?", btID).
		Preload("Config").
		Find(&list).
		Error
	return list, err
}

func (d *DeviceRepo) PUEEveryHour(id int) ([]response.PUEEveryHour, error) {
	var (
		query = fmt.Sprintf(`
		from(bucket: "tw_platform")
		|> range(start:-24h, stop:now())
		|> filter(fn: (r) => r._measurement == "PUE")
		|> filter(fn: (r) => r.id == "%d")
		|> filter(fn: (r) => r._field == "0")
		|> window( period:1h)
		|> mean()
		`,
			id,
		)
		ctx, cancel = context.WithTimeout(context.Background(), 5*time.Second)
		data        = make([]response.PUEEveryHour, 0, 24)
	)
	defer cancel()

	result, err := d.influxdb.Query.Query(ctx, query)
	if err != nil {
		return data, err
	}

	l, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return data, err
	}

	for result.Next() {
		var (
			record = result.Record()
			value  interface{}
		)

		value = record.Value()
		if fValue, ok := value.(float64); ok {
			value = utils.Round(fValue, 2)
		}

		stop := record.Stop()
		if stop.Minute() != 0 {
			continue
		}
		data = append(data, response.PUEEveryHour{
			PUE:  value,
			Time: stop.In(l).Format("2006-01-02 15"),
		})

	}
	return data, nil
}

func (d *DeviceRepo) ListByTopLevel(btID int, isRecursive bool, filter model.Device) ([]model.Device, error) {
	var (
		list    []model.Device
		builder = d.db.Model(model.Device{})
	)

	switch {
	case filter.ID != 0:
		builder = builder.Where("id = ?", filter.ID)
	case !isRecursive && filter.BusinessTypeID != 0:
		builder = builder.Where("business_type_id = ?", filter.BusinessTypeID)
	case isRecursive && filter.BusinessTypeID != 0:
		builder = builder.Where(
			"EXISTS(?)",
			d.db.Model(model.BusinessTypeRelation{}).
				Select("1").
				Where("parent_id = ? and devices.business_type_id = child_id", filter.BusinessTypeID),
		)
	}

	err := builder.
		Where(
			"EXISTS (?)",
			d.db.Model(model.BusinessTypeRelation{}).
				Select("1").
				Where("parent_id = ? and devices.business_type_id = child_id", btID),
		).
		Find(&list).
		Error
	return list, err
}

func (d *DeviceRepo) ConfigListByTopLevel(btID, id int) ([]model.Config, error) {
	var (
		list    []model.Config
		builder = d.db.Model(model.Config{}).
			Joins("inner join devices on devices.id = device_configs.id")
	)

	if id != 0 {
		builder = builder.Where("devices.id = ?", id)
	}

	err := builder.Where(
		"EXISTS (?)",
		d.db.Model(model.BusinessTypeRelation{}).
			Select("1").
			Where("parent_id = ? and devices.business_type_id = child_id", btID),
	).
		Find(&list).
		Error
	return list, err
}

func (d *DeviceRepo) CountByBtID(btID int) (int64, error) {
	var total int64
	err := d.db.Model(model.Device{}).
		Where("business_type_id = ?", btID).
		Count(&total).
		Error
	return total, err
}

func (d *DeviceRepo) FirstWithConfig(filter model.Device) (model.DeviceWithConfig, error) {
	var result model.DeviceWithConfig

	err := d.db.Model(model.Device{}).
		Where(filter).
		Preload("Config").
		First(&result).
		Error
	return result, err
}

func (d *DeviceRepo) GetDataCache(id int) (response.DeviceData, error) {
	var (
		ctx, cancel = utils.RedisCtx()
		data        response.DeviceData
	)
	defer cancel()
	err := d.r.Get(ctx, fmt.Sprintf(d.dataCacheKey, id)).Scan(&data)

	return data, err
}

func (d *DeviceRepo) filterByUserID(uid int) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where(
			"exists (?)",
			d.db.Raw(
				"? union ?",
				d.db.Model(model.UserArea{}).
					Select("1").
					Where("user_id = ? and area_id = devices.area_id", uid),
				d.db.Model(model.DepartmentDevice{}).
					Select("1").
					Where(
						"exists (?) and devices.id = department_devices.device_id",
						d.db.Model(model.UserDepartment{}).
							Select("1").
							Where("user_id = ? and department_id = department_devices.department_id", uid),
					),
			),
		)
	}
}

func (d *DeviceRepo) ConfigsByAreaID(areaID int) ([]model.Config, error) {
	var configs []model.Config
	err := d.db.Model(fmt.Sprintf(
		"%s dc",
		model.Config{}.TableName(),
	)).
		Joins(fmt.Sprintf(
			"%s d on d.id = dc.id",
			model.Device{}.TableName(),
		)).
		Select("dc.*").
		Where("d.area_id = ?", areaID).
		Find(&configs).
		Error
	return configs, err
}

func (d *DeviceRepo) Count(filter model.Device) (int64, error) {
	var total int64

	err := d.db.Model(model.Device{}).Debug().
		Where(filter).
		Count(&total).
		Error
	return total, err
}

func (d *DeviceRepo) CreateReportDevices(gatewayID int, devices []model.Device, configs []model.Config) error {
	var (
		device model.Device
		config model.Config
		ids    []int
	)
	err := d.db.Model(device).Where("gateway_id = ?", gatewayID).
		Pluck("id", &ids).
		Error
	if err != nil {
		return err
	}
	return d.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(device).
			Where("id in (?)", ids).
			Delete(&device).
			Error
		if err != nil {
			return err
		}

		err = tx.Model(config).
			Where("id in (?)", ids).
			Delete(&config).
			Error
		if err != nil {
			return err
		}

		err = tx.Model(model.Device{}).
			Create(&devices).
			Error
		if err != nil {
			return err
		}
		for i := 0; i < len(devices); i++ {
			configs[i].ID = devices[i].ID
		}
		return tx.Model(model.Config{}).
			Create(&configs).
			Error
	})
}

func (d *DeviceRepo) ListWithSelector(selector []string, filter model.Device) ([]model.Device, error) {
	var list []model.Device
	d.db.Model(model.Device{}).
		Select(selector).
		Where(filter).
		Find(&list)
	return list, nil
}

func (d *DeviceRepo) FirstByGatewayFID(fid string, devID int) (model.Device, error) {
	var (
		device model.Device
	)

	err := d.db.Model(device).
		Where(
			"exists (?) and internal_id = ?",
			d.db.Model(model.Gateway{}).
				Select("1").
				Where("flash_id = ? and devices.gateway_id = id", fid),
			devID,
		).
		First(&device).
		Error

	return device, err
}

func (d *DeviceRepo) DetailByAreaID(areaID, btID int) ([]model.DeviceWithConfig, error) {
	var (
		list []model.DeviceWithConfig
	)
	builder := d.db.Model(model.Device{}).
		Where("area_id = ?", areaID).
		Where(
			"EXISTS (?)",
			d.db.Model(model.BusinessTypeRelation{}).
				Select("1").
				Where("parent_id = ? and devices.business_type_id = child_id", btID),
		)
	err := builder.Preload("Config").
		Find(&list).
		Error
	return list, err
}
func (d *DeviceRepo) DetailByUserID(UID, btID int) ([]model.DeviceWithConfig, error) {
	var (
		list []model.DeviceWithConfig
	)
	builder := d.db.Model(model.Device{}).
		Where(
			"EXISTS (?)",
			d.db.Model(model.UserArea{}).
				Select("1").
				Where("user_id = ? and area_id = devices.area_id", UID),
		).
		Where(
			"EXISTS (?)",
			d.db.Model(model.BusinessTypeRelation{}).
				Select("1").
				Where("parent_id = ? and devices.business_type_id = child_id", btID),
		)
	err := builder.Preload("Config").
		Find(&list).
		Error
	return list, err
}

func (d *DeviceRepo) DetailByBusinessTypeID(btID, uid int) ([]model.DeviceWithConfig, error) {
	var (
		list []model.DeviceWithConfig
	)
	err := d.db.Model(model.Device{}).
		Where("business_type_id = ?", btID).
		Where(
			"EXISTS (?)",
			d.db.Model(model.UserArea{}).
				Select("1").
				Where("user_id = ? and area_id = devices.area_id", uid),
		).
		Preload("Config").
		Find(&list).
		Error
	return list, err
}

func (d *DeviceRepo) ReportListWithGateway(page, size int, filter model.Device) ([]model.DeviceWithGatewayInfo, int64, error) {
	var (
		list  []model.DeviceWithGatewayInfo
		total int64
	)
	err := d.db.Model(model.Device{}).
		Where("area_id = ?", 0).
		Where(filter).
		Count(&total).
		Limit(size).
		Offset((page-1)*size).
		Preload("GatewayInfo", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "name")
		}).
		Order("id desc").
		Find(&list).
		Error
	return list, total, err
}

func (d *DeviceRepo) SetReportInfo(param request.SetReportDeviceInfo) error {
	var (
		fields = model.Device{
			AreaID:         param.AreaID,
			BusinessTypeID: param.BusinessTypeID,
		}
		dds          = make([]model.DepartmentDevice, 0)
		gatewayIDs   = make([]int, 0)
		gatewayIDMap = make(map[int]struct{})
		ags          = make([]model.AreaGateway, 0)
		dgs          = make([]model.DepartmentGateway, 0)
		bt           model.BusinessType
		total        = len(param.IDs)
		dtc          = make([]model.DepartmentTypeCount, 0)
	)
	typeID, err := d.secondTypeID(param.BusinessTypeID)
	if err != nil {
		return errors.New("不允许归属于顶层业务类型")
	}
	err = d.db.Model(model.BusinessType{}).
		Where("id = ?", param.BusinessTypeID).
		First(&bt).
		Error
	if err != nil {
		return err
	}

	var existTotal int64
	err = d.db.Model(fields).
		Where("business_type_id = ?", param.BusinessTypeID).
		Count(&existTotal).
		Error
	if err != nil {
		return err
	}

	if bt.Number < int64(total)+existTotal {
		return errors.New("添加后该类型设备数量已超过授权上限")
	}
	for _, id := range param.IDs {
		for _, departmentID := range param.DepartmentIDs {
			dds = append(dds, model.DepartmentDevice{
				DepartmentID: departmentID,
				DeviceID:     id,
			})
		}
	}

	for _, id := range param.DepartmentIDs {
		dtc = append(dtc, model.DepartmentTypeCount{
			DepartmentID:   id,
			BusinessTypeID: typeID,
			Count:          total,
		})
	}

	err = d.db.Model(fields).Where("id in (?)", param.IDs).
		Pluck("gateway_id", &gatewayIDs).
		Error
	if err != nil {
		return err
	}

	for _, id := range gatewayIDs {
		gatewayIDMap[id] = struct{}{}
	}

	for id := range gatewayIDMap {
		ags = append(ags, model.AreaGateway{
			AreaID:    param.AreaID,
			GatewayID: id,
		})
		for _, departmentID := range param.DepartmentIDs {
			dgs = append(dgs, model.DepartmentGateway{
				DepartmentID: departmentID,
				GatewayID:    id,
			})
		}
	}

	return d.db.Transaction(func(tx *gorm.DB) error {
		err = tx.Model(model.Device{}).
			Where("id in (?)", param.IDs).
			Updates(fields).
			Error
		if err != nil {
			return err
		}
		err = tx.Model(model.DepartmentDevice{}).
			Create(&dds).
			Error
		if err != nil {
			return err
		}
		err = tx.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "area_id"}, {Name: "gateway_id"}},
			DoNothing: true,
		}).
			Model(model.AreaGateway{}).
			Create(ags).
			Error
		if err != nil {
			return err
		}
		err = tx.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "department_id"}, {Name: "gateway_id"}},
			DoNothing: true,
		}).
			Model(model.DepartmentGateway{}).
			Create(dgs).
			Error
		if err != nil {
			return err
		}

		err = tx.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "area_id"}, {Name: "business_type_id"}},
			DoUpdates: clause.Assignments(map[string]interface{}{"count": gorm.Expr("area_type_counts.count + ?", total)}),
		}).
			Model(model.AreaTypeCount{}).
			Create(&model.AreaTypeCount{
				AreaID:         param.AreaID,
				BusinessTypeID: typeID,
				Count:          total,
			}).
			Error
		if err != nil {
			return err
		}
		err = tx.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "department_id"}, {Name: "business_type_id"}},
			DoUpdates: clause.Assignments(map[string]interface{}{"count": gorm.Expr("department_type_counts.count + ?", total)}),
		}).
			Model(model.DepartmentTypeCount{}).
			Create(&dtc).
			Error
		if err != nil {
			return err
		}
		return nil
	})
}
