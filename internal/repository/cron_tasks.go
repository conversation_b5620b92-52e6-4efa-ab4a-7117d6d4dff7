package repository

import (
	"time"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/system/config"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type CronTaskRepo struct {
	db *gorm.DB
	l  *zap.Logger
}

func NewCronTaskRepo(db *gorm.DB, config *config.Config, log *zap.Logger) *CronTaskRepo {
	return &CronTaskRepo{
		db: db,
		l:  log.Named("cronTask_repository:"),
	}
}

// add
func (d *CronTaskRepo) Create(cron_task model.CronTask, config model.CronTaskConfig) error {
	return d.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(model.CronTask{}).Create(&cron_task).Error
		if err != nil {
			return err
		}
		config.ID = cron_task.ID
		return tx.Model(model.CronTaskConfig{}).Create(&config).Error
	})
}
func (d *CronTaskRepo) GetCronTaskList() ([]model.CronTask, error) {
	var (
		list []model.CronTask
	)

	info := d.db.Model(&model.CronTask{}).
		Order("id asc").
		Find(&list)
	if info.Error != nil {
		return nil, info.Error
	}

	return list, nil
}
func (lr *CronTaskRepo) GetCronTaskDetail(id int) (model.CronTaskConfig, error) {
	var detail = model.CronTaskConfig{
		ID: id,
	}
	err := lr.db.First(&detail).Error
	return detail, err
}
func (d *CronTaskRepo) GetCronTaskAll() ([]model.CronTaskAll, error) {
	var results []model.CronTaskAll
	err := d.db.Table("cron_task").
		Select("cron_task.*, cron_task_configs.condition, cron_task_configs.action").
		Joins("LEFT JOIN cron_task_configs ON cron_task.id = cron_task_configs.id").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}
	for index, cron_task := range results {
		if cron_task.Action == nil {
			continue
		}

		if cron_task.Action.ActionType == "LINK" {
			var info = response.LinkageName{}
			err := d.db.Model(model.Linkage{}).
				Where("id = ?", cron_task.Action.LinkId).
				Select("name").
				First(&info).
				Error
			if err != nil {
				d.l.Warn("链接不存在", zap.Int("link_id", cron_task.Action.LinkId), zap.Error(err))
				results[index].Action.LinkName = "未知联动"
			} else {
				results[index].Action.LinkName = info.Name
			}
		} else if cron_task.Action.ActionType == "NOTICE" {
			for _, group_id := range cron_task.Action.NoticeGroups {
				var group = model.NotificationGroup{
					ID: group_id,
				}
				info := d.db.Model(model.NotificationGroup{}).
					First(&group)
				if info.Error != nil {
					d.l.Warn("通知组不存在", zap.Int("group_id", group_id), zap.Error(info.Error))
					continue
				}
				results[index].Action.NoticeGroupsNames = append(results[index].Action.NoticeGroupsNames, group.Name)
			}
		}
	}
	return results, nil
}
func (d *CronTaskRepo) Delete(id int) error {
	return d.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Where("id = ?", id).Delete(&model.CronTask{}).Error
		if err != nil {
			return err
		}

		return tx.Model(model.CronTaskConfig{}).Where("id = ?", id).Delete(&model.CronTaskConfig{}).Error
	})
}
func (d *CronTaskRepo) Update(id int, cron_task model.CronTask, config model.CronTaskConfig) error {
	return d.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(&model.CronTask{}).
			Where("id = ?", id).
			Updates(&cron_task).
			Error
		if err != nil {
			return err
		}
		return tx.Model(&model.CronTaskConfig{}).
			Where("id = ?", id).
			Updates(&config).
			Error

	})
}
func (s *CronTaskRepo) UpdateTaskNextExecutionTime(id int, nextExecutionTime time.Time) error {
	var task model.CronTaskConfig
	err := s.db.Model(&model.CronTaskConfig{}).Where("id = ?", id).First(&task).Error
	if err != nil {
		return err
	}

	task.Condition.NextExecutionTime = nextExecutionTime
	return s.db.Model(&model.CronTaskConfig{}).Where("id = ?", id).Updates(map[string]interface{}{
		"condition": task.Condition,
	}).Error
}
