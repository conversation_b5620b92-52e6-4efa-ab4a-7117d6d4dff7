package repository

import (
	"tw_platform/pkg/model"

	"gorm.io/gorm"
)

type RoleRepo struct {
	db *gorm.DB
}

func NewRoleRepo(db *gorm.DB) *RoleRepo {
	return &RoleRepo{
		db: db,
	}
}

func (r *RoleRepo) Create(role model.Role) error {
	return r.db.Create(&role).Error
}

func (r *RoleRepo) ListByPage(page, size int, filter model.Role) ([]model.Role, int64, error) {
	var (
		total int64
		list  []model.Role
	)
	err := r.db.Model(model.Role{}).
		Where(filter).
		Count(&total).
		Limit(size).
		Offset((page - 1) * size).
		Order("id").
		Find(&list).
		Error
	return list, total, err
}

func (r *RoleRepo) Update(id int, role model.Role) error {
	return r.db.Model(model.Role{}).
		Where("id = ?", id).
		Updates(role).
		Error
}

func (r *RoleRepo) Delete(id int) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Where("id = ?", id).Delete(&model.Role{}).Error
		if err != nil {
			return err
		}
		return tx.Where("role_id = ?", id).Delete(&model.RoleMenu{}).Error
	})
}

func (r *RoleRepo) SetMenus(id int, ids []model.RoleMenu) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(model.RoleMenu{}).
			Where("role_id = ?", id).
			Delete(&model.RoleMenu{}).
			Error
		if err != nil {
			return err
		}
		if len(ids) == 0 {
			return nil
		}
		return tx.Model(model.RoleMenu{}).
			Create(&ids).
			Error
	})
}

func (r *RoleRepo) GetMenus(id int) ([]int, error) {
	var (
		ids = make([]int, 0)
	)
	err := r.db.Model(model.RoleMenu{}).
		Where("role_id = ?", id).
		Pluck("menu_id", &ids).
		Error
	return ids, err
}

func (r *RoleRepo) AllocableList(rid int) ([]model.Role, error) {
	var (
		list []model.Role
	)

	err := r.db.Model(model.Role{}).
		Where("id >= ?", rid).
		Find(&list).
		Error

	return list, err
}

func (r *RoleRepo) Permissions(id int) ([]int, error) {
	var (
		list = make([]int, 0)
	)
	err := r.db.Model(model.Permission{}).
		Where(
			"exists (?)",
			r.db.Model(model.RolePermission{}).
				Select("1").
				Where(
					"role_id = ? and permissions.id = role_permissions.permission_id",
					id,
				),
		).
		Pluck("id", &list).
		Error
	return list, err
}

func (r *RoleRepo) SetPermissions(rid int, ids []int) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		var (
			rp  model.RolePermission
			rps = make([]model.RolePermission, 0, len(ids))
		)
		err := tx.Model(rp).
			Where("role_id = ?", rid).
			Delete(&rp).
			Error
		if err != nil {
			return err
		}
		if len(ids) == 0 {
			return nil
		}
		for _, id := range ids {
			rps = append(rps, model.RolePermission{
				RoleID:       rid,
				PermissionID: id,
			})
		}
		return tx.Model(model.RolePermission{}).
			Create(&rps).
			Error
	})
}

func (r *RoleRepo) ListWithPermissions() ([]model.RoleWithPermissions, error) {
	var (
		list []model.RoleWithPermissions
	)

	err := r.db.Model(model.Role{}).
		Preload("Permissions").
		Find(&list).
		Error
	return list, err
}

func (r *RoleRepo) First(filter model.Role) (model.Role, error) {
	var role model.Role
	err := r.db.Where(filter).First(&role).Error
	return role, err
}
