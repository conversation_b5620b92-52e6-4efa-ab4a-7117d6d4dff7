package repository

import (
	"fmt"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/system/config"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type VideoRepo struct {
	db     *gorm.DB
	config *config.Config
	l      *zap.Logger
}
type FfmpegInfo struct {
	RTSP string
	RTMP string
}

func NewVideoRepo(db *gorm.DB, config *config.Config, log *zap.Logger) *VideoRepo {
	return &VideoRepo{
		db:     db,
		config: config,
		l:      log.Named("video_repository:"),
	}
}

func (us *VideoRepo) GetVideoList() ([]response.VideoListResponse, error) {
	var (
		list         []model.DeviceWithAreaName
		total        int64
		videoConfig  []model.Config
		configIds    []int64
		responseList []response.VideoListResponse
	)
	info := us.db.Model(model.Device{}).
		Select(
			"devices.id",
			"devices.name",
			"devices.type",
			"devices.activity",
			"devices.status",
			"devices.area_id",
			"a.name as area_name",
			"devices.business_type_id",
			"devices.created_at",
			"devices.updated_at",
		).
		Joins("left join areas as a on devices.area_id = a.id").
		Where("type = ?", "camera").
		Or("type = ?", "nvr").
		Order("id asc").
		Find(&list)
	err := info.Count(&total).Error
	if err != nil {
		return responseList, err
	}
	for i := range list {
		configIds = append(configIds, int64(list[i].ID))
	}

	if err = us.db.Model(model.Config{}).
		Where("id in ?", configIds).
		Order("id asc").
		Find(&videoConfig).Error; err != nil {
		return nil, err
	}

	for i := 0; i < len(videoConfig); i++ {
		responseList = append(responseList, response.VideoListResponse{
			ID:             list[i].ID,
			Type:           list[i].Type,
			Activity:       list[i].Activity,
			DeviceName:     list[i].Name,
			IP:             videoConfig[i].Config.VideoDevice.IP,
			Status:         list[i].Status,
			AreaID:         list[i].AreaID,
			AreaName:       list[i].AreaName,
			BusinessTypeID: list[i].BusinessTypeID,
			CreatedAt:      list[i].CreatedAt,
			UpdatedAt:      list[i].UpdatedAt,
			Name:           videoConfig[i].Config.VideoDevice.Name,
			Brand:          videoConfig[i].Config.VideoDevice.Brand,
			ChannelID:      videoConfig[i].Config.VideoDevice.ChannelID,
			Password:       videoConfig[i].Config.VideoDevice.Password,
			Stream:         videoConfig[i].Config.VideoDevice.Stream,
		})
	}

	//us.l.Info("video list", zap.Any("list", responseList))
	return responseList, err

}

func (us *VideoRepo) PlayVideo(param request.RealTimePlayRequest) (model.Config, error) {
	var (
		info model.Config
	)
	err := us.db.Model(model.Config{}).
		Where("id = ?", param.Id).
		Find(&info).
		Error
	if err != nil {
		return info, err
	}

	return info, err

}
func (us *VideoRepo) PlaybackVideo(param request.PlaybackRequest) (model.Config, error) {
	var (
		info model.Config
	)
	err := us.db.Model(model.Config{}).
		Where("id = ?", param.Id).
		Find(&info).
		Error
	if err != nil {
		return info, err
	}

	return info, err
}
func (us *VideoRepo) StopPlayingFFMPEG() {
	fmt.Println("user store create")
}
func (us *VideoRepo) CaptureImage(id int) (response.VideoConfigWithDeviceName, error) {
	var data response.VideoConfigWithDeviceName
	err := us.db.Model(model.Device{}).
		Select(
			"devices.id",
			"devices.name",
			"dc.type",
			"dc.config",
		).
		Joins("left join device_configs as dc on devices.id = dc.id").
		Where("devices.id = ?", id).
		Find(&data).
		Error
	return data, err
}
