package repository

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"strings"
	"time"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/system/influxdb"
	"tw_platform/pkg/system/table_name"
	"tw_platform/pkg/utils"

	"github.com/influxdata/influxdb-client-go/v2/api/write"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type StandbyRepo struct {
	db       *gorm.DB
	tables   *table_name.Tables
	influxdb *influxdb.Client
	l        *zap.Logger

	UnHandlerTables map[string]struct{}
}

func NewStandbyRepo(
	db *gorm.DB,
	tables *table_name.Tables,
	influxdb *influxdb.Client,
	l *zap.Logger,
) *StandbyRepo {
	return &StandbyRepo{
		db:       db,
		tables:   tables,
		influxdb: influxdb,
		l:        l.Named("standby_repository"),
		UnHandlerTables: map[string]struct{}{
			model.RegisterRepo{}.TableName():         {},
			model.Standby{}.TableName():              {},
			model.OperationLog{}.TableName():         {},
			model.BusinessType{}.TableName():         {},
			model.BusinessTypeRelation{}.TableName(): {},
		},
	}
}

func (s *StandbyRepo) GetStatus(info model.Standby) error {
	var (
		client = http.Client{
			Timeout: 5 * time.Second,
		}

		result struct {
			Code int    `json:"code"`
			Msg  string `json:"msg"`
		}
	)

	resp, err := client.Get(fmt.Sprintf("http://%s:%s/api/system/standbt_status", info.IP, info.Port))
	if err != nil {
		return err
	}

	err = json.NewDecoder(resp.Body).Decode(&result)
	if err != nil {
		return err
	}
	if result.Code != 1 {
		return errors.New(result.Msg)
	}
	return nil
}

func (s *StandbyRepo) SyncTables() (map[string]interface{}, error) {
	var (
		results = make(map[string]interface{})
		err     error
		et      = map[string]struct{}{
			model.AlarmLog{}.TableName():     {},
			model.Menu{}.TableName():         {},
			model.RegisterRepo{}.TableName(): {},
			model.OperationLog{}.TableName(): {},
			model.Standby{}.TableName():      {},

			model.HistoryAlarm{}.TableName(): {},
		}
	)
	for table, model := range *s.tables {
		_, ok := et[table]
		if ok {
			continue
		}
		list := model
		err = s.db.Table(table).
			Find(&list).
			Error
		if err != nil {
			break
		}

		results[table] = list
	}
	return results, err
}

func (s *StandbyRepo) GetSyncTables(param model.Standby) (string, error) {
	var (
		client = http.Client{
			Timeout: 5 * time.Minute,
		}

		result struct {
			Code int                 `json:"code"`
			Msg  string              `json:"msg"`
			Data response.SyncTables `json:"data"`
		}

		path string
	)

	resp, err := client.Get(fmt.Sprintf(
		"http://%s:%s/api/v1/standby/sync_tables?timestamp=%d",
		param.IP,
		param.Port,
		time.Now().Unix(),
	))
	if err != nil {
		return path, err
	}

	defer resp.Body.Close()

	err = json.NewDecoder(resp.Body).Decode(&result)
	if err != nil {
		return path, err
	}
	if result.Code != 1 {
		return path, errors.New(result.Msg)
	}

	resp1, err := client.Get(fmt.Sprintf(
		"http://%s:%s/%s",
		param.IP,
		param.Port,
		result.Data.Path,
	))
	if err != nil {
		return path, err
	}
	defer resp1.Body.Close()
	f, err := os.Create(result.Data.Path)
	if err != nil {
		return path, err
	}
	defer f.Close()
	reader := io.TeeReader(resp1.Body, f)

	sum, err := utils.FDMD5(reader)
	if err != nil {
		return path, err
	}
	if sum != result.Data.MD5 {
		os.Remove(result.Data.Path)
		return path, errors.New("MD5对比不一致")
	}
	return result.Data.Path, nil
}

func (s *StandbyRepo) Sync(data map[string]interface{}) []string {
	var failed []string
	for table, list := range data {
		err := s.db.Exec(fmt.Sprintf(
			"TRUNCATE TABLE %s RESTART IDENTITY",
			table,
		)).
			Error
		if err != nil {
			failed = append(failed, table)
			continue
		}

		err = s.db.Table(table).Create(list).Error
		if err != nil {
			failed = append(failed, table)
			continue
		}
	}

	return failed
}

func (s *StandbyRepo) SetStandby(standby model.Standby) error {
	return s.db.Model(standby).
		Where("id = ?", standby.ID).
		Updates(standby).
		Error
}

func (s *StandbyRepo) GetStandby(id int) (model.Standby, error) {
	var standby model.Standby
	err := s.db.Where("id = ?", id).First(&standby).Error
	return standby, err
}

func (s *StandbyRepo) PushTables(info model.Standby, path string) error {
	var (
		client = http.Client{
			Timeout: 5 * time.Minute,
		}
		buffer bytes.Buffer
		mw     = multipart.NewWriter(&buffer)
	)

	fd, err := os.Open(path)
	if err != nil {
		return err
	}
	defer fd.Close()

	fw, err := mw.CreateFormFile("tables", path)
	if err != nil {
		return err
	}

	_, err = io.Copy(fw, fd)
	if err != nil {
		return err
	}

	err = mw.Close()
	if err != nil {
		return err
	}

	req, err := http.NewRequest(
		http.MethodPost,
		fmt.Sprintf(
			"http://%s:%s/api/v1/standby/fetch_tables?timestamp=%d",
			info.IP,
			info.Port,
			time.Now().Unix(),
		),
		&buffer,
	)

	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", mw.FormDataContentType())

	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("fetch tables errorcode:%d", resp.StatusCode)
	}
	defer resp.Body.Close()
	var result struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data []string
	}

	err = json.NewDecoder(resp.Body).Decode(&result)
	if err != nil {
		return err
	}

	if result.Code != 1 {
		return errors.New(result.Msg)
	}
	return nil
}

func (s *StandbyRepo) DeviceData(begin, end string) ([]request.StandbyData, error) {
	var (
		sb          strings.Builder
		ctx, cancel = context.WithTimeout(context.Background(), time.Minute)
		results     = make([]request.StandbyData, 0)
	)
	defer cancel()
	location, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return results, err
	}

	sb.WriteString(fmt.Sprintf(
		`from(bucket:"tw_platform") |> range(start: %s, stop: %s)`,
		begin,
		end,
	))

	result, err := s.influxdb.Query.Query(ctx, sb.String())
	if err != nil {
		return results, err
	}

	for result.Next() {
		var (
			record = result.Record()
			tmp    = record.Value()
			tag    string
			value  float64
			ok     bool
		)
		field := record.Field()
		measurement := record.Measurement()
		id := record.ValueByKey("id")
		tag, ok = id.(string)
		if !ok {
			s.l.Warn("id is not string", zap.Any("id", id))
			continue
		}
		value, ok = tmp.(float64)
		if !ok {
			s.l.Warn("value is not float64", zap.Any("value", tmp))
			continue
		}
		results = append(results, request.StandbyData{
			Measurement: measurement,
			Tags:        tag,
			Fields: map[string]float64{
				field: value,
			},
			Time: record.Time().In(location).Format(time.DateTime),
		})
	}

	return results, nil
}

func (s *StandbyRepo) FetchDeviceData(lists []request.StandbyData) error {
	var (
		ctx, cancel = context.WithTimeout(context.Background(), time.Minute)
		points      = make([]*write.Point, 0, len(lists))
	)
	defer cancel()

	for _, param := range lists {
		t, err := time.Parse(time.DateTime, param.Time)
		if err != nil {
			s.l.Warn("parse time failure", zap.Error(err))
			continue
		}

		var fields = make(map[string]interface{}, len(param.Fields))
		for k, v := range param.Fields {
			fields[k] = v
		}

		points = append(points, write.NewPoint(
			param.Measurement,
			map[string]string{
				"id": param.Tags,
			},
			fields,
			t,
		))
	}
	err := s.influxdb.Write.WritePoint(ctx, points...)
	return err
}

func (s *StandbyRepo) TruncateTables() error {
	var (
		ok bool

		sb strings.Builder
	)

	for table := range *s.tables {
		_, ok = s.UnHandlerTables[table]
		if ok {
			fmt.Println(table)
			continue
		}
		sb.WriteString(fmt.Sprintf(
			"TRUNCATE TABLE %s RESTART IDENTITY;\n",
			table,
		))
	}
	return s.db.Debug().Exec(sb.String()).Error
}

func (s *StandbyRepo) RestartIdentity() {
	for table := range *s.tables {
		s.db.Exec(fmt.Sprintf(
			"select setval('%s_id_seq',(select max(id) from %s))",
			table,
			table,
		))
	}
}
