package repository

import (
	"tw_platform/pkg/model"

	"gorm.io/gorm"
)

type CronjobRepo struct {
	db *gorm.DB
}

func NewCronjobRepo(db *gorm.DB) *CronjobRepo {
	return &CronjobRepo{
		db: db,
	}
}

func (cr *CronjobRepo) Create(job model.CronJob) error {
	return cr.db.Model(job).
		Create(&job).
		Error
}

func (cr *CronjobRepo) Update(job model.CronJob) error {
	return cr.db.Model(job).
		Where("id = ?", job.ID).
		Updates(job).
		Error
}

func (cr *CronjobRepo) First(filter model.CronJob) (model.CronJob, error) {
	var job model.CronJob
	err := cr.db.Model(job).
		Where(filter).
		First(&job).
		Error
	return job, err
}

func (cr *CronjobRepo) List(page, size int, filter model.CronJob) ([]model.CronJob, int64, error) {
	var (
		list  []model.CronJob
		total int64
	)
	err := cr.db.Model(model.CronJob{}).
		Where(filter).
		Count(&total).
		Limit(size).
		Offset((page - 1) * size).
		Order("id desc").
		Find(&list).
		Error
	return list, total, err
}

func (cr *CronjobRepo) Delete(id int) error {
	return cr.db.Where("id = ?", id).Delete(&model.CronJob{}).Error
}
