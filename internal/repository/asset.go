package repository

import (
	"errors"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/response"

	"gorm.io/gorm"
)

type AssetRepo struct {
	db *gorm.DB
}

func NewAssetRepo(db *gorm.DB) *AssetRepo {
	return &AssetRepo{
		db: db,
	}
}

func (a *AssetRepo) CreateCabinet(asset model.Cabinet) error {
	err := a.db.Create(&asset).Error
	if errors.Is(err, gorm.ErrDuplicatedKey) {
		return errors.New("该机柜ID已存在")
	}
	return err
}

func (a *AssetRepo) UpdateCabinet(asset model.Cabinet) error {
	return a.db.Where("custom_id = ?", asset.CustomID).Updates(&asset).Error
}

func (a *AssetRepo) CabinetList(areaID, businessTypeID int) ([]model.Cabinet, error) {
	var list []model.Cabinet
	err := a.db.Model(model.Cabinet{}).
		Where("area_id = ? and business_type_id = ?", areaID, businessTypeID).
		Find(&list).
		Error
	return list, err
}

func (a *AssetRepo) DeleteCabinet(ids []string) error {
	return a.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(model.Cabinet{}).
			Where("custom_id in (?)", ids).
			Delete(&model.Cabinet{}).
			Error
		if err != nil {
			return err
		}

		return tx.Model(model.Asset{}).
			Where("cabinet_id in (?)", ids).
			Update("cabinet_id", "").
			Error
	})
}

func (a *AssetRepo) CreateAsset(asset model.Asset) error {
	return a.db.Create(&asset).Error
}

func (a *AssetRepo) UpdateAsset(asset model.Asset) error {
	return a.db.Where("id = ?", asset.ID).Updates(asset).Error
}

func (a *AssetRepo) ListByCabinetID(cabinetIDs []string) (model.Asset, error) {
	var asset model.Asset
	err := a.db.Model(model.Asset{}).
		Where("cabinet_id in (?)", cabinetIDs).
		First(&asset).
		Error
	return asset, err
}

func (a *AssetRepo) List(filter model.Asset) ([]response.AssetList, error) {
	var list []response.AssetList
	err := a.db.Model(model.Asset{}).
		Select(
			"assets.*",
			"cabinets.name as cabinet_name",
		).
		Joins("inner join cabinets on assets.cabinet_id = cabinets.custom_id").
		Where(filter).
		Find(&list).
		Error
	return list, err
}

func (a *AssetRepo) Delete(ids []int) error {
	return a.db.Where("id in (?)", ids).
		Delete(&model.Asset{}).
		Error
}
