package repository

import (
	"tw_platform/pkg/model"
	"tw_platform/pkg/system/config"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type SnmpTrapRepo struct {
	db     *gorm.DB
	config *config.Config
	l      *zap.Logger
}

func NewSnmpRepo(db *gorm.DB, config *config.Config, log *zap.Logger) *SnmpTrapRepo {
	return &SnmpTrapRepo{
		db:     db,
		config: config,
		l:      log.Named("snmptrap_repository:"),
	}
}

// add
func (d *SnmpTrapRepo) Create(snmp model.SnmpTrap) error {
	return d.db.Model(model.SnmpTrap{}).Create(&snmp).Error
}
func (d *SnmpTrapRepo) GetSnmpTrapList() ([]model.SnmpTrap, error) {
	var (
		list []model.SnmpTrap
	)

	err := d.db.Model(model.SnmpTrap{}).
		Order("id asc").
		Find(&list).
		Error

	return list, err
}
func (d *SnmpTrapRepo) Delete(id int) error {
	return d.db.
		Where("id = ?", id).
		Delete(&model.SnmpTrap{}).
		Error
}
func (d *SnmpTrapRepo) Update(snmp model.SnmpTrap, id int) error {
	return d.db.Model(model.SnmpTrap{}).
		Where("id = ?", id).
		Updates(&snmp).
		Error
}
