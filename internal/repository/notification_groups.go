package repository

import (
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/system/config"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type NotificationGroupRepo struct {
	db     *gorm.DB
	config *config.Config
	l      *zap.Logger
}

func NewNotificationGroupRepo(db *gorm.DB, config *config.Config, log *zap.Logger) *NotificationGroupRepo {
	return &NotificationGroupRepo{
		db:     db,
		config: config,
		l:      log.Named("notification_repository:"),
	}
}

// add
func (d *NotificationGroupRepo) Create(group model.NotificationGroup, users []model.NotificationUsers) error {
	return d.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(model.NotificationGroup{}).Create(&group).Error
		if err != nil {
			return err
		}
		for _, user := range users {
			user.NotificationGroupId = group.ID
			err = tx.Model(model.NotificationUsers{}).Create(&user).Error
			if err != nil {
				return err
			}
		}
		return err
	})
}
func (d *NotificationGroupRepo) GetNotificationGroupList() ([]response.ListNotificationGroup, error) {
	var (
		group []model.NotificationGroup
		list  []response.ListNotificationGroup
	)

	info := d.db.Model(model.NotificationGroup{}).
		Order("id asc").
		Find(&group)
	if info.Error != nil {
		return nil, info.Error
	}
	for _, l := range group {
		usersWithName := []response.NotificationUsersWithName{}
		info := d.db.Model(model.NotificationUsers{}).
			Select("notification_users.*, users.name").
			Joins("left join users on users.id = notification_users.user_id").
			Where("notification_users.notification_id = ?", l.ID).
			Find(&usersWithName)
		if info.Error != nil {
			return nil, info.Error
		}
		list = append(list, response.ListNotificationGroup{
			NotificationGroup: l,
			NotificationUsers: usersWithName,
		})
	}

	return list, nil
}
func (d *NotificationGroupRepo) GetNotificationGroupDetail(id int) (response.ListNotificationGroupDetail, error) {
	var (
		group model.NotificationGroup
		list  response.ListNotificationGroupDetail
	)
	info := d.db.Model(model.NotificationGroup{}).
		Where("id = ? ", id).
		First(&group)
	if info.Error != nil {
		return list, info.Error
	}
	users := []model.User{}
	info = d.db.Model(model.NotificationUsers{}).
		Select("users.*").
		Joins("left join users on users.id = notification_users.user_id").
		Where("notification_users.notification_id = ?", group.ID).
		Scan(&users)
	if info.Error != nil {
		return list, info.Error
	}

	list = response.ListNotificationGroupDetail{
		NotificationGroup: group,
		NotificationUsers: users,
	}
	return list, nil
}
func (d *NotificationGroupRepo) Delete(id int) error {
	return d.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Where("id = ?", id).
			Delete(&model.NotificationGroup{}).
			Error
		if err != nil {
			return err
		}

		return tx.Model(model.NotificationUsers{}).
			Where("notification_id = ?", id).
			Delete(&model.Config{}).
			Error
	})
}
func (d *NotificationGroupRepo) Update(group model.NotificationGroup, users []model.NotificationUsers, id int) error {
	return d.db.Transaction(func(tx *gorm.DB) error {
		// 更新 NotificationGroup 信息
		err := tx.Model(&model.NotificationGroup{}).
			Where("id = ?", id).
			Updates(&group).
			Error
		if err != nil {
			return err
		}

		// 删除旧的关联 NotificationUsers
		err = tx.Where("notification_id = ?", id).
			Delete(&model.NotificationUsers{}).
			Error
		if err != nil {
			return err
		}

		// 插入新的关联 NotificationUsers
		for _, user := range users {
			user.NotificationGroupId = id
			err = tx.Model(&model.NotificationUsers{}).
				Create(&user).
				Error
			if err != nil {
				return err
			}
		}

		return nil
	})
}
func (d *NotificationGroupRepo) GetGprsConfig() (model.Gprs, error) {
	var config model.NoticeSystemConfig
	if err := d.db.First(&config).Error; err != nil {
		return config.Gprs, err
	}
	return config.Gprs, nil
}
func (d *NotificationGroupRepo) UpdateGprsConfig(gprs model.Gprs) error {
	var config model.NoticeSystemConfig
	if err := d.db.First(&config).Error; err != nil {
		return err
	}
	config.Gprs = gprs
	if err := d.db.Save(&config).Error; err != nil {
		return err
	}
	return nil
}
func (d *NotificationGroupRepo) GetEmailConfig() (model.EmailClient, error) {
	var config model.NoticeSystemConfig
	if err := d.db.First(&config).Error; err != nil {
		return config.Email, err
	}
	return config.Email, nil
}
func (d *NotificationGroupRepo) UpdateEmailConfig(email model.EmailClient) error {
	var config model.NoticeSystemConfig
	if err := d.db.First(&config).Error; err != nil {
		return err
	}
	config.Email = email
	if err := d.db.Save(&config).Error; err != nil {
		return err
	}
	return nil
}
