package repository

import (
	"errors"
	"fmt"
	"tw_platform/pkg/model"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type DepartmentRepo struct {
	db *gorm.DB
}

func NewDepartmentRepo(db *gorm.DB) *DepartmentRepo {
	return &DepartmentRepo{
		db: db,
	}
}

func (d *DepartmentRepo) Create(department model.Department, relations []model.DepartmentRelation) error {
	return d.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Create(&department).Error
		if err != nil {
			return err
		}
		for i := 0; i < len(relations); i++ {
			relations[i].ChildID = department.ID
			relations[i].Depth += 1
		}
		relations = append(relations, model.DepartmentRelation{
			ParentID: department.ID,
			ChildID:  department.ID,
			Depth:    0,
		})
		return tx.Model(model.DepartmentRelation{}).Create(&relations).Error
	})
}

func (d *DepartmentRepo) All() ([]model.Department, error) {
	var departments []model.Department
	err := d.db.Model(model.Department{}).
		Select(
			"id",
			"name",
			"parent_id",
			"status",
		).
		Order("id asc").
		Find(&departments).Error
	return departments, err
}

func (d *DepartmentRepo) FindByUserID(userID int) ([]model.Department, error) {
	var (
		list []model.Department
	)

	err := d.db.Model(model.Department{}).
		Select(
			"id",
			"name",
			"CASE WHEN user_departments.department_id iS NULL THEN -1 ELSE departments.parent_id END AS parent_id",
		).
		Joins("LEFT JOIN user_departments ON departments.parent_id = user_departments.department_id AND user_departments.user_id = ?", userID).
		Where(
			"EXISTS (?)",
			d.db.Model(model.UserDepartment{}).
				Select("1").
				Where("user_id = ? and departments.id = user_departments.department_id", userID),
		).
		Order("id asc").
		Find(&list).
		Error
	return list, err
}

func (d *DepartmentRepo) FindByUserIDWithTypeCounts(userID, businessTypeID int) ([]model.DepartmentWithTypeCount, error) {
	var (
		list   []model.DepartmentWithTypeCount
		sqlStr = `  (select true from devices d where exists (select 1 from department_devices dd where dd.department_id = dtc.department_id and dd.device_id = d.id) and exists 
		(SELECT 1 FROM business_type_relations WHERE parent_id = dtc.business_type_id AND child_id = d.business_type_id) and d.status!=%d limit 1) as alarmed`
	)

	err := d.db.Model(model.Department{}).
		Select(
			"id",
			"name",
			"CASE WHEN user_departments.department_id iS NULL THEN -1 ELSE departments.parent_id END AS parent_id",
		).
		Joins("LEFT JOIN user_departments ON departments.parent_id = user_departments.department_id AND user_departments.user_id = ?", userID).
		Where(
			"EXISTS (?)",
			d.db.Model(model.UserDepartment{}).
				Select("1").
				Where("user_id = ? and departments.id = user_departments.department_id", userID),
		).
		Preload("Types", func(db *gorm.DB) *gorm.DB {
			return db.Table("department_type_counts dtc").Debug().
				Select(
					"dtc.department_id",
					"dtc.business_type_id",
					"dtc.count",
					"bt.name",
					"bt.id",
					fmt.Sprintf(sqlStr, model.DeviceStatusNormal),
				).
				Where(
					"EXISTS (?)",
					d.db.Model(model.UserBusinessType{}).
						Select("1").
						Where("user_id = ? and user_business_types.business_type_id = dtc.business_type_id", userID).
						Where(
							"EXISTS (?)",
							d.db.Model(model.BusinessTypeRelation{}).
								Select("1").
								// Where("parent_id = ? and depth = 1 and child_id = user_business_types.business_type_id", businessTypeID),
								Where("parent_id = ? and child_id = user_business_types.business_type_id", businessTypeID),
						),
				).
				Joins("inner join business_types bt on dtc.business_type_id = bt.id")
		}).
		Order("id asc").
		Find(&list).
		Error
	return list, err
}

func (d *DepartmentRepo) AllWithTypeCounts() ([]model.DepartmentWithTypeCount, error) {
	var (
		list []model.DepartmentWithTypeCount
	)
	err := d.db.Model(model.Department{}).
		Select(
			"id",
			"name",
			"parent_id",
		).
		Preload("Types", func(db *gorm.DB) *gorm.DB {
			return db.Table("department_type_counts dtc").Debug().
				Select(
					"dtc.department_id",
					"dtc.business_type_id",
					"dtc.count",
					"bt.name",
					"bt.id",
				).
				Joins("inner join business_types bt on dtc.business_type_id = bt.id")
		}).
		Order("id asc").
		Find(&list).
		Error
	return list, err
}

func (d *DepartmentRepo) RelationListByChildID(id int) ([]model.DepartmentRelation, error) {
	var relations []model.DepartmentRelation
	err := d.db.Model(model.DepartmentRelation{}).
		Where("child_id = ?", id).
		Find(&relations).
		Error
	return relations, err
}

func (d *DepartmentRepo) countIncrease(tx *gorm.DB, departmentIDs []int, typeId, step int) error {
	var (
		total   = int64(len(departmentIDs))
		counts  = make([]model.DepartmentTypeCount, 0, total)
		success = make(map[int]struct{}, 0)
		retry   = make([]model.DepartmentTypeCount, 0)
	)

	rows := tx.Model(&counts).Clauses(clause.Returning{
		Columns: []clause.Column{
			{Name: "count"},
			{Name: "department_id"},
		},
	}).
		Where(
			"department_id IN (?) AND business_type_id = ?",
			departmentIDs,
			typeId,
		).
		Update(
			"count",
			gorm.Expr("count + ?", step),
		).
		RowsAffected

	if rows == total {
		return nil
	}

	for _, count := range counts {
		success[count.DepartmentID] = struct{}{}
	}

	for _, id := range departmentIDs {
		if _, ok := success[id]; ok {
			continue
		}

		retry = append(retry, model.DepartmentTypeCount{
			DepartmentID:   id,
			BusinessTypeID: typeId,
			Count:          step,
		})
	}

	return tx.Model(model.DepartmentTypeCount{}).Create(&retry).Error
}

func (d *DepartmentRepo) countReduce(tx *gorm.DB, departmentIDs []int, typeID, step int) error {
	var (
		total   = int64(len(departmentIDs))
		counts  = make([]model.DepartmentTypeCount, 0, total)
		retry   = make([]int, 0, total)
		deletes model.DepartmentTypeCount
	)

	err := tx.Model(&counts).Clauses(clause.Returning{
		Columns: []clause.Column{
			{Name: "department_id"},
			{Name: "count"},
		},
	}).
		Where(
			"department_id in (?) AND business_type_id = ?",
			departmentIDs,
			typeID,
		).
		Update(
			"count",
			gorm.Expr("count - ?", step),
		).
		Error

	if err != nil {
		return err
	}

	for _, count := range counts {
		if count.Count > 0 {
			continue
		}

		retry = append(retry, count.DepartmentID)
	}

	return tx.Model(deletes).
		Where(
			"department_id in (?) and business_type_id = ?",
			retry,
			typeID,
		).
		Delete(&deletes).
		Error

}

func (d *DepartmentRepo) IDsByDevice(deviceID int) ([]int, error) {
	var ids []int
	err := d.db.Model(model.DepartmentDevice{}).
		Where("device_id = ?", deviceID).
		Pluck("department_id", &ids).
		Error
	return ids, err
}

func (d *DepartmentRepo) Update(id int, dpt model.Department) error {
	return d.db.Model(dpt).
		Where("id = ?", id).
		Updates(dpt).
		Error
}

func (d *DepartmentRepo) Delete(id int) error {
	var (
		department model.Department
		ud         model.UserDepartment
	)
	return d.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(department).
			Where("id = ?", id).
			Delete(&department).
			Error
		if err != nil {
			return err
		}

		return tx.Model(ud).
			Where("department_id = ?", id).
			Delete(&ud).
			Error
	})
}

func (d *DepartmentRepo) HasDevice(id int) (bool, error) {
	var dd model.DepartmentDevice
	err := d.db.Model(dd).
		Where("department_id = ?", id).
		First(&dd).
		Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, nil
		}
		return true, err
	}

	return true, nil
}

func (d *DepartmentRepo) First(filter model.Department) (model.Department, error) {
	var dpt model.Department
	err := d.db.Model(dpt).
		Where(filter).
		First(&dpt).
		Error
	return dpt, err
}

func (d *DepartmentRepo) ListByFilter(filter model.Department) ([]model.Department, error) {
	var dpts []model.Department
	err := d.db.Model(dpts).
		Where(filter).
		Find(&dpts).
		Error
	return dpts, err
}
