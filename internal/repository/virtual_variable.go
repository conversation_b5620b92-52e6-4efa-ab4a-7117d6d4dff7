package repository

import (
	"encoding/json"
	"fmt"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/system/config"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type VirtualVariableRepo struct {
	db *gorm.DB
	l  *zap.Logger
}

func NewVirtualVariableRepo(db *gorm.DB, config *config.Config, log *zap.Logger) *VirtualVariableRepo {
	return &VirtualVariableRepo{
		db: db,
		l:  log.Named("vitualvariable_repository:"),
	}
}
func (d *VirtualVariableRepo) Create(virtual model.VirtualDevice) error {
	return d.db.Model(model.VirtualDevice{}).Create(&virtual).Error
}
func (d *VirtualVariableRepo) List() ([]model.VirtualDevice, error) {
	var results []model.VirtualDevice
	err := d.db.Model(model.VirtualDevice{}).
		Order("id").
		Scan(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}
func (d *VirtualVariableRepo) Update(id int, device model.VirtualDevice) (int64, error) {
	var updates map[string]interface{} = make(map[string]interface{})
	if device.Name != "" {
		updates["name"] = device.Name
	}
	fmt.Println(device)
	if len(device.Unit) != 0 {
		unitJson, err := json.Marshal(device.Unit)
		if err != nil {
			return 0, err
		}
		updates["unit"] = string(unitJson)
	}
	fmt.Println(updates)
	builder := d.db.Model(&model.VirtualDevice{}).
		Where("id = ?", id).
		Updates(updates)

	return builder.RowsAffected, builder.Error
}
func (d *VirtualVariableRepo) UpdateUnit(param request.UpdateVirtualUnit) (int64, error) {

	var device model.VirtualDevice
	if err := d.db.Where("id = ?", param.DeviceID).First(&device).Error; err != nil {
		return 0, err
	}
	updated := false
	for i, unit := range device.Unit {
		if unit.Id == param.UnitID {
			if name, ok := param.Fields["name"]; ok {
				device.Unit[i].Name = name.(string)
				updated = true
			}
			if value, ok := param.Fields["value"]; ok {
				device.Unit[i].Value = value.(string)
				updated = true
			}
			break
		}
	}
	if !updated {
		return 0, fmt.Errorf("unit with id %d not found", param.UnitID)
	}
	unitJSON, err := json.Marshal(device.Unit)
	if err != nil {
		return 0, err
	}
	builder := d.db.Model(&device).
		Where("id = ?", param.DeviceID).
		Update("unit", unitJSON)

	return builder.RowsAffected, builder.Error
}
func (d *VirtualVariableRepo) Ddelet(id int) error {
	return d.db.
		Where("id = ?", id).
		Delete(&model.VirtualDevice{}).
		Error
}
func (d *VirtualVariableRepo) Detail(id int) (model.VirtualDevice, error) {
	var detail = model.VirtualDevice{
		ID: id,
	}
	err := d.db.First(&detail).Error
	return detail, err
}
