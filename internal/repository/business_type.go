package repository

import (
	"fmt"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"

	"gorm.io/gorm"
)

type BusinessTypeRepo struct {
	db *gorm.DB
}

func NewBusinessTypeRepo(db *gorm.DB) *BusinessTypeRepo {
	return &BusinessTypeRepo{
		db: db,
	}
}

func (b *BusinessTypeRepo) Create(businessType model.BusinessType, relations []model.BusinessTypeRelation) error {
	return b.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Create(&businessType).Error
		if err != nil {
			return err
		}

		for i := 0; i < len(relations); i++ {
			relations[i].ChildID = businessType.ID
			relations[i].Depth += 1
		}
		relations = append(relations, model.BusinessTypeRelation{
			ParentID: businessType.ID,
			ChildID:  businessType.ID,
			Depth:    0,
		})

		return tx.Model(model.BusinessTypeRelation{}).Create(&relations).Error
	})
}

func (b *BusinessTypeRepo) RelationListByChildID(id int) ([]model.BusinessTypeRelation, error) {
	var relations []model.BusinessTypeRelation
	err := b.db.Model(model.BusinessTypeRelation{}).
		Where("child_id = ?", id).
		Find(&relations).
		Error
	return relations, err
}

func (b *BusinessTypeRepo) All() ([]model.BusinessType, error) {
	var list []model.BusinessType
	err := b.db.Model(model.BusinessType{}).
		Order("sort,id asc").
		Find(&list).
		Error
	return list, err
}

func (b *BusinessTypeRepo) AllWithDevices() ([]model.BusinessTypeWithDevice, error) {
	var (
		list []model.BusinessTypeWithDevice
	)

	err := b.db.Model(model.BusinessType{}).
		Preload("Devices", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "business_type_id", "name", "type")
		}).
		Order("sort,id asc").
		Find(&list).
		Error
	return list, err
}

func (b *BusinessTypeRepo) Update(businessType model.BusinessType) error {
	return b.db.Model(businessType).
		Where("id = ?", businessType.ID).
		Updates(&businessType).
		Error
}

func (b *BusinessTypeRepo) First(filter model.BusinessType) (model.BusinessType, error) {
	var businessType model.BusinessType
	err := b.db.Where(filter).First(&businessType).Error
	return businessType, err
}

func (b *BusinessTypeRepo) Delete(id int) error {
	return b.db.Transaction(func(tx *gorm.DB) error {
		var btr model.BusinessTypeRelation
		err := tx.Model(btr).
			Where("child_id = ?", id).
			Delete(&btr).
			Error
		if err != nil {
			return err
		}
		return tx.Where("id = ?", id).Delete(&model.BusinessType{}).Error
	})
}

func (b *BusinessTypeRepo) Find(filter model.BusinessType) ([]model.BusinessType, error) {
	var list []model.BusinessType
	err := b.db.Model(model.BusinessType{}).
		Select("id", "name").
		Where(filter).
		Order("sort,id asc").
		Find(&list).
		Error
	return list, err
}

func (b *BusinessTypeRepo) FindByPID(param request.FindBusinessTypeByPID) ([]model.BusinessType, error) {
	var (
		list []model.BusinessType
	)

	err := b.db.Model(model.BusinessType{}).
		Where(
			"exists (?) and parent_id = ?",
			b.db.Model(model.UserBusinessType{}).
				Select("1").
				Where("user_id = ? and business_type_id = business_types.id", param.UID),
			param.PID,
		).
		Order("sort,id asc").
		Find(&list).
		Error
	return list, err
}

func (b *BusinessTypeRepo) FindHasDevice(pid, areaID, uid int) ([]model.BusinessType, error) {
	var list []model.BusinessType
	err := b.db.Model(model.BusinessType{}).Debug().
		Select("id", "name").
		Where(
			"exists (?)",
			b.db.Model(model.AreaTypeCount{}).
				Select("1").
				Where(
					"area_id = ? and business_type_id = business_types.id",
					areaID,
				),
		).
		Where("parent_id = ?", pid).
		Where(
			"exists (?)",
			b.db.Model(model.UserBusinessType{}).
				Select("1").
				Where("user_id = ? and business_type_id = business_types.id", uid),
		).
		Find(&list).
		Error
	return list, err
}

func (b *BusinessTypeRepo) ListByTopLevel(btID int, filter model.BusinessType) ([]model.BusinessType, error) {
	var list []model.BusinessType
	err := b.db.Model(model.BusinessType{}).
		Where(
			"EXISTS (?)",
			b.db.Model(model.BusinessTypeRelation{}).
				Select("1").
				Where("parent_id = ? and child_id = business_types.id", btID),
		).
		Where(filter).
		Order("sort,id asc").
		Find(&list).
		Error
	return list, err
}

func (b *BusinessTypeRepo) CreateByMandate(bts []model.BusinessType, btrs []model.BusinessTypeRelation, regStr string) error {
	var (
		bt  model.BusinessType
		btr model.BusinessTypeRelation
		reg = model.RegisterRepo{
			RegInfo: regStr,
		}
	)
	return b.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Exec(
			fmt.Sprintf("TRUNCATE TABLE %s,%s,%s RESTART IDENTITY",
				bt.TableName(),
				btr.TableName(),
				reg.TableName(),
			),
		).Error
		if err != nil {
			return err
		}

		err = tx.Create(&reg).Error
		if err != nil {
			return err
		}

		err = tx.Create(&bts).Error
		if err != nil {
			return err
		}
		return tx.Create(&btrs).Error
	})
}

func (b *BusinessTypeRepo) FindByUserID(param request.BusinessTypeTreeOnlyUser) ([]model.BusinessType, error) {
	var list []model.BusinessType
	builder := b.db.Model(model.BusinessType{}).
		Select(
			"id",
			"name",
			"sort",
			"number",
			"CASE WHEN ubt.business_type_id iS NULL THEN -1 ELSE business_types.parent_id END AS parent_id",
		).
		Joins(fmt.Sprintf(
			"LEFT JOIN %s ubt ON business_types.parent_id = ubt.business_type_id AND ubt.user_id = ?",
			model.UserBusinessType{}.TableName(),
		), param.UID).
		Where(
			"exists (?)",
			b.db.Model(model.UserBusinessType{}).
				Select("1").
				Where("user_id = ? and user_business_types.business_type_id = business_types.id", param.UID),
		)

	if param.PID != 0 {
		builder = builder.Where(
			"exists (?)",
			b.db.Model(model.BusinessTypeRelation{}).
				Select("1").
				Where("parent_id = ? and child_id = business_types.id", param.PID),
		)
	}

	err := builder.Find(&list).Error
	return list, err
}

func (b *BusinessTypeRepo) CountByPID(uid, pid int) (int64, error) {
	var (
		bt    model.BusinessType
		ubt   model.UserBusinessType
		total int64
	)
	err := b.db.Model(bt).
		Where(
			"exists (?) and parent_id = ?",
			b.db.Model(ubt).
				Select("1").
				Where(fmt.Sprintf(
					"user_id = ? and business_type_id = %s.id",
					model.BusinessType{}.TableName(),
				), uid),
			pid,
		).
		Count(&total).
		Error
	return total, err
}

func (b *BusinessTypeRepo) AllBySelect(selector []string) ([]model.BusinessType, error) {
	var list []model.BusinessType
	err := b.db.Model(model.BusinessType{}).
		Select(selector).
		Find(&list).
		Error
	return list, err
}

func (b *BusinessTypeRepo) ListWithDevicesByPID(param request.BusinessTypeTreeWithDevice) ([]model.BusinessTypeWithDevice, error) {
	var (
		list []model.BusinessTypeWithDevice
	)

	err := b.db.Model(model.BusinessType{}).
		Select("id", "name", "sort", "parent_id").
		Where(
			"EXISTS (?)",
			b.db.Model(model.BusinessTypeRelation{}).
				Select("1").
				Where("EXISTS (?) and child_id = business_types.parent_id and parent_id = ?",
					b.db.Model(model.UserBusinessType{}).
						Select("1").
						Where("user_id = ? and user_business_types.business_type_id = business_type_relations.child_id", param.UID),
					param.PID,
				),
		).
		Preload("Devices", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "business_type_id", "name", "type", "status")
		}).
		Order("sort,id asc").
		Find(&list).
		Error
	return list, err
}
