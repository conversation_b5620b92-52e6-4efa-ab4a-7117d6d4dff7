package repository

import (
	"tw_platform/pkg/model"
	"tw_platform/pkg/system/config"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type AlarmGroupRepo struct {
	db *gorm.DB
	l  *zap.Logger
}

func NewAlarmGroupRepo(db *gorm.DB, config *config.Config, log *zap.Logger) *AlarmGroupRepo {
	return &AlarmGroupRepo{
		db: db,
		l:  log.Named("alarm_groups_repository:"),
	}
}
func (d *AlarmGroupRepo) Create(alarm_group model.AlarmGroup, devices []int, notification_groups []int) error {
	return d.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(model.AlarmGroup{}).Create(&alarm_group).Error
		if err != nil {
			return err
		}
		for _, device_id := range devices {
			alarm_device := model.AlarmGroupDevice{
				AlarmGroupId: alarm_group.ID,
				DeviceId:     device_id,
			}
			err = tx.Model(model.AlarmGroupDevice{}).Create(&alarm_device).Error
			if err != nil {
				return err
			}
		}
		for _, notification_id := range notification_groups {
			alarm_notification := model.AlarmGroupNotification{
				AlarmGroupId:        alarm_group.ID,
				NotificationGroupId: notification_id,
			}
			err = tx.Model(model.AlarmGroupNotification{}).Create(&alarm_notification).Error
			if err != nil {
				return err
			}
		}
		return err
	})
}
func (d *AlarmGroupRepo) List() ([]model.AlarmGroupDetail, error) {
	var alarmGroups []model.AlarmGroupDetail
	err := d.db.Model(model.AlarmGroup{}).
		Preload("Devices", func(tx *gorm.DB) *gorm.DB {
			return tx.Select(
				"devices.id",
				"devices.name",
				"alarm_group_devices.device_id",
				"alarm_group_devices.alarm_group_id",
			).
				Joins("inner join devices on devices.id = alarm_group_devices.device_id")
		}).
		Preload("Notifications", func(tx *gorm.DB) *gorm.DB {
			return tx.Select(
				"alarm_notification_groups.alarm_group_id",
				"alarm_notification_groups.notification_group_id",
				"notification_groups.id",
				"notification_groups.name",
			).
				Joins("inner join notification_groups on notification_groups.id = alarm_notification_groups.notification_group_id")
		}).
		Find(&alarmGroups).
		Error

	return alarmGroups, err
}
func (d *AlarmGroupRepo) Delete(id int) error {
	return d.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Where("alarm_group_id = ?", id).Delete(&model.AlarmGroupDevice{}).Error
		if err != nil {
			return err
		}
		err = tx.Where("alarm_group_id = ?", id).Delete(&model.AlarmGroupNotification{}).Error
		if err != nil {
			return err
		}
		err = tx.Where("id = ?", id).Delete(&model.AlarmGroup{}).Error
		if err != nil {
			return err
		}

		return nil
	})
}
func (d *AlarmGroupRepo) Update(group model.AlarmGroup, devices []int, notification_groups []int) error {
	return d.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(&model.AlarmGroup{}).
			Where("id = ?", group.ID).
			Updates(map[string]interface{}{
				"name":         group.Name,
				"enabled":      group.Enabled,
				"description":  group.Description,
				"alarm_config": group.Config,
			}).Error
		if err != nil {
			return err
		}

		err = tx.Where("alarm_group_id = ?", group.ID).Delete(&model.AlarmGroupDevice{}).Error
		if err != nil {
			return err
		}
		for _, device_id := range devices {
			alarm_device := model.AlarmGroupDevice{
				AlarmGroupId: group.ID,
				DeviceId:     device_id,
			}
			err = tx.Model(&model.AlarmGroupDevice{}).Create(&alarm_device).Error
			if err != nil {
				return err
			}
		}
		err = tx.Where("alarm_group_id = ?", group.ID).Delete(&model.AlarmGroupNotification{}).Error
		if err != nil {
			return err
		}

		for _, notification_id := range notification_groups {
			alarm_notification := model.AlarmGroupNotification{
				AlarmGroupId:        group.ID,
				NotificationGroupId: notification_id,
			}
			err = tx.Model(&model.AlarmGroupNotification{}).Create(&alarm_notification).Error
			if err != nil {
				return err
			}
		}

		return nil
	})
}
