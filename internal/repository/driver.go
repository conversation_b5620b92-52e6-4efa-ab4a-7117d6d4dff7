package repository

import (
	"tw_platform/pkg/model"

	"gorm.io/gorm"
)

type DriverRepo struct {
	db *gorm.DB
}

func NewDriverRepo(db *gorm.DB) *DriverRepo {
	return &DriverRepo{
		db: db,
	}
}

func (d *DriverRepo) Create(driver model.Driver) error {
	return d.db.Create(&driver).Error
}

func (d *DriverRepo) List(page, size int, order int8, filter model.Driver) ([]model.Driver, int64, error) {
	var (
		list  []model.Driver
		total int64
	)
	builder := d.db.Model(model.Driver{}).
		Where(filter).
		Count(&total).
		Limit(size).
		Offset((page - 1) * size)
	if order == -1 {
		builder = builder.Order("id desc")
	}
	err := builder.Select(
		"id",
		"name",
		"agree",
		"use",
		"manu_facturer",
		"dev_type",
		"model",
		"created_at",
		"updated_at",
	).
		Find(&list).Error

	return list, total, err
}

func (d *DriverRepo) GetByID(id int) (model.Driver, error) {
	var driver = model.Driver{
		ID: id,
	}
	err := d.db.First(&driver).Error
	return driver, err
}

func (d *DriverRepo) DeleteByID(id int) error {
	return d.db.Delete(&model.Driver{ID: id}).Error
}

func (d *DriverRepo) Group(field string) ([]string, error) {
	var result []string
	err := d.db.Model(model.Driver{}).
		Select(field).
		Group(field).
		Pluck(field, &result).
		Error
	return result, err
}
