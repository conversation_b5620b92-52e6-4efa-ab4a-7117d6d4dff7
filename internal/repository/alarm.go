package repository

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/utils"

	jsoniter "github.com/json-iterator/go"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type AlarmRepo struct {
	db *gorm.DB
	r  *redis.Client
	l  *zap.Logger

	deviceRepo *DeviceRepo

	device_alarm_counts string
	device_alarm_set    string
	alarm_list_key      string
}

func NewAlarmRepo(
	db *gorm.DB,
	r *redis.Client,
	l *zap.Logger,

	deviceRepo *DeviceRepo,
) *AlarmRepo {
	return &AlarmRepo{
		db:                  db,
		r:                   r,
		l:                   l.Named("alarm_repository"),
		deviceRepo:          deviceRepo,
		device_alarm_counts: "device_alarm_counts:%d:%d",
		device_alarm_set:    "device_alarm_set:%d",
		alarm_list_key:      "alarm_list",
	}
}

func (ar *AlarmRepo) AlarmIncrease(deviceID, unitID int) int64 {
	ctx, cancel := utils.RedisCtx()
	defer cancel()
	value := ar.r.Incr(
		ctx,
		fmt.Sprintf(ar.device_alarm_counts, deviceID, unitID),
	).
		Val()
	ar.r.Expire(
		ctx,
		fmt.Sprintf(ar.device_alarm_counts, deviceID, unitID),
		10*time.Minute,
	)
	return value
}

func (ar *AlarmRepo) IsAlarmed(deviceID, unitID int, isSecond bool) bool {
	var (
		ctx, cancel = utils.RedisCtx()
		identifier  string
	)
	defer cancel()
	if isSecond {
		identifier = fmt.Sprintf("%d_", unitID)
	} else {
		identifier = strconv.Itoa(unitID)
	}

	return ar.r.SIsMember(
		ctx,
		fmt.Sprintf(ar.device_alarm_set, deviceID),
		identifier,
	).
		Val()

}

func (ar *AlarmRepo) SetAlarmedHash(deviceID, unitID int, isSecond bool) {
	var (
		ctx, cancel = utils.RedisCtx()
		identifier  = strconv.Itoa(unitID)
	)
	defer cancel()

	if isSecond {
		ar.r.SAdd(
			ctx,
			fmt.Sprintf(ar.device_alarm_set, deviceID),
			strconv.Itoa(unitID),
		)
		identifier = fmt.Sprintf("%d_", unitID)
	}
	ar.r.SAdd(
		ctx,
		fmt.Sprintf(ar.device_alarm_set, deviceID),
		identifier,
	)
}

func (ar *AlarmRepo) RemoveAlarmed(deviceID, unitID int) int64 {
	ctx, cancel := utils.RedisCtx()
	defer cancel()
	return ar.r.SRem(
		ctx,
		fmt.Sprintf(ar.device_alarm_set, deviceID),
		strconv.Itoa(unitID),
		fmt.Sprintf("%d_", unitID),
	).
		Val()
}

func (ar *AlarmRepo) RemoveAlarmCounts(deviceID, unitID int) {
	ctx, cancel := utils.RedisCtx()
	defer cancel()
	ar.r.Del(
		ctx,
		fmt.Sprintf(ar.device_alarm_counts, deviceID, unitID),
	)
}

func (ar *AlarmRepo) DeviceAlarmCounts(deviceID int) int64 {
	ctx, cancel := utils.RedisCtx()
	defer cancel()
	return ar.r.SCard(
		ctx,
		fmt.Sprintf(ar.device_alarm_set, deviceID),
	).
		Val()
}

func (ar *AlarmRepo) Create(alarm model.Alarm, path string) error {
	var (
		historyAlarm = model.HistoryAlarm{
			Alarm: alarm,
		}
	)
	return ar.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(alarm).Create(&alarm).Error
		if err != nil {
			if errors.Is(err, gorm.ErrDuplicatedKey) {
				return nil
			}
			return err
		}

		historyAlarm.AlarmID = alarm.ID
		err = tx.Model(historyAlarm).
			Create(&historyAlarm).
			Error
		if err != nil {
			return err
		}

		timeLine := model.AlarmTimeLine{
			AlarmID: historyAlarm.ID,
			Status:  model.AlarmStatusHappened,
		}
		err = tx.Model(timeLine).
			Create(&timeLine).
			Error
		if err != nil {
			return err
		}

		switch alarm.Type {
		case model.AlarmTypeCommon:
			err = ar.deviceAlarmHappen(tx, *alarm.DeviceID, *alarm.UnitID, path)
		case model.AlarmTypeGateway:
			err = ar.gatewayAlarmHappen(tx, *alarm.GatewayID)
		case model.AlarmTypeAsset:
		default:
			ar.l.Error("alarm type not support", zap.Uint8("type", alarm.Type))
		}

		return err
	})
}

func (ar *AlarmRepo) Recovery(filter model.Alarm) error {
	var (
		alarm   model.Alarm
		hisotry model.HistoryAlarm
	)

	err := ar.db.Model(hisotry).
		Where(filter).
		Last(&hisotry).
		Error
	if err != nil {
		return err
	}

	return ar.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(alarm).
			Where("id = ?", hisotry.AlarmID).
			Delete(&alarm).
			Error
		if err != nil {
			return err
		}
		err = tx.Model(hisotry).
			Where("id = ?", hisotry.ID).
			Update("status", model.AlarmStatusRecovered).
			Error
		if err != nil {
			return err
		}
		timeLine := model.AlarmTimeLine{
			AlarmID: hisotry.ID,
			Status:  model.AlarmStatusRecovered,
		}
		return tx.Model(timeLine).
			Create(&timeLine).
			Error
	})
}
func (ar *AlarmRepo) Recovery1(filter model.Alarm, path string) error {
	var (
		alarm   model.Alarm
		history model.HistoryAlarm
	)

	err := ar.db.Model(history).
		Where(filter).
		Last(&history).
		Error
	if err != nil {
		return err
	}

	return ar.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(alarm).
			Where("id = ?", history.AlarmID).
			Delete(&alarm).
			Error
		if err != nil {
			return err
		}
		err = tx.Model(history).
			Where("id = ?", history.ID).
			Update("status", model.AlarmStatusRecovered).
			Error
		if err != nil {
			return err
		}
		timeLine := model.AlarmTimeLine{
			AlarmID: history.ID,
			Status:  model.AlarmStatusRecovered,
		}
		err = tx.Model(timeLine).
			Create(&timeLine).
			Error
		if err != nil {
			return err
		}
		switch history.Type {
		case model.AlarmTypeCommon:
			err = ar.deviceRecovery(tx, *history.DeviceID, *history.UnitID, path)
		case model.AlarmTypeGateway:
			err = ar.gatewayRecovery(tx, *history.GatewayID)
		case model.AlarmTypeAsset:
		}
		return err
	})
}

func (ar *AlarmRepo) deviceRecovery(tx *gorm.DB, deviceID, unitID int, path string) error {
	err := tx.Model(model.Device{}).
		Where("id = ?", deviceID).
		Update("status", model.DeviceStatusNormal).
		Error
	if err != nil {
		return err
	}

	return tx.Model(model.Config{}).
		Where("id = ?", deviceID).
		Update(
			"config",
			datatypes.JSONSet("config").
				Set(fmt.Sprintf(
					"{%s, unit, %d, alarmstatus}",
					path,
					unitID,
				),
					model.DeviceStatusNormal,
				),
		).
		Error
}
func (ar *AlarmRepo) gatewayRecovery(tx *gorm.DB, id int) error {
	err := tx.Model(model.Gateway{}).Debug().
		Where("id = ?", id).
		Update("status", model.GatewayStatusOnline).
		Error
	if err != nil {
		return err
	}

	return tx.Model(model.Device{}).Debug().
		Where(
			"exists (?)",
			tx.Model(model.GatewayPort{}).
				Select("1").
				Where("gateway_id = ? and devices.port_id = id", id),
		).
		Update("status", model.DeviceStatusNormal).
		Error
}

func (ar *AlarmRepo) GatewayRecovery(gatewayID int) error {
	return ar.db.Model(model.Gateway{}).
		Where("id = ?", gatewayID).
		Update("status", model.GatewayStatusOnline).
		Error
}

func (ar *AlarmRepo) deviceAlarmHappen(tx *gorm.DB, deviceID, unitID int, path string) error {
	err := tx.Model(model.Device{}).
		Where("id = ?", deviceID).
		Update("status", model.DeviceStatusAlarmed).
		Error
	if err != nil {
		return err
	}

	return tx.Model(model.Config{}).
		Where("id = ?", deviceID).
		Update(
			"config",
			datatypes.JSONSet("config").
				Set(
					fmt.Sprintf(
						"{%s, unit, %d, alarmstatus}",
						path,
						unitID,
					),
					model.DeviceStatusAlarmed,
				),
		).
		Error
}

func (ar *AlarmRepo) gatewayAlarmHappen(tx *gorm.DB, gatewayID int) error {
	err := tx.Model(model.Gateway{}).
		Where("id = ?", gatewayID).
		Update("status", model.GatewayStatusOffline).
		Error
	if err != nil {
		return err
	}

	return tx.Model(model.Device{}).
		Where(
			"exists (?)",
			tx.Model(model.GatewayPort{}).
				Select("1").
				Where("gateway_id = ? and devices.port_id = id", gatewayID),
		).
		Update("status", model.DeviceStatusGatewayOffline).
		Error
}

func (ar *AlarmRepo) filter(userID int, name string, areaID int) func(*gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		var (
			dev = ar.db.Model(model.UserArea{}).
				Select("1").
				Where("user_id = ? and area_id = devices.area_id", userID)

			gateway = ar.db.Model(model.UserArea{}).
				Select("1").
				Where("user_id = ? and area_id = ag.area_id", userID)
		)

		if areaID != 0 {
			dev = dev.Where(
				"exists (?)",
				ar.db.Model(model.AreaRelation{}).
					Select("1").
					Where(fmt.Sprintf(
						"parent_id = ? and %s.area_id = child_id",
						model.Device{}.TableName(),
					),
						areaID,
					),
			)
			gateway = gateway.
				Where(
					"exists (?)",
					ar.db.Model(model.AreaRelation{}).
						Select("1").
						Where(
							"parent_id = ? and ag.area_id = child_id",
							areaID,
						),
				)
		}

		return db.Table(name).Where(
			"exists (?)",
			ar.db.Raw(
				"? union ?",
				ar.db.Model(model.Device{}).
					Select("1").
					Where(
						fmt.Sprintf("EXISTS (?) and %s.device_id = devices.id", name),
						dev,
					),
				ar.db.Table(fmt.Sprintf("%s ag", model.AreaGateway{}.TableName())).
					Select("1").
					Where(
						fmt.Sprintf("EXISTS (?) and ag.gateway_id = %s.gateway_id", name),
						gateway,
					),
			),
		)
	}
}

func (ar *AlarmRepo) filterByDepartmentID(departmentID int, table string) func(*gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where(
			"exists (?)",
			ar.db.Raw(
				"? union ?",
				ar.db.Model(model.DepartmentDevice{}).
					Select("1").
					Where(
						fmt.Sprintf("exists (?) and %s.device_id = department_devices.device_id", table),
						ar.db.Model(model.DepartmentRelation{}).
							Select("1").
							Where("parent_id = ? and child_id = department_devices.department_id", departmentID),
					),
				ar.db.Table(fmt.Sprintf("%s dg", model.DepartmentGateway{}.TableName())).
					Select("1").
					Where(
						fmt.Sprintf("EXISTS (?) and dg.gateway_id = %s.gateway_id", table),
						ar.db.Model(model.DepartmentRelation{}).
							Select("1").
							Where("parent_id = ? and child_id = dg.department_id", departmentID),
					),
			),
		)
	}
}

func (ar *AlarmRepo) HistoryList(param request.HistoryAlarmList) ([]model.HistoryAlarmWithTimeline, int64, error) {
	var (
		list    []model.HistoryAlarmWithTimeline
		total   int64
		builder = ar.db.Model(model.HistoryAlarm{}).Debug()
	)

	if param.Begin != "" {
		builder = builder.Where("history_alarms.created_at >= ?", param.Begin)
	}

	if param.End != "" {
		builder = builder.Where("history_alarms.created_at <= ?", param.End)
	}
	if param.BusinessTypeID != 0 {
		builder = builder.Where("d.business_type_id = ?", param.BusinessTypeID).
			Joins("left join devices d on d.id = history_alarms.device_id")
	}

	if param.DepartmentID != 0 {
		builder = builder.
			Scopes(ar.filterByDepartmentID(param.DepartmentID, model.HistoryAlarm{}.TableName()))
	}

	builder = builder.
		Scopes(ar.filter(param.UID, model.HistoryAlarm{}.TableName(), param.AreaID))

	if param.Type != 0 {
		builder = builder.Where("history_alarms.type = ?", param.Type)
	}

	if param.Status != 0 {
		builder = builder.Where("history_alarms.status = ?", param.Status)
	}

	if param.Level != "" {
		builder = builder.Where("history_alarms.level = ?", param.Level)
	}

	err := builder.
		Count(&total).
		Select(
			"history_alarms.id",
			"history_alarms.level",
			"history_alarms.message",
			"history_alarms.status",
			"history_alarms.type",
			"history_alarms.alarm_id",
			"history_alarms.device_name",
			"history_alarms.unit_name",
			"history_alarms.gateway_name",
			"history_alarms.value",
			"history_alarms.flag",
			"history_alarms.area_id",
			"history_alarms.area_name",
			"history_alarms.created_at",
			"history_alarms.updated_at",
			"hat.ticket_id",
		).
		Joins("left join history_alarm_tickets hat on history_alarms.id = hat.history_alarm_id").
		Limit(param.Size).
		Offset((param.Page-1)*param.Size).
		Order("id desc").
		Preload("Timelines", func(tx *gorm.DB) *gorm.DB {
			return tx.Order("id asc")
		}).
		Find(&list).
		Error

	return list, total, err
}
func (ar *AlarmRepo) ExportHistoryList(param request.ExportHistoryAlarm) ([]model.HistoryAlarmWithTimeline, error) {
	var (
		list    []model.HistoryAlarmWithTimeline
		builder = ar.db.Model(model.HistoryAlarm{})
		now     = time.Now()
	)

	switch param.DateType {
	case request.LastDay:
		builder = builder.Where("history_alarms.created_at >= ?", now.AddDate(0, 0, -1))
	case request.LastWeek:
		builder = builder.Where("history_alarms.created_at >= ?", now.AddDate(0, 0, -7))
	case request.LastMonth:
		builder = builder.Where("history_alarms.created_at >= ?", now.AddDate(0, -1, 0))
	case request.LastQuarter:
		builder = builder.Where("history_alarms.created_at >= ?", now.AddDate(0, -3, 0))
	case request.LastHalfYear:
		builder = builder.Where("history_alarms.created_at >= ?", now.AddDate(0, -6, 0))
	case request.LastYear:
		builder = builder.Where("history_alarms.created_at >= ?", now.AddDate(-1, 0, 0))
	default:
		if param.Begin != "" {
			builder = builder.Where("history_alarms.created_at >= ?", param.Begin)
		}

		if param.End != "" {
			builder = builder.Where("history_alarms.created_at <= ?", param.End)
		}
	}

	if param.BusinessTypeID != 0 {
		builder = builder.Where("d.business_type_id = ?", param.BusinessTypeID).
			Joins("left join devices d on d.id = history_alarms.device_id")
	}

	if param.DepartmentID != 0 {
		builder = builder.
			Scopes(ar.filterByDepartmentID(param.DepartmentID, model.HistoryAlarm{}.TableName()))
	}

	builder = builder.
		Scopes(ar.filter(param.UID, model.HistoryAlarm{}.TableName(), param.AreaID))

	if param.Type != 0 {
		builder = builder.Where("history_alarms.type = ?", param.Type)
	}

	if param.Status != 0 {
		builder = builder.Where("history_alarms.status = ?", param.Status)
	}

	if param.Level != "" {
		builder = builder.Where("history_alarms.level = ?", param.Level)
	}

	err := builder.
		Select(
			"history_alarms.id",
			"history_alarms.level",
			"history_alarms.message",
			"history_alarms.status",
			"history_alarms.type",
			"history_alarms.alarm_id",
			"history_alarms.device_name",
			"history_alarms.unit_name",
			"history_alarms.gateway_name",
			"history_alarms.value",
			"history_alarms.flag",
			"history_alarms.area_id",
			"history_alarms.area_name",
			"history_alarms.created_at",
			"history_alarms.updated_at",
			"hat.ticket_id",
		).
		Joins("left join history_alarm_tickets hat on history_alarms.id = hat.history_alarm_id").
		Order("id desc").
		Preload("Timelines", func(tx *gorm.DB) *gorm.DB {
			return tx.Order("id asc")
		}).
		Find(&list).
		Error

	return list, err
}

func (ar *AlarmRepo) RealtimeAlarmList(param request.RealtimeAlarmList) ([]response.RealtimeList, int64, error) {
	var (
		list    []response.RealtimeList
		builder = ar.db.Model(model.Alarm{}).
			Joins("left join devices d on d.id = alarms.device_id")
		total int64
	)

	if param.Begin != "" {
		builder = builder.Where("alarms.created_at >= ?", param.Begin)
	}

	if param.End != "" {
		builder = builder.Where("alarms.created_at <= ?", param.End)
	}

	if param.ID != 0 {
		builder = builder.Where("alarms.id = ?", param.ID)
	}

	if param.BusinessTypeID != 0 {
		builder = builder.Where("d.business_type_id = ?", param.BusinessTypeID)
	}

	if param.DepartmentID != 0 {
		builder = builder.
			Scopes(ar.filterByDepartmentID(param.DepartmentID, model.Alarm{}.TableName()))
	}

	builder = builder.
		Scopes(ar.filter(param.UID, model.Alarm{}.TableName(), param.AreaID))

	if param.Type != 0 {
		builder = builder.Where("alarms.type = ?", param.Type)
	}

	if param.Status != 0 {
		builder = builder.Where("alarms.status = ?", param.Status)
	}
	if param.Level != "" {
		builder = builder.Where("alarms.level = ?", param.Level)
	}

	err := builder.
		Count(&total).
		Select(
			"alarms.id",
			"alarms.device_id",
			"alarms.unit_id",
			"alarms.level",
			"alarms.message",
			"alarms.created_at",
			"alarms.type",
			"alarms.status",
			"alarms.gateway_id",
			"alarms.device_name",
			"alarms.unit_name",
			"alarms.gateway_name",
			"alarms.value",
			"alarms.flag",
			"alarms.area_id",
			"alarms.area_name",
			"bt.id as business_type_id",
			"bt.name as business_type_name",
		).
		Joins("left join business_types bt on bt.id = d.business_type_id").
		Limit(param.Size).
		Offset((param.Page - 1) * param.Size).
		Order("alarms.id desc").
		Find(&list).
		Error

	return list, total, err
}

func (ar *AlarmRepo) RealtimeTotal() (int64, error) {
	var total int64
	err := ar.db.Model(model.Alarm{}).
		Count(&total).
		Error
	return total, err
}

func (ar *AlarmRepo) RealtimeTotalByUser(user request.TokenInfo) (int64, error) {
	var total int64
	builder := ar.db.Model(model.Alarm{})
	if user.RID != model.RoleIDAdmin {
		builder = builder.
			Scopes(ar.filter(user.UID, model.Alarm{}.TableName(), 0))
	}
	err := builder.
		Count(&total).
		Error
	return total, err
}

func (ar *AlarmRepo) UpdateRealtimeAlarm(alarm model.Alarm) error {
	return ar.db.Model(alarm).
		Where("id = ?", alarm.ID).
		Updates(alarm).
		Error
}

func (ar *AlarmRepo) FirstHistoryAlarm(id int) (model.HistoryAlarm, error) {
	var alarm model.HistoryAlarm
	err := ar.db.Model(alarm).
		Where("alarm_id = ?", id).
		First(&alarm).
		Error
	return alarm, err
}

func (ar *AlarmRepo) FirstRealtimeAlarm(filter model.Alarm) (model.Alarm, error) {
	var alarm model.Alarm
	err := ar.db.Model(alarm).
		Where(filter).
		First(&alarm).
		Error
	return alarm, err
}

func (ar *AlarmRepo) SetStatusBlocked(alarmId, historyID int) error {
	var (
		alarm    model.Alarm
		timeLine = model.AlarmTimeLine{
			AlarmID: historyID,
			Status:  model.AlarmStatusBlocked,
		}
	)
	return ar.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(alarm).
			Where("id = ?", alarmId).
			Delete(&alarm).
			Error
		if err != nil {
			return err
		}
		err = tx.Model(model.HistoryAlarm{}).
			Where("id = ?", historyID).
			Update("status", model.AlarmStatusBlocked).
			Error
		if err != nil {
			return err
		}

		return tx.Model(timeLine).
			Create(&timeLine).
			Error
	})
}
func (ar *AlarmRepo) SetStatusHandled(alarmID, historyID int) error {
	return ar.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(model.Alarm{}).
			Where("id = ?", alarmID).
			Update("status", model.AlarmStatusHandled).
			Error
		if err != nil {
			return err
		}
		err = tx.Model(model.HistoryAlarm{}).
			Where("id = ?", historyID).
			Update("status", model.AlarmStatusHandled).
			Error
		if err != nil {
			return err
		}
		tl := model.AlarmTimeLine{
			AlarmID: historyID,
			Status:  model.AlarmStatusHandled,
		}
		return tx.Model(tl).
			Create(&tl).
			Error
	})
}

func (ar *AlarmRepo) TotalOfTime(param request.AlarmTotalOfTime) ([]response.AlarmTotalOfTime, error) {
	var (
		results  []response.AlarmTotalOfTime
		begin    = time.Now()
		timeUnit string
	)

	switch param.Type {
	case request.AlarmTotalOfDay:
		results = make([]response.AlarmTotalOfTime, 0, 30)
		begin = begin.Add(-30 * 24 * time.Hour)
		timeUnit = "day"
	case request.AlarmTotalOfHour:
		results = make([]response.AlarmTotalOfTime, 0, 24)
		begin = begin.Add(-24 * time.Hour)
		timeUnit = "hour"
	case request.AlarmTotalOfMinute:
		results = make([]response.AlarmTotalOfTime, 0, 10)
		begin = begin.Add(-10 * time.Minute)
		timeUnit = "minute"
	}

	err := ar.db.Model(model.HistoryAlarm{}).Debug().
		Select(
			"count(1) as total",
			fmt.Sprintf("date_trunc('%s', created_at) as time", timeUnit),
		).
		Scopes(ar.filter(param.UID, model.HistoryAlarm{}.TableName(), param.AreaID)).
		Where("created_at >= ?", begin).
		Group(fmt.Sprintf("date_trunc('%s', created_at)", timeUnit)).
		Find(&results).
		Error
	return results, err
}

func (ar *AlarmRepo) RealtimeAlarmPush(param request.SystemStatistic) ([]response.RealtimeList, error) {
	var (
		list    []response.RealtimeList
		builder = ar.db.Model(model.Alarm{}).
			Joins("left join devices d on d.id = alarms.device_id").
			Where(fmt.Sprintf("%s.id > ?", model.Alarm{}.TableName()), param.AlarmID)
	)

	if param.Begin != "" {
		builder = builder.Where("alarms.created_at >= ?", param.Begin)
	}

	if param.End != "" {
		builder = builder.Where("alarms.created_at <= ?", param.End)
	}

	if param.BusinessTypeID != 0 {
		builder = builder.Where("d.business_type_id = ?", param.BusinessTypeID)
	}

	if param.DepartmentID != 0 {
		builder = builder.
			Scopes(ar.filterByDepartmentID(param.DepartmentID, model.Alarm{}.TableName()))
	}

	builder = builder.
		Scopes(ar.filter(param.UID, model.Alarm{}.TableName(), param.AreaID))

	if param.Type != 0 {
		builder = builder.Where("alarms.type = ?", param.Type)
	}

	if param.Status != 0 {
		builder = builder.Where("alarms.status = ?", param.Status)
	}
	if param.Level != "" {
		builder = builder.Where("alarms.level = ?", param.Level)
	}

	err := builder.
		Select(
			"alarms.id",
			"alarms.device_id",
			"alarms.unit_id",
			"alarms.level",
			"alarms.message",
			"alarms.created_at",
			"alarms.type",
			"alarms.status",
			"alarms.gateway_id",
			"alarms.device_name",
			"alarms.unit_name",
			"alarms.gateway_name",
			"alarms.value",
			"alarms.flag",
			"alarms.area_id",
			"alarms.area_name",
			"bt.id as business_type_id",
			"bt.name as business_type_name",
		).
		Joins("left join business_types bt on bt.id = d.business_type_id").
		Order("alarms.id desc").
		Find(&list).
		Error

	return list, err
}

func (ar *AlarmRepo) RealtimeListWS(param request.RealtimeAlarmList) ([]response.RealtimeList, error) {
	var (
		list    []response.RealtimeList
		builder = ar.db.Model(model.Alarm{}).Debug().
			Joins("left join devices d on d.id = alarms.device_id")
	)

	if param.Begin != "" {
		builder = builder.Where("alarms.created_at >= ?", param.Begin)
	}

	if param.End != "" {
		builder = builder.Where("alarms.created_at <= ?", param.End)
	}

	if param.BusinessTypeID != 0 {
		builder = builder.Where("d.business_type_id = ?", param.BusinessTypeID)
	}

	if param.DepartmentID != 0 {
		builder = builder.
			Scopes(ar.filterByDepartmentID(param.DepartmentID, model.Alarm{}.TableName()))
	}

	builder = builder.
		Scopes(ar.filter(param.UID, model.Alarm{}.TableName(), param.AreaID))

	if param.Type != 0 {
		builder = builder.Where("alarms.type = ?", param.Type)
	}

	if param.Status != 0 {
		builder = builder.Where("alarms.status = ?", param.Status)
	}
	if param.Level != "" {
		builder = builder.Where("alarms.level = ?", param.Level)
	}

	err := builder.
		Select(
			"alarms.id",
			"alarms.device_id",
			"alarms.unit_id",
			"alarms.level",
			"alarms.message",
			"alarms.created_at",
			"alarms.type",
			"alarms.status",
			"alarms.gateway_id",
			"alarms.device_name",
			"alarms.unit_name",
			"alarms.gateway_name",
			"alarms.value",
			"alarms.flag",
			"alarms.area_id",
			"alarms.area_name",
			"bt.id as business_type_id",
			"bt.name as business_type_name",
		).
		Joins("left join business_types bt on bt.id = d.business_type_id").
		Order("alarms.id desc").
		Find(&list).
		Error

	return list, err
}

func (ar *AlarmRepo) FilterByDate(begin, end string) ([]model.HistoryAlarmExport, error) {
	var (
		list []model.HistoryAlarmExport
	)
	err := ar.db.Model(model.HistoryAlarm{}).
		Where("created_at >= ? and created_at <= ?", begin, end).
		Preload("Timelines", func(tx *gorm.DB) *gorm.DB {
			return tx.Order("id asc")
		}).
		Find(&list).
		Error
	return list, err
}

func (ar *AlarmRepo) ExportCreate(alarm model.ExportAlarm) error {
	return ar.db.Model(alarm).Create(&alarm).Error
}

func (ar *AlarmRepo) ExportList(param request.PageInfo) ([]model.ExportAlarm, int64, error) {
	var (
		list  []model.ExportAlarm
		total int64
	)

	err := ar.db.Model(model.ExportAlarm{}).
		Count(&total).
		Limit(param.Size).
		Offset((param.Page - 1) * param.Size).
		Order("id desc").
		Find(&list).
		Error
	return list, total, err
}

func (ar *AlarmRepo) Publish(param response.AlarmListPayload) error {
	var (
		ctx, cancel = utils.RedisCtx()
	)
	defer cancel()

	data, err := json.Marshal(param)
	if err != nil {
		return err
	}

	return ar.r.RPush(ctx, ar.alarm_list_key, data).Err()
}

func (ar *AlarmRepo) Subscribe() (response.AlarmListPayload, error) {
	var (
		ctx, cancel = utils.RedisCtx()
		result      response.AlarmListPayload
	)
	defer cancel()
	results, err := ar.r.BLPop(ctx, 0, ar.alarm_list_key).Result()
	if err != nil {
		return result, err
	}
	err = jsoniter.UnmarshalFromString(results[1], &result)
	return result, err
}

func (ar *AlarmRepo) RecoveryAsset(alarmID int) error {
	var (
		ha model.HistoryAlarm
	)
	err := ar.db.Model(ha).
		Select("id").
		Where("alarm_id = ?", alarmID).
		First(&ha).
		Error
	if err != nil {
		return err
	}
	return ar.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(model.Alarm{}).
			Where("id = ?", alarmID).
			Delete(&model.Alarm{}).
			Error
		if err != nil {
			return err
		}

		err = tx.Model(model.HistoryAlarm{}).
			Where("id = ?", ha.ID).
			Update("status", model.AlarmStatusRecovered).
			Error
		if err != nil {
			return err
		}
		tl := model.AlarmTimeLine{
			AlarmID: ha.ID,
			Status:  model.AlarmStatusRecovered,
		}
		return tx.Model(tl).
			Create(&tl).
			Error
	})
}
