package repository

import (
	"fmt"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/system/config"

	"tw_platform/pkg/model"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type AreaRepo struct {
	db     *gorm.DB
	config *config.Config
}

func NewAreaRepo(db *gorm.DB, config *config.Config) *AreaRepo {
	return &AreaRepo{
		db:     db,
		config: config,
	}
}

func (a *AreaRepo) Create(area model.Area, relations []model.AreaRelation) error {
	return a.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Create(&area).Error
		if err != nil {
			return err
		}

		for i := 0; i < len(relations); i++ {
			relations[i].ChildID = area.ID
			relations[i].Depth += 1
		}

		relations = append(relations, model.AreaRelation{
			ParentID: area.ID,
			ChildID:  area.ID,
		})

		return tx.Model(model.AreaRelation{}).Create(&relations).Error
	})
}

func (a *AreaRepo) RelationListByChildID(childID int) ([]model.AreaRelation, error) {
	var relations []model.AreaRelation

	err := a.db.Model(model.AreaRelation{}).
		Where("child_id = ?", childID).
		Find(&relations).
		Error

	return relations, err
}

func (a *AreaRepo) All() ([]model.Area, error) {
	var (
		list []model.Area
	)

	err := a.db.Model(model.Area{}).
		Find(&list).
		Error

	return list, err
}

func (a *AreaRepo) FindByUserIDByScene(userID, btID int) ([]model.Area, error) {
	var (
		list []model.Area
	)
	builder := a.db.Model(model.Area{}).Debug().
		Select(
			"id",
			"name",
			"CASE WHEN user_areas.area_id iS NULL THEN -1 ELSE areas.parent_id END AS parent_id",
		).
		Joins("LEFT JOIN user_areas ON areas.parent_id = user_areas.area_id AND user_areas.user_id = ?", userID).
		Where(
			"EXISTS (?)",
			a.db.Model(model.UserArea{}).
				Select("1").
				Where("user_id = ? and areas.id = user_areas.area_id", userID),
		)
	if btID != 0 {
		builder = builder.Where(
			"EXISTS (?)",
			a.db.Model(model.AreaRelation{}).
				Select("1").
				Where(
					"EXISTS (?) and parent_id = areas.id",
					a.db.Model(model.AreaScene{}).
						Select("1").
						Where("business_type_id = ? and type = ? and area_scenes.area_id = area_relations.child_id", btID, model.SceneTypeScreen),
				),
		)
	}
	err := builder.
		Order("id asc").
		Find(&list).
		Error

	return list, err
}
func (a *AreaRepo) FindByUserID(userID, btID int) ([]model.Area, error) {
	var (
		list []model.Area
	)
	builder := a.db.Model(model.Area{}).
		Select(
			"id",
			"name",
			"CASE WHEN user_areas.area_id iS NULL THEN -1 ELSE areas.parent_id END AS parent_id",
		).
		Joins("LEFT JOIN user_areas ON areas.parent_id = user_areas.area_id AND user_areas.user_id = ?", userID).
		Where(
			"EXISTS (?)",
			a.db.Model(model.UserArea{}).
				Select("1").
				Where("user_id = ? and areas.id = user_areas.area_id", userID),
		).
		Where(
			"EXISTS (?)",
			a.db.Model(model.AreaRelation{}).
				Select("1").
				Where(
					"EXISTS (?) and parent_id = areas.id",
					a.db.Model(model.AreaTypeCount{}).
						Select("1").
						// Where("business_type_id = ? and area_id = area_relations.child_id", btID),
						Where("EXISTS (?) and area_id = area_relations.child_id",
							a.db.Model(model.BusinessTypeRelation{}).
								Select("1").
								Where("parent_id = ? and child_id = area_type_counts.business_type_id", btID),
						),
				),
		)
	err := builder.
		Order("id asc").
		Find(&list).
		Error

	return list, err
}

func (a *AreaRepo) FindByUserIDWithTypeCounts(userID, businessTypeID int) ([]model.AreaWithTypes, error) {
	var (
		list   []model.AreaWithTypes
		sqlStr = `(SELECT true FROM devices d WHERE atc.area_id = d.area_id AND EXISTS (
        SELECT 1 FROM business_type_relations WHERE parent_id = atc.business_type_id AND child_id = d.business_type_id) and d.status!=%d LIMIT 1) as alarmed`
	)

	err := a.db.Model(model.Area{}).Debug().
		Select(
			"id",
			"name",
			"longitude",
			"latitude",
			"CASE WHEN user_areas.area_id iS NULL THEN -1 ELSE areas.parent_id END AS parent_id",
		).
		Joins("LEFT JOIN user_areas ON areas.parent_id = user_areas.area_id AND user_areas.user_id = ?", userID).
		Where(
			"EXISTS (?)",
			a.db.Model(model.UserArea{}).
				Select("1").
				Where("user_id = ? and areas.id = user_areas.area_id", userID),
		).
		Preload("Types", func(db *gorm.DB) *gorm.DB {
			return db.Table("area_type_counts atc").Debug().
				Select(
					"atc.area_id",
					"atc.business_type_id",
					"atc.count",
					"bt.name",
					"bt.id",
					fmt.Sprintf(sqlStr, model.DeviceStatusNormal),
				).
				Where(
					"EXISTS (?)",
					a.db.Model(model.UserBusinessType{}).
						Select("1").
						Where("user_id = ? and user_business_types.business_type_id = atc.business_type_id", userID).
						Where(
							"EXISTS (?)",
							a.db.Model(model.BusinessTypeRelation{}).
								Select("1").
								// Where("parent_id = ? and depth = 1 and child_id = user_business_types.business_type_id", businessTypeID),
								Where("parent_id = ? and child_id = user_business_types.business_type_id", businessTypeID),
						),
				).
				Joins("inner join business_types bt on atc.business_type_id = bt.id")
		}).
		Order("id asc").
		Find(&list).
		Error
	return list, err
}

func (a *AreaRepo) AllWithTypeCounts() ([]model.AreaWithTypes, error) {
	var (
		list []model.AreaWithTypes
	)

	err := a.db.Model(model.Area{}).Debug().
		Select(
			"id",
			"name",
			"parent_id",
			"longitude",
			"latitude",
		).
		Preload("Types", func(db *gorm.DB) *gorm.DB {
			return db.Table("area_type_counts atc").
				Select(
					"atc.area_id",
					"atc.business_type_id",
					"atc.count",
					"bt.name",
					"bt.id",
				).
				Joins("inner join business_types bt on atc.business_type_id = bt.id")
		}).
		Order("id asc").
		Find(&list).
		Error

	return list, err
}

func (a *AreaRepo) AllWithDevices() ([]model.AreaWithDevices, error) {
	var (
		list []model.AreaWithDevices
	)

	err := a.db.Preload(
		"Devices",
		func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "area_id", "name", "type")
		}).
		Order("id asc").
		Find(&list).
		Error

	return list, err
}

func (a *AreaRepo) ListWithDeviceConfig(uid, btID int, devType string) ([]model.AreaWithDeviceConfigs, error) {
	var (
		list []model.AreaWithDeviceConfigs
	)
	err := a.db.Model(model.Area{}).
		Select("id", "name", "parent_id", "status").
		Where(
			"exists (?)",
			a.db.Model(model.UserArea{}).
				Select("1").
				Where("user_id = ? and area_id = areas.id", uid),
		).
		Preload("Devices", func(db *gorm.DB) *gorm.DB {
			builder := db.Select("id", "area_id", "name", "type", "activity").
				Where("business_type_id = ?", btID)
			if devType != "" {
				builder = builder.Where("type = ?", devType)
			}
			return builder
		}).
		Preload("Devices.Config", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "type", "config")
		}).
		Order("id asc").
		Find(&list).
		Error
	return list, err
}

func (a *AreaRepo) First(filter model.Area) (model.Area, error) {
	var area model.Area
	err := a.db.Where(filter).First(&area).Error
	return area, err
}

func (a *AreaRepo) Delete(id int) error {
	return a.db.Transaction(func(tx *gorm.DB) error {
		var (
			area  model.Area
			scene model.AreaScene
		)
		err := tx.Model(area).
			Where("id = ?", id).
			Delete(&area).
			Error
		if err != nil {
			return err
		}

		return tx.Model(scene).
			Where("area_id = ?", id).
			Delete(&scene).
			Error
	})
}

func (a *AreaRepo) CreateScene(scene model.AreaScene) (int, error) {
	err := a.db.Model(scene).
		Create(&scene).
		Error
	return scene.ID, err
}

func (a *AreaRepo) CreateSceneBatch(scenes []model.AreaScene) error {
	return a.db.Model(model.AreaScene{}).Create(scenes).Error
}

func (a *AreaRepo) LastScene(filter model.AreaScene) (model.AreaScene, error) {
	var scene model.AreaScene
	err := a.db.Where(filter).
		Last(&scene).
		Error
	return scene, err
}

func (a *AreaRepo) LastSceneRecursive(filter model.AreaScene) (model.AreaScene, error) {
	var (
		scene     model.AreaScene
		relations = make([]model.AreaRelation, 0)
		ids       = make([]int, 0)
		scenes    = make([]model.AreaScene, 0)
	)

	err := a.db.Model(relations).
		Where("child_id = ?", filter.AreaID).
		Order("depth asc").
		Find(&relations).
		Error
	if err != nil {
		return scene, err
	}

	if len(relations) <= 1 {
		return scene, nil
	}

	for i := 1; i < len(relations); i++ {
		ids = append(ids, relations[i].ParentID)
	}

	err = a.db.Model(model.AreaScene{}).
		Where(
			"area_id in (?) and type = ? and business_type_id = ?",
			ids,
			filter.Type,
			filter.BusinessTypeID,
		).
		Find(&scenes).
		Error
	if err != nil {
		return scene, err
	}

	if len(scenes) == 0 {
		return scene, nil
	}

	var (
		sceneMap = make(map[int]model.AreaScene)
		ok       bool
	)
	for i := 0; i < len(scenes); i++ {
		sceneMap[scenes[i].AreaID] = scenes[i]
	}

	for i := 0; i < len(relations); i++ {
		scene, ok = sceneMap[relations[i].ParentID]
		if ok {
			break
		}
	}

	return scene, nil
}

func (a *AreaRepo) UpdateScene(id int, scene model.AreaScene) error {
	return a.db.Model(scene).
		Where("id = ?", id).
		Updates(&scene).
		Error
}

func (a *AreaRepo) DeleteScene(id int) error {
	return a.db.Where("id = ?", id).Delete(&model.AreaScene{}).Error
}

func (a *AreaRepo) countIncrease(tx *gorm.DB, areaID, typeId, step int) error {
	var (
		count model.AreaTypeCount
	)

	rows := tx.Model(count).
		Where("area_id = ? AND business_type_id = ?", areaID, typeId).
		Update("count", gorm.Expr("count + ?", step)).
		RowsAffected

	if rows > 0 {
		return nil
	}
	count = model.AreaTypeCount{
		AreaID:         areaID,
		BusinessTypeID: typeId,
		Count:          step,
	}
	return tx.Model(count).
		Create(&count).
		Error
}

func (a *AreaRepo) countReduce(tx *gorm.DB, areaID, typeID, step int) error {
	var (
		count model.AreaTypeCount
	)

	err := tx.Model(&count).Clauses(clause.Returning{
		Columns: []clause.Column{
			{Name: "count"},
		},
	}).
		Where("area_id = ? AND business_type_id = ?", areaID, typeID).
		Update("count", gorm.Expr("count - ?", step)).
		Error

	if err != nil {
		return err
	}

	if count.Count > 0 {
		return nil
	}

	return tx.Model(count).
		Where("area_id = ? AND business_type_id = ?", areaID, typeID).
		Delete(&count).
		Error
}

func (a *AreaRepo) Update(id int, area model.Area) error {
	return a.db.Model(area).
		Where("id = ?", id).
		Updates(&area).
		Error
}

func (a *AreaRepo) ListByPID(pid, bid int) ([]model.Area, error) {
	var (
		list    []model.Area
		builder = a.db.Model(model.Area{}).
			Where("parent_id = ?", pid)
	)
	if bid != 0 {
		builder = builder.Where(
			"exists (?)",
			a.db.Model(model.AreaTypeCount{}).
				Select("1").
				Where(
					"area_id = areas.id and exists (?)",
					a.db.Model(model.BusinessTypeRelation{}).
						Select("1").
						Where("parent_id = ? and child_id = area_type_counts.business_type_id", bid),
				),
		)
	}
	err := builder.
		Find(&list).
		Error
	return list, err
}

func (a *AreaRepo) ListByPIDRecursive(pid, bid int) ([]model.Area, error) {
	var (
		list    []model.Area
		builder = a.db.Model(model.Area{}).
			Where("parent_id = ?", pid)
	)

	if bid != 0 {
		builder = builder.Where(
			"exists (?)",
			a.db.Model(model.AreaRelation{}).
				Select("1").
				Where(
					"parent_id = areas.id and exists (?)",
					a.db.Model(model.AreaTypeCount{}).
						Select("1").
						Where(
							"area_id = area_relations.child_id and exists (?)",
							a.db.Model(model.BusinessTypeRelation{}).
								Select("1").
								Where("parent_id = ? and child_id = area_type_counts.business_type_id", bid),
						),
				),
		)
	}

	err := builder.Find(&list).
		Error
	return list, err

}

func (a *AreaRepo) DefaultSceneType(data model.AreaDefaultSceneType) error {
	return a.db.Transaction(func(tx *gorm.DB) error {
		var filter = model.AreaDefaultSceneType{
			AreaID:         data.AreaID,
			BusinessTypeID: data.BusinessTypeID,
		}
		err := tx.Model(data).
			Where(filter).
			Delete(&filter).
			Error
		if err != nil {
			return err
		}

		return tx.Model(data).
			Create(data).
			Error
	})
}

func (a *AreaRepo) DefaultSceneTypeList(filter model.AreaDefaultSceneType) ([]response.AreaDefaultSceneType, error) {
	var list []response.AreaDefaultSceneType
	err := a.db.Table(fmt.Sprintf("%s %s", model.BusinessType{}.TableName(), "bt")).
		Select("bt.id", "bt.name", "adst.scene_type").
		Joins(
			fmt.Sprintf(
				"left join %s %s on bt.id = adst.business_type_id and adst.area_id = %d",
				model.AreaDefaultSceneType{}.TableName(),
				"adst",
				filter.AreaID,
			),
		).
		Where("bt.parent_id = ?", filter.BusinessTypeID).
		Order("bt.sort asc").
		Order("bt.id asc").
		Find(&list).
		Error
	return list, err
}

func (a *AreaRepo) FindNotExistsScene(areaID int) ([]int, error) {
	var (
		results = make([]int, 0)
	)

	err := a.db.Model(model.BusinessType{}).
		Where(
			"parent_id = -1 and not exists (?)",
			a.db.Model(model.AreaScene{}).
				Select("1").
				Where(
					"area_id = ? and business_type_id = business_types.id and type = ?",
					areaID,
					model.SceneType2D,
				),
		).
		Pluck("id", &results).
		Error
	return results, err
}

func (a *AreaRepo) FindSceneByAreaID(areaID int) ([]model.AreaScene, error) {
	var scenes []model.AreaScene
	err := a.db.Model(model.AreaScene{}).
		Where("area_id = ?", areaID).
		Find(&scenes).
		Error

	return scenes, err
}

func (a *AreaRepo) FindByUserIDWithAlarmCounts(userID int) ([]model.AreaWithAlarmCounts, error) {
	var (
		list   []model.AreaWithAlarmCounts
		sqlStr = "(select count(1) from alarms where exists (select 1 from devices where area_id = atc.area_id and business_type_id = atc.business_type_id and alarms.device_id = devices.id)) as alarm_counts"
	)
	err := a.db.Model(model.Area{}).
		Select(
			"id",
			"name",
			"CASE WHEN user_areas.area_id iS NULL THEN -1 ELSE areas.parent_id END AS parent_id",
		).
		Joins("LEFT JOIN user_areas ON areas.parent_id = user_areas.area_id AND user_areas.user_id = ?", userID).
		Where(
			"EXISTS (?)",
			a.db.Model(model.UserArea{}).
				Select("1").
				Where("user_id = ? and areas.id = user_areas.area_id", userID),
		).
		Preload("Types", func(db *gorm.DB) *gorm.DB {
			return db.Table(fmt.Sprintf(
				"%s atc",
				model.AreaTypeCount{}.TableName(),
			)).
				Select(
					"atc.area_id",
					"atc.business_type_id",
					"bt.name",
					sqlStr,
				).
				Joins("left join business_types bt on atc.business_type_id = bt.id").
				Where("atc.count > 0")
		}).
		Order("id asc").
		Find(&list).
		Error
	return list, err
}

func (a *AreaRepo) ListWithAlarmedByPID(param request.AreaListWithAlarmedByPid) ([]model.AreaWithAlarmed, error) {
	var (
		list    []model.AreaWithAlarmed
		builder = a.db.Model(model.Area{}).
			Where(
				"exists(?)",
				a.db.Model(model.UserArea{}).
					Select("1").
					Where("user_id = ? and areas.id = user_areas.area_id", param.UID),
			).
			Where("parent_id = ?", param.ParentID)
	)
	if param.BusinessTypeID != 0 {
		builder = builder.Where(
			"exists (?)",
			a.db.Model(model.AreaTypeCount{}).
				Select("1").
				Where(
					"area_id = areas.id and exists (?)",
					a.db.Model(model.BusinessTypeRelation{}).
						Select("1").
						Where("parent_id = ? and child_id = area_type_counts.business_type_id", param.BusinessTypeID),
				),
		)
	}
	err := builder.
		Preload("Alarms").
		Find(&list).
		Error
	return list, err
}

func (a *AreaRepo) FindByUserIDWithDevices(userID, btID int) ([]model.AreaWithDevices, error) {
	var (
		list []model.AreaWithDevices
	)
	builder := a.db.Model(model.Area{}).
		Select(
			"id",
			"name",
			"CASE WHEN user_areas.area_id iS NULL THEN -1 ELSE areas.parent_id END AS parent_id",
		).
		Joins("LEFT JOIN user_areas ON areas.parent_id = user_areas.area_id AND user_areas.user_id = ?", userID).
		Where(
			"EXISTS (?)",
			a.db.Model(model.UserArea{}).
				Select("1").
				Where("user_id = ? and areas.id = user_areas.area_id", userID),
		)
	// if btID != 0 {
	// 	builder = builder.Where(
	// 		"EXISTS (?)",
	// 		a.db.Model(model.AreaRelation{}).
	// 			Select("1").
	// 			Where(
	// 				"EXISTS (?) and parent_id = areas.id",
	// 				a.db.Model(model.AreaScene{}).
	// 					Select("1").
	// 					Where("business_type_id = ? and area_scenes.area_id = area_relations.child_id", btID),
	// 			),
	// 	)
	// }
	err := builder.
		Order("id asc").
		Preload("Devices", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "name", "status", "area_id").
				Where("business_type_id = ?", btID)
		}).
		Find(&list).
		Error

	return list, err
}

func (a *AreaRepo) IDs(uid int) ([]int, error) {
	var (
		ids []int
	)

	err := a.db.Model(model.Area{}).
		Select("id").
		Where(
			"EXISTS (?)",
			a.db.Model(model.UserArea{}).
				Where(
					"user_id = ? and areas.id = user_areas.area_id", uid,
				),
		).
		Pluck("id", &ids).
		Error

	return ids, err
}

func (a *AreaRepo) Alarmed(ids []int) ([]model.Area, error) {
	var (
		results []model.Area
	)
	err := a.db.Table(fmt.Sprintf("%s a", model.Area{}.TableName())).
		Select("id").
		Where(
			"id in (?) and exists (?)",
			ids,
			a.db.Raw(
				"? union ?",
				a.db.Model(model.Device{}).
					Select("1").
					Where(
						"exists (?) and a.id = area_id",
						a.db.Model(model.Alarm{}).
							Select("1").
							Where("device_id = devices.id"),
					),
				a.db.Table(fmt.Sprintf("%s ags", model.AreaGateway{}.TableName())).
					Select("1").
					Where(
						"exists (?) and a.id = ags.area_id",
						a.db.Model(model.Alarm{}).
							Select("1").
							Where("ags.gateway_id = gateway_id "),
					),
			),
		).
		Find(&results).
		Error

	return results, err
}
