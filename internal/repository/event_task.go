package repository

import (
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/system/config"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type EventTaskRepo struct {
	db *gorm.DB
	l  *zap.Logger
}

func NewEventTaskRepo(db *gorm.DB, config *config.Config, log *zap.Logger) *EventTaskRepo {
	return &EventTaskRepo{
		db: db,
		l:  log.Named("eventTask_repository:"),
	}
}

// add
func (d *EventTaskRepo) Create(event_task model.EventTask, config model.EventTaskConfig) error {
	return d.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(model.EventTask{}).Create(&event_task).Error
		if err != nil {
			return err
		}
		config.ID = event_task.ID
		return tx.Model(model.EventTaskConfig{}).Create(&config).Error
	})
}
func (d *EventTaskRepo) GetEventTaskList() ([]response.ListEventTask, error) {
	var resplist []response.ListEventTask
	err := d.db.Table("event_task").
		Select("event_task.*, event_task_configs.action->>'link_id' AS link_id").
		Joins("LEFT JOIN event_task_configs ON event_task.id = event_task_configs.id").
		Scan(&resplist).Error

	if err != nil {
		return nil, err
	}

	return resplist, nil
}
func (lr *EventTaskRepo) GetEventTaskDetail(id int) (model.EventTaskConfig, error) {
	var detail = model.EventTaskConfig{
		ID: id,
	}
	err := lr.db.First(&detail).Error
	return detail, err
}
func (d *EventTaskRepo) GetEventTaskAll() ([]model.EventTaskAll, error) {
	var results []model.EventTaskAll
	err := d.db.Table("event_task").
		Select("event_task.*, event_task_configs.condition, event_task_configs.action").
		Joins("LEFT JOIN event_task_configs ON event_task.id = event_task_configs.id").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	return results, nil
}
func (d *EventTaskRepo) Delete(id int) error {
	return d.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Where("id = ?", id).Delete(&model.EventTask{}).Error
		if err != nil {
			return err
		}

		return tx.Model(model.EventTaskConfig{}).Where("id = ?", id).Delete(&model.EventTaskConfig{}).Error
	})
}
func (d *EventTaskRepo) Update(id int, event_task model.EventTask, config model.EventTaskConfig) error {
	return d.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(&model.EventTask{}).
			Where("id = ?", id).
			Updates(&event_task).
			Error
		if err != nil {
			return err
		}
		return tx.Model(&model.EventTaskConfig{}).
			Where("id = ?", id).
			Updates(&config).
			Error

	})
}
func (s *EventTaskRepo) UpdateTaskStatus(id int, has_actioned bool) error {
	var task model.EventTaskConfig
	err := s.db.Model(&model.EventTaskConfig{}).Where("id = ?", id).First(&task).Error
	if err != nil {
		return err
	}

	task.Condition.HasActioned = has_actioned
	return s.db.Model(&model.EventTaskConfig{}).Where("id = ?", id).Updates(map[string]interface{}{
		"condition": task.Condition,
	}).Error
}
