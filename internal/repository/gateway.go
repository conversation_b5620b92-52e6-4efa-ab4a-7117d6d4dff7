package repository

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/opcua/scanner"
	"tw_platform/pkg/utils"

	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

type GatewayRepo struct {
	db           *gorm.DB
	r            *redis.Client
	setStatusKey string
	cacheKey     string
	cacheLockKey string
}

func NewGatewayRepo(db *gorm.DB, r *redis.Client) *GatewayRepo {
	return &GatewayRepo{
		db: db,
		r:  r,

		setStatusKey: "gateway_set_status:%d",
		cacheKey:     "gateway_cache:%d",
		cacheLockKey: "gateway_cache_lock:%d",
	}
}

func (g *GatewayRepo) Create(gateway model.GatewayWithPorts) (int, error) {
	var (
		temp  = gateway.Gateway
		ports = gateway.Ports
	)
	err := g.db.Session(
		&gorm.Session{
			Logger: logger.Default.LogMode(logger.Silent),
		},
	).Transaction(func(tx *gorm.DB) error {
		err := tx.Model(temp).
			Create(&temp).
			Error
		if err != nil {
			return err
		}

		for i := 0; i < len(ports); i++ {
			ports[i].GatewayID = temp.ID
		}
		return tx.Model(model.GatewayPort{}).
			Create(&ports).
			Error
	})
	return temp.ID, err //
}

func (g *GatewayRepo) Update(filter model.Gateway, gateway model.Gateway) error {
	return g.db.Where(filter).Updates(&gateway).Error
}

func (g *GatewayRepo) First(filter model.Gateway) (model.Gateway, error) {
	var gateway model.Gateway
	err := g.db.Where(filter).
		First(&gateway).
		Error
	return gateway, err
}

func (g *GatewayRepo) ListWithPortsPage(page, size int, filter model.Gateway) ([]model.GatewayWithPorts, int64, error) {
	var (
		list  []model.GatewayWithPorts
		total int64
	)
	err := g.db.Model(model.Gateway{}).
		Where(filter).
		Count(&total).
		Limit(size).
		Offset((page-1)*size).
		Preload("Ports", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "gateway_id")
		}).
		Find(&list).
		Error
	return list, total, err
}

func (g *GatewayRepo) FirstWithPorts(filter model.Gateway) (model.GatewayWithPorts, error) {
	var (
		gateway model.GatewayWithPorts
	)

	err := g.db.Model(gateway).
		Where(filter).
		Preload("Ports", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "gateway_id", "port")
		}).
		Find(&gateway).
		Error
	return gateway, err
}

func (g *GatewayRepo) FirstByDevice(id int) (model.Gateway, error) {
	var gateway model.Gateway

	err := g.db.Table("gateways as g").Debug().
		Select(
			"g.*",
		).
		Joins("inner join gateway_ports as gp on g.id=gp.gateway_id").
		Joins("inner join devices as d on d.port_id = gp.id").
		Where("d.id = ?", id).
		First(&gateway).
		Error
	return gateway, err
}

func (g *GatewayRepo) ListWithDevices(filter model.Gateway) ([]model.GatewayPortDevices, error) {
	var list []model.GatewayPortDevices
	err := g.db.Model(model.Gateway{}).
		Select(
			"id",
			"name",
			"version",
			"status",
			"type",
			"ip",
		).
		Where(filter).
		Preload("Ports", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "gateway_id", "port")
		}).
		Preload("Ports.Devices", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "port_id", "name", "type", "activity")
		}).
		Find(&list).
		Error
	return list, err
}
func (g *GatewayRepo) FirstWithDevices(filter model.Gateway) (model.GatewayPortDevices, error) {
	var gateway model.GatewayPortDevices
	err := g.db.Model(model.Gateway{}).
		Select(
			"id",
			"name",
			"version",
			"status",
			"type",
			"ip",
		).
		Where(filter).
		Preload("Ports", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "gateway_id", "port")
		}).
		Preload("Ports.Devices", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "port_id", "name", "type", "activity")
		}).
		First(&gateway).
		Error
	return gateway, err
}

func (g *GatewayRepo) PortWithGateway(filter model.GatewayPort) (model.PortWithGateway, error) {
	var port model.PortWithGateway

	err := g.db.Model(model.GatewayPort{}).
		Where(filter).
		Preload("GatewayInfo", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "flash_id", "ip")
		}).
		First(&port).
		Error
	return port, err
}

func (g *GatewayRepo) UpdatePort(id int, port model.GatewayPort) error {
	return g.db.Model(port).
		Where("id = ?", id).
		Updates(&port).
		Error
}

func (g *GatewayRepo) AddPort(port model.GatewayPort) error {
	return g.db.Model(port).
		Create(&port).
		Error
}

func (g *GatewayRepo) DeletePort(id int) error {
	var port model.GatewayPort
	return g.db.Model(port).
		Where("id = ?", id).
		Delete(&port).
		Error
}

func (g *GatewayRepo) PortList(id int) ([]model.GatewayPort, error) {
	var list []model.GatewayPort
	err := g.db.Model(model.GatewayPort{}).
		Where("gateway_id = ?", id).
		Order("id desc").
		Find(&list).
		Error
	return list, err
}

func (d *GatewayRepo) Delete(id int) error {
	var (
		gateway model.Gateway
		port    model.GatewayPort
	)
	return d.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(gateway).
			Where("id = ?", id).
			Delete(&gateway).
			Error
		if err != nil {
			return err
		}
		err = tx.Model(port).
			Where("gateway_id = ?", id).
			Delete(&port).
			Error
		if err != nil {
			return err
		}

		err = tx.Model(model.Alarm{}).
			Where("gateway_id = ?", id).
			Delete(&model.Alarm{}).
			Error
		if err != nil {
			return err
		}

		err = tx.Model(model.HistoryAlarm{}).
			Where("gateway_id = ? and status = ?", id, model.AlarmStatusHappened).
			Update("status", model.AlarmStatusRecovered).
			Error
		if err != nil {
			return err
		}
		err = tx.Model(model.GatewayAlarmBinding{}).
			Where("gateway_id = ?", id).
			Delete(&model.GatewayAlarmBinding{}).
			Error
		if err != nil {
			return err
		}
		err = tx.Model(model.AreaGateway{}).
			Where("gateway_id = ?", id).
			Delete(&model.AreaGateway{}).
			Error
		if err != nil {
			return err
		}

		return tx.Model(model.DepartmentGateway{}).
			Where("gateway_id = ?", id).
			Delete(&model.DepartmentGateway{}).
			Error
	})
}

func (d *GatewayRepo) GetAlarmLock(id int, status model.GatewayStatus) bool {
	var (
		ctx, cancel = utils.RedisCtx()
	)
	defer cancel()

	return d.r.SetNX(
		ctx,
		fmt.Sprintf(d.setStatusKey, id),
		status,
		10*time.Second,
	).
		Val()
}

func (d *GatewayRepo) SetStatus(id int, status model.GatewayStatus) (bool, error) {
	var (
		deviceStatus = model.DeviceStatusNormal
		ctx, cancel  = utils.RedisCtx()
		// err          error
		ok bool
	)

	defer cancel()

	result := d.r.SetNX(
		ctx,
		fmt.Sprintf(d.setStatusKey, id),
		status,
		10*time.Second,
	).
		Val()
	if !result {
		return ok, nil
	}

	ok = true

	if status == model.GatewayStatusOffline {
		deviceStatus = model.DeviceStatusGatewayOffline
	}

	return ok, d.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(model.Gateway{}).
			Where("id = ?", id).
			Update("status", status).
			Error
		if err != nil {
			return err
		}
		return tx.Model(model.Device{}).
			Where(
				"exists (?)",
				tx.Model(model.GatewayPort{}).
					Select("1").
					Where("gateway_id = ? and devices.port_id = id", id),
			).
			Update("status", deviceStatus).
			Error
	})
}

func (d *GatewayRepo) OPCCreate(id int, data scanner.ScanResult) error {
	jsonData, err := json.Marshal(data.Nodes)
	if err != nil {
		return err
	}

	return d.db.Model(model.Opcua{}).Create(&model.Opcua{
		ID:       id,
		NodeData: string(jsonData),
	}).Error
}

func (d *GatewayRepo) OPCNodeList(id int) (model.Opcua, error) {
	var info model.Opcua
	err := d.db.Model(model.Opcua{}).
		Where("id = ?", id).
		Order("id desc").
		Find(&info).
		Error
	return info, err
}

func (d *GatewayRepo) ModelList() ([]string, error) {
	var models []string
	err := d.db.Model(model.Gateway{}).
		Select("model").
		Group("model").
		Pluck("model", &models).
		Error
	return models, err
}

func (d *GatewayRepo) FirstByPortID(portID int) (model.Gateway, error) {
	var gateway model.Gateway
	err := d.db.Model(gateway).
		Joins("inner join gateway_ports gp on gateways.id = gp.gateway_id").
		Where("gp.id= ?", portID).
		First(&gateway).
		Error
	return gateway, err
}

func (d *GatewayRepo) IPList() ([]string, error) {
	var (
		ips []string
	)
	err := d.db.Model(model.Gateway{}).
		Where("type = ?", model.GatewayTypeModbus).
		Pluck("ip", &ips).
		Error

	return ips, err
}

func (d *GatewayRepo) AlarmBinding(bindingID int, data []model.GatewayAlarmBinding) error {
	return d.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(model.GatewayAlarmBinding{}).
			Where("binding_id = ?", bindingID).
			Delete(&model.GatewayAlarmBinding{}).
			Error
		if err != nil {
			return err
		}

		return tx.Model(model.GatewayAlarmBinding{}).
			Create(&data).
			Error

	})
}

func (d *GatewayRepo) IDsByBindingID(bindingID int) ([]int, error) {
	var ids []int
	err := d.db.Model(model.GatewayAlarmBinding{}).
		Where("binding_id = ?", bindingID).
		Pluck("gateway_id", &ids).
		Error
	return ids, err
}

func (d *GatewayRepo) BatchCreate(param []model.GatewayWithPorts) error {
	var (
		gateways = make([]model.Gateway, 0, len(param))
		ports    = make([][]model.GatewayPort, 0)
		portArr  = make([]model.GatewayPort, 0)
	)

	for _, v := range param {
		gateways = append(gateways, v.Gateway)
		ports = append(ports, v.Ports)
	}

	return d.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(model.Gateway{}).
			Create(&gateways).
			Error
		if err != nil {
			return err
		}

		for i, port := range ports {
			for _, v := range port {
				v.GatewayID = gateways[i].ID
				portArr = append(portArr, v)
			}
		}

		return tx.Model(model.GatewayPort{}).
			Create(&portArr).
			Error
	})
}

func (d *GatewayRepo) GetAlarmBinding(id int) (model.GatewayAlarmBinding, error) {
	var binding model.GatewayAlarmBinding
	err := d.db.Model(model.GatewayAlarmBinding{}).
		Where("gateway_id = ?", id).
		First(&binding).
		Error
	return binding, err
}

func (g *GatewayRepo) Statistic(token request.TokenInfo) (response.GatewayStatistic, error) {
	var (
		statistic response.GatewayStatistic
		gateway   = model.Gateway{}
	)
	err := g.db.Model(gateway).
		Scopes(g.filterByUserID(token.UID)).
		Count(&statistic.Total).
		Error
	if err != nil {
		return statistic, err
	}
	err = g.db.Model(gateway).
		Scopes(g.filterByUserID(token.UID)).
		Where("status = ?", model.GatewayStatusOnline).
		Count(&statistic.Online).
		Error
	if err != nil {
		return statistic, err
	}
	err = g.db.Model(gateway).
		Scopes(g.filterByUserID(token.UID)).
		Where("status = ?", model.GatewayStatusOffline).
		Count(&statistic.Offline).
		Error
	if err != nil {
		return statistic, err
	}
	err = g.db.Model(gateway).Select("name", "status").
		Scopes(g.filterByUserID(token.UID)).
		Find(&statistic.List).
		Error
	return statistic, err
}

func (g *GatewayRepo) CountByUID(uid int) (int64, error) {
	var (
		total   int64
		gateway = model.Gateway{}
	)
	err := g.db.Model(gateway).
		Scopes(g.filterByUID(uid)).
		Count(&total).
		Error
	return total, err
}

func (g *GatewayRepo) filterByUID(uid int) func(tx *gorm.DB) *gorm.DB {
	var (
		ag = model.AreaGateway{}
		ua = model.UserArea{}
	)
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where(
			"exists (?)",
			g.db.Model(ag).
				Select("1").
				Where(
					"exists (?) and gateways.id = gateway_id",
					g.db.Model(ua).
						Select("1").
						Where(fmt.Sprintf(
							"user_id = ? and area_id = %s.area_id",
							ag.TableName(),
						), uid),
				),
		)
	}
}

func (g *GatewayRepo) Replace(oldID, newID int, devices []model.Device) error {
	return g.db.Transaction(func(tx *gorm.DB) error {
		for _, v := range devices {
			err := g.db.Model(v).
				Where("id = ?", v.ID).
				Update("port_id", *v.PortID).
				Error
			if err != nil {
				continue
			}
		}

		err := tx.Model(model.GatewayAlarmBinding{}).
			Where("gateway_id = ?", oldID).
			Update("gateway_id", newID).
			Error
		if err != nil {
			return err
		}
		err = tx.Model(model.AreaGateway{}).
			Where("gateway_id = ?", oldID).
			Update("gateway_id", newID).
			Error
		if err != nil {
			return err
		}

		err = tx.Model(model.DepartmentGateway{}).
			Where("gateway_id = ?", oldID).
			Update("gateway_id", newID).
			Error

		return err
	})
}

func (g *GatewayRepo) IDsByUID(uid int) ([]int, error) {
	var (
		ids []int
	)
	err := g.db.Model(model.AreaGateway{}).
		Where(
			"EXISTS (?)",
			g.db.Model(model.UserArea{}).
				Select("1").
				Where(
					"user_id = ? and area_gateways.area_id = user_areas.area_id",
					uid,
				),
		).
		Pluck("gateway_id", &ids).
		Error
	return ids, err
}

func (g *GatewayRepo) Cache(id int) model.GatewayStatus {
	//虚拟设备等不存在网关时，
	//使用非网关状态返回，避免频繁触发写缓存
	if id == 0 {
		return 100
	}
	var (
		ctx, cancel = utils.RedisCtx()
	)
	defer cancel()
	result, err := g.r.Get(ctx, fmt.Sprintf(
		g.cacheKey,
		id,
	)).
		Result()
	if err != nil {
		return 0
	}
	status, err := strconv.Atoi(result)
	if err != nil {
		return 0
	}
	return model.GatewayStatus(status)
}

func (g *GatewayRepo) SetCache(id int, status model.GatewayStatus) error {
	ctx, cancel := utils.RedisCtx()
	defer cancel()

	return g.r.Set(
		ctx,
		fmt.Sprintf(g.cacheKey, id),
		status,
		time.Hour,
	).
		Err()
}

func (g *GatewayRepo) GetCacheLock(id int) bool {
	ctx, cancel := utils.RedisCtx()
	defer cancel()

	return g.r.SetNX(
		ctx,
		fmt.Sprintf(g.cacheLockKey, id),
		1,
		10*time.Second,
	).
		Val()
}

func (g *GatewayRepo) filterByUserID(uid int) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where(
			"exists (?)",
			g.db.Model(model.AreaGateway{}).
				Select("1").
				Where("gateway_id = gateways.id and exists (?)",
					g.db.Model(model.UserArea{}).
						Select("1").
						Where("user_id = ? and area_id = area_gateways.area_id", uid),
				),
		)
	}
}

func (g *GatewayRepo) CreateWithDevices(gateway model.Gateway, devices []model.Device, configs []model.Config) error {
	return g.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(gateway).Create(&gateway).Error
		if err != nil {
			return err
		}

		for i := 0; i < len(devices); i++ {
			devices[i].GatewayID = &gateway.ID
		}

		err = tx.Model(model.Device{}).Create(&devices).Error
		if err != nil {
			return err
		}
		for idx, device := range devices {
			configs[idx].ID = device.ID
		}
		return tx.Model(model.Config{}).Create(&configs).Error
	})
}
