package repository

import (
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"

	"gorm.io/gorm"
)

type LinkAgeCaptureRepo struct {
	db *gorm.DB
}

func NewLinkAgeCaptureRepo(db *gorm.DB) *LinkAgeCaptureRepo {
	return &LinkAgeCaptureRepo{db: db}
}
func (r *LinkAgeCaptureRepo) Create(capture *model.LinkageCapture) error {
	return r.db.Create(capture).Error
}
func (r *LinkAgeCaptureRepo) GetList(query request.LinkageCaptureList) ([]model.LinkageCapture, int64, error) {
	var captures []model.LinkageCapture
	var total int64
	db := r.db.Model(&model.LinkageCapture{})
	if query.BeginAt != "" && query.EndAt != "" {

		db = db.Where("created_at BETWEEN ? AND ?", query.BeginAt, query.EndAt)
	} else if query.BeginAt != "" {

		db = db.Where("created_at >= ?", query.BeginAt)
	} else if query.EndAt != "" {

		db = db.Where("created_at <= ?", query.EndAt)
	}

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	offset := (query.Page - 1) * query.Size
	if err := db.Offset(offset).Limit(query.Size).Find(&captures).Error; err != nil {
		return nil, 0, err
	}

	return captures, total, nil
}
