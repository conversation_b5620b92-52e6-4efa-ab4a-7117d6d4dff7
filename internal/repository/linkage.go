package repository

import (
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/system/config"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type LinkageRepo struct {
	db     *gorm.DB
	config *config.Config
	l      *zap.Logger
}

func NewLinkageRepo(db *gorm.DB, config *config.Config, log *zap.Logger) *LinkageRepo {
	return &LinkageRepo{
		db:     db,
		config: config,
		l:      log.Named("linkage_repository:"),
	}
}
func (lr *LinkageRepo) GetLinkageList() ([]model.Linkage, error) {
	var list []model.Linkage
	result := lr.db.
		Order("id asc").
		Find(&list).
		Error
	if result != nil {
		return list, result
	}
	return list, nil
}
func (lr *LinkageRepo) GetLinkageName(id int) (response.LinkageName, error) {
	var info = response.LinkageName{}
	err := lr.db.Model(model.Linkage{}).
		Select("name").
		First(&info).
		Error
	return info, err
}
func (lr *LinkageRepo) GetLinkageDetail(id int) (model.LinkageData, error) {
	var detail = model.LinkageData{
		ID: id,
	}
	err := lr.db.First(&detail).Error
	return detail, err
}
func (lr *LinkageRepo) CreateLinkage(linkage model.Linkage, data model.LinkageData) (int, error) {
	var id int
	return id, lr.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(model.Linkage{}).Create(&linkage).Error
		if err != nil {
			return err
		}
		data.ID = linkage.ID
		err = tx.Model(model.LinkageData{}).Create(&data).Error
		if err != nil {
			return err
		}
		id = linkage.ID
		return err
	})
}

func (lr *LinkageRepo) DeleteLinkage(id int) error {
	return lr.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Where("id = ?", id).Delete(&model.Linkage{}).Error
		if err != nil {
			return err
		}

		return tx.Model(model.LinkageData{}).Where("id = ?", id).Delete(&model.LinkageData{}).Error
	})
}
func (lr *LinkageRepo) UpdateLinkageInfo(id int, info model.Linkage) (int64, error) {
	builder := lr.db.Model(info).
		Where("id = ?", id).
		Updates(info)

	return builder.RowsAffected, builder.Error
}
func (lr *LinkageRepo) UpdateLinkageData(id int, info model.LinkageData) (int64, error) {
	builder := lr.db.Model(model.LinkageData{}).
		Where("id = ?", id).
		Updates(info)

	return builder.RowsAffected, builder.Error
}
