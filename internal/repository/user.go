package repository

import (
	"fmt"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/system/config"

	"gorm.io/gorm"
)

type UserRepo struct {
	db                 *gorm.DB
	config             *config.Config
	defaultAdminRoleID int
}

func NewUserRepo(db *gorm.DB, config *config.Config) *UserRepo {
	return &UserRepo{
		db:                 db,
		config:             config,
		defaultAdminRoleID: 1,
	}
}

func (us *UserRepo) Create(user *model.User) error {
	return us.db.Create(user).Error
}

func (us *UserRepo) First(filter model.User) (model.User, error) {
	var user model.User
	err := us.db.
		Where(filter).
		First(&user).
		Error
	return user, err
}

func (us *UserRepo) List(page, size int, filter model.User) ([]response.UserList, int64, error) {
	var (
		total int64
		list  []response.UserList
	)
	builder := us.db.Model(model.User{})
	if filter.Nickname != "" {
		builder = builder.Where("nickname like ?", fmt.Sprintf("%%%s%%", filter.Nickname))
	}
	if filter.Phone != "" {
		builder = builder.Where("phone like ?", fmt.Sprintf("%%%s%%", filter.Phone))
	}
	if filter.RoleID != 0 {
		builder = builder.Where("role_id = ?", filter.RoleID)
	}
	err := builder.Count(&total).
		Limit(size).
		Offset((page-1)*size).
		Preload("RoleInfo", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "name")
		}).
		Find(&list).
		Error
	return list, total, err
}

func (us *UserRepo) SetArea(uid int, areas []model.UserArea) error {
	return us.db.Transaction(func(tx *gorm.DB) error {
		var ua model.UserArea
		err := tx.Model(ua).Where("user_id = ?", uid).Delete(&ua).Error
		if err != nil {
			return err
		}

		if len(areas) == 0 {
			return nil
		}

		return tx.Model(ua).Create(&areas).Error
	})
}

func (us *UserRepo) GetArea(uid int) ([]int, error) {
	var ids []int
	err := us.db.Model(model.UserArea{}).
		Where("user_id = ?", uid).
		Pluck("area_id", &ids).
		Error
	return ids, err
}

func (us *UserRepo) SetDepartment(uid int, departments []model.UserDepartment) error {
	return us.db.Transaction(func(tx *gorm.DB) error {
		var ud model.UserDepartment
		err := tx.Model(ud).Where("user_id = ?", uid).Delete(&ud).Error
		if err != nil {
			return err
		}
		if len(departments) == 0 {
			return nil
		}
		return tx.Model(ud).Create(&departments).Error
	})
}

func (us *UserRepo) GetDepartment(uid int) ([]int, error) {
	var ids []int
	err := us.db.Model(model.UserDepartment{}).
		Where("user_id = ?", uid).
		Pluck("department_id", &ids).
		Error
	return ids, err
}

func (us *UserRepo) SetBusinessType(uid int, businessTypes []model.UserBusinessType) error {
	return us.db.Transaction(func(tx *gorm.DB) error {
		var ubt model.UserBusinessType
		err := tx.Model(ubt).Where("user_id = ?", uid).Delete(&ubt).Error
		if err != nil {
			return err
		}
		if len(businessTypes) == 0 {
			return nil
		}
		return tx.Model(ubt).Create(&businessTypes).Error
	})
}

func (us *UserRepo) GetBusinessType(uid int) ([]int, error) {
	var ids []int
	err := us.db.Model(model.UserBusinessType{}).
		Where("user_id = ?", uid).
		Pluck("business_type_id", &ids).
		Error
	return ids, err
}

func (us *UserRepo) Update(user model.User) error {
	return us.db.Model(user).Where("id = ?", user.ID).Updates(&user).Error
}

func (us *UserRepo) ListSimplify(filter model.User) ([]model.User, error) {
	var users []model.User
	err := us.db.Model(filter).
		Select("id", "nickname", "phone").
		Where(filter).
		Find(&users).
		Error
	return users, err
}

func (us *UserRepo) Delete(id int) error {
	return us.db.Delete(&model.User{ID: id}).Error
}

func (us *UserRepo) FirstWithRole(filter model.User) (model.UserWithRole, error) {
	var user model.UserWithRole
	err := us.db.Model(user).
		Select(
			"users.id",
			"users.nickname",
			"users.role_id",
			"users.password",
			"users.status",
			"roles.name as role_name",
		).
		Where(filter).
		Joins("left join roles on users.role_id = roles.id").
		First(&user).
		Error
	return user, err
}

func (us *UserRepo) ListByID(ids []int) ([]model.User, error) {
	var users []model.User
	err := us.db.Model(model.User{}).
		Select("id", "nickname").
		Where("id in ?", ids).
		Order("id asc").
		Find(&users).
		Error
	return users, err
}

func (us *UserRepo) ListByMenuID(menuID int) ([]model.User, error) {
	var users []model.User
	err := us.db.Model(model.User{}).
		Select("id", "nickname").
		Where(
			"EXISTS (?)",
			us.db.Model(model.RoleMenu{}).
				Select("1").
				Where(
					"role_id = users.role_id and menu_id = ?",
					menuID,
				),
		).
		Find(&users).
		Error
	return users, err
}

func (us *UserRepo) BatchCreate(users []model.User) error {
	return us.db.Model(model.User{}).Create(&users).Error
}

func (us *UserRepo) WechatList() ([]model.User, error) {
	var users []model.User
	err := us.db.Model(model.User{}).
		Where("status >= ?", model.QywxInit).
		Find(&users).
		Error
	return users, err
}

func (us *UserRepo) UpdateByFilter(filter, user model.User) error {
	return us.db.Model(model.User{}).
		Where(filter).
		Updates(user).
		Error
}

func (us *UserRepo) SelfUpdate(user model.User) error {
	return us.db.Model(user).
		Where("id = ?", user.ID).
		Updates(user).
		Error
}

func (us *UserRepo) FirstWithSelector(selector []string, filter model.User) (model.User, error) {
	var user model.User
	err := us.db.Model(user).
		Select(selector).
		Where(filter).
		First(&user).
		Error
	return user, err
}

func (us *UserRepo) ListWithoutPage(filter model.User) ([]model.User, error) {
	var users []model.User
	builder := us.db.Model(model.User{}).
		Select("id", "nickname")

	if filter.Nickname != "" {
		builder = builder.Where("nickname like ?", fmt.Sprintf("%%%s%%", filter.Nickname))
	}

	err := builder.
		Find(&users).
		Error
	return users, err
}
