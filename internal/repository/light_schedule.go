package repository

import (
	"time"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type LightScheduleRepo struct {
	db *gorm.DB
	l  *zap.Logger
}

func NewLightScheduleRepo(db *gorm.DB, log *zap.Logger) *LightScheduleRepo {
	return &LightScheduleRepo{
		db: db,
		l:  log.Named("light_schedule_repository:"),
	}
}

func (l *LightScheduleRepo) Create(param request.CreateLightSchedule) (int, error) {
	var (
		data = model.LightSchedule{
			AreaID:  param.AreaID,
			Name:    param.Name,
			Cron:    param.Cron,
			Status:  param.Status,
			Type:    param.Type,
			BeginAt: param.BeginAt,
			EndAt:   param.EndAt,
		}
		actions = make([]model.LightScheduleDetail, 0, len(param.Actions))
	)
	err := l.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(model.LightSchedule{}).
			Create(&data).
			Error
		if err != nil {
			return err
		}

		for _, action := range param.Actions {
			actions = append(actions, model.LightScheduleDetail{
				ScheduleID: data.ID,
				DeviceID:   action.DeviceID,
				UnitID:     action.UnitID,
				Value:      action.Value,
			})
		}
		if len(actions) == 0 {
			return nil
		}
		return tx.Model(model.LightScheduleDetail{}).
			Create(&actions).
			Error
	})
	return data.ID, err
}

func (l *LightScheduleRepo) Update(data model.LightSchedule) error {
	return l.db.Model(data).Where("id = ?", data.ID).Updates(data).Error
}
func (l *LightScheduleRepo) UpdateWithDetail(data model.LightSchedule, add []model.LightScheduleDetail, del []int) error {
	var lsd model.LightScheduleDetail
	return l.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(data).Where("id = ?", data.ID).Updates(data).Error
		if err != nil {
			return err
		}
		if len(del) > 0 {
			err = tx.Model(lsd).
				Where("id IN (?)", del).
				Delete(&lsd).
				Error
			if err != nil {
				return err
			}
		}
		if len(add) > 0 {
			err = tx.Model(lsd).
				Create(&add).
				Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

func (l *LightScheduleRepo) FirstWithActions(filter model.LightSchedule) (model.LightScheduleWithAction, error) {
	var data model.LightScheduleWithAction
	return data, l.db.Model(data).
		Where(filter).
		Preload("Actions").
		First(&data).
		Error
}

func (l *LightScheduleRepo) Actions(filter model.LightScheduleDetail) ([]model.LightScheduleDetail, error) {
	var actions []model.LightScheduleDetail
	err := l.db.Model(filter).
		Where(filter).
		Find(&actions).
		Error
	return actions, err
}

func (l *LightScheduleRepo) List(areaID int) ([]model.LightSchedule, error) {
	var list []model.LightSchedule
	err := l.db.Model(model.LightSchedule{}).
		Select("id", "name", "cron").
		Where("area_id = ?", areaID).
		Find(&list).
		Error
	return list, err
}
func (l *LightScheduleRepo) ListByUserID(UID int) ([]model.LightSchedule, error) {
	var list []model.LightSchedule
	err := l.db.Model(model.LightSchedule{}).
		Select("id", "name", "cron").
		Where("EXISTS (?)",
			l.db.Model(model.UserArea{}).
				Select("1").
				Where("user_id = ? and area_id = light_schedules.area_id", UID),
		).
		Find(&list).
		Error
	return list, err
}

func (l *LightScheduleRepo) ActionsByID(id int) ([]model.LightScheduleDetail, error) {
	var actions []model.LightScheduleDetail
	err := l.db.Model(model.LightScheduleDetail{}).
		Where("schedule_id = ?", id).
		Find(&actions).
		Error
	return actions, err
}

func (l *LightScheduleRepo) First(filter model.LightSchedule) (model.LightSchedule, error) {
	var data model.LightSchedule
	err := l.db.Model(data).
		Where(filter).
		First(&data).
		Error
	return data, err
}

func (l *LightScheduleRepo) Delete(filter model.LightSchedule) error {
	return l.db.Where(filter).Delete(&model.LightSchedule{}).Error
}

func (l *LightScheduleRepo) CronAfter(actionIDs []int, logs []model.LightScheduleLog) error {
	return l.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(model.LightScheduleDetail{}).
			Where("id IN (?)", actionIDs).
			Update("execed_at", time.Now()).
			Error
		if err != nil {
			return err
		}
		return tx.Model(model.LightScheduleLog{}).
			Create(&logs).
			Error
	})
}

func (l *LightScheduleRepo) ActionsByAreaID(areaID int) ([]model.LightScheduleDetail, error) {
	var actions []model.LightScheduleDetail
	err := l.db.Model(model.LightScheduleDetail{}).
		Where(
			"EXISTS (?)",
			l.db.Model(model.LightSchedule{}).
				Select("1").
				Where("id = light_schedule_details.schedule_id and area_id = ?", areaID),
		).
		Order("execed_at desc").
		Limit(15).
		Find(&actions).
		Error
	return actions, err
}

func (l *LightScheduleRepo) ActionsByUserID(UID int) ([]model.LightScheduleDetail, error) {
	var actions []model.LightScheduleDetail
	err := l.db.Model(model.LightScheduleDetail{}).
		Where(
			"EXISTS (?)",
			l.db.Model(model.LightSchedule{}).
				Select("1").
				Where(
					"id = light_schedule_details.schedule_id and EXISTS (?)",
					l.db.Model(model.UserArea{}).
						Select("1").
						Where("user_id = ? and area_id = light_schedules.area_id", UID),
				),
		).
		Order("execed_at desc").
		Limit(15).
		Find(&actions).
		Error
	return actions, err
}
