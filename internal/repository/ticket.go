package repository

import (
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"

	"gorm.io/gorm"
)

type TicketRepo struct {
	db *gorm.DB
}

func NewTicketRepo(db *gorm.DB) *TicketRepo {
	return &TicketRepo{
		db: db,
	}
}

func (t *TicketRepo) Create(userID int, ticket model.Ticket, alarmID int) error {
	return t.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Create(&ticket).Error
		if err != nil {
			return err
		}
		err = tx.Create(&model.TicketTimeLine{
			TicketID: ticket.ID,
			UserId:   userID,
			Status:   ticket.Status,
		}).Error
		if err != nil {
			return err
		}

		if alarmID != 0 && ticket.Type <= model.TicketTypeCommon {
			hat := model.HistoryAlarmTicket{
				HistoryAlarmID: alarmID,
				TicketID:       ticket.ID,
			}
			err = tx.Model(hat).Create(&hat).Error
		}
		return err
	})
}

func (t *TicketRepo) List(page int, size int, filter model.Ticket) ([]model.TicketWithUser, int64, error) {
	var (
		total int64
		list  []model.TicketWithUser
	)
	err := t.db.Model(model.Ticket{}).
		Where(filter).
		Count(&total).
		Limit(size).
		Offset((page-1)*size).
		Order("id desc").
		Preload("CreatedUserInfo", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "nickname")
		}).
		Preload("AssignedUserInfo", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "nickname")
		}).
		Preload("Timelines.UserInfo", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "nickname")
		}).
		Find(&list).
		Error

	return list, total, err
}

func (t *TicketRepo) Timeline(id int) ([]model.TicketTimelineWithUser, error) {
	var list []model.TicketTimelineWithUser
	err := t.db.Model(model.TicketTimeLine{}).
		Where("ticket_id = ?", id).
		Order("id desc").
		Preload("UserInfo", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "nickname")
		}).
		Find(&list).
		Error
	return list, err
}

func (t *TicketRepo) Update(param request.UpdateTicket) error {
	var updater = model.Ticket{
		Status: param.Status,
		FlowID: param.FlowID,
	}
	return t.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(model.Ticket{}).
			Where("id = ?", param.ID).
			Updates(updater).
			Error
		if err != nil {
			return err
		}

		timeline := model.TicketTimeLine{
			TicketID: param.ID,
			Status:   param.Status,
			UserId:   param.UID,
			Comment:  &param.Comment,
		}
		return tx.Model(timeline).
			Create(&timeline).
			Error
	})
}

func (t *TicketRepo) Count(filter model.Ticket) (int64, error) {
	var count int64
	err := t.db.Model(model.Ticket{}).
		Where(filter).
		Count(&count).
		Error
	return count, err
}

func (t *TicketRepo) Detail(id int) (model.TicketWithUser, error) {
	var detail model.TicketWithUser
	err := t.db.Model(detail).
		Where("id = ?", id).
		Preload("CreatedUserInfo", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "nickname")
		}).
		Preload("AssignedUserInfo", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "nickname")
		}).
		Preload("Timelines.UserInfo", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "nickname")
		}).
		First(&detail).
		Error
	return detail, err
}

func (t *TicketRepo) UpdateAsset(param request.UpdateTicketAsset) error {
	var ticket model.Ticket
	err := t.db.Model(ticket).
		Where("flow_id = ?", param.FlowID).
		First(&ticket).
		Error
	if err != nil {
		return err
	}
	return t.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(ticket).
			Where("id = ?", ticket.ID).
			Update("status", param.Status).
			Error
		if err != nil {
			return err
		}
		return tx.Model(model.TicketTimeLine{}).
			Create(&model.TicketTimeLine{
				TicketID: ticket.ID,
				Status:   param.Status,
				// 此处UID恒为0
				UserId:  param.UID,
				Comment: &param.Comment,
			}).
			Error
	})
}
