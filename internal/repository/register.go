package repository

import (
	"encoding/base64"
	"fmt"
	"tw_platform/pkg/model"
	"tw_platform/pkg/system/config"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type RegisterRepo struct {
	db     *gorm.DB
	config *config.Config
	l      *zap.Logger
}

const (
	FUNCITEM_TABLE = "funcitem"
)

func NewRegisterRepo(db *gorm.DB, config *config.Config, log *zap.Logger) *RegisterRepo {
	return &RegisterRepo{
		db:     db,
		config: config,
		l:      log.Named("register_repository:"),
	}
}

func (r *RegisterRepo) GetRegInfo() (*model.RegisterRepo, error) {
	var (
		info model.RegisterRepo
		err  error
	)
	if err = r.db.Model(model.RegisterRepo{}).First(&info).Error; err != nil {
		return nil, err
	}
	return &info, err
}

func (r *RegisterRepo) SetRegInfo(dbstr, threed string) error {
	if err := r.db.Exec("delete from registe").Error; err != nil {
		return err
	}
	ciperStr := base64.StdEncoding.EncodeToString([]byte(dbstr))
	return r.db.Model(model.RegisterRepo{}).Create(&model.RegisterRepo{
		RegInfo: ciperStr,
		ThreeD:  threed,
	}).Error

}

func (r *RegisterRepo) SetFunction(funcs []model.Function) error {
	dstr := fmt.Sprintf("delete from %s", FUNCITEM_TABLE)
	if err := r.db.Exec(dstr).Error; err != nil {
		return err
	}

	if err := r.db.Table(FUNCITEM_TABLE).Create(funcs).Error; err != nil {
		return err
	}
	return nil
}

func (r *RegisterRepo) GetFuncItems() ([]model.Function, error) {
	items := []model.Function{}
	if err := r.db.Model(model.Function{}).Find(&items).Error; err != nil {
		return nil, err
	}
	return items, nil
}

func (r *RegisterRepo) GetFuncItem(filter model.Function) (model.Function, error) {
	var item model.Function
	err := r.db.Model(item).
		Where(filter).
		First(&item).
		Error
	return item, err
}
