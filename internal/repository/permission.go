package repository

import (
	"fmt"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"

	"gorm.io/gorm"
)

type PermissionRepo struct {
	db *gorm.DB
}

func NewPermissionRepo(db *gorm.DB) *PermissionRepo {
	return &PermissionRepo{
		db: db,
	}
}

func (p *PermissionRepo) Create(permission model.Permission) (int, error) {
	err := p.db.Create(&permission).Error
	return permission.ID, err
}

func (p *PermissionRepo) List(param request.PermissionList) ([]model.Permission, int64, error) {
	var (
		list    []model.Permission
		total   int64
		builder = p.db.Model(model.Permission{})
	)

	if param.Method != 0 {
		builder = builder.Where("method = ?", param.Method)
	}
	if param.Name != "" {
		builder = builder.Where("name like ?", fmt.Sprintf("%s%%", param.Name))
	}
	if param.Path != "" {
		builder = builder.Where("path like ?", fmt.Sprintf("%s%%", param.Path))
	}

	err := builder.
		Count(&total).
		Limit(param.Size).
		Offset((param.Page - 1) * param.Size).
		Order("id desc").
		Find(&list).
		Error
	return list, total, err
}

func (p *PermissionRepo) Update(id int, permission model.Permission) error {
	return p.db.Where("id = ?", id).
		Updates(permission).
		Error
}

func (p *PermissionRepo) Delete(id int) error {
	var permission model.Permission
	return p.db.Model(permission).
		Where("id = ?", id).
		Delete(&permission).
		Error
}

func (p *PermissionRepo) All() ([]model.Permission, error) {
	var list []model.Permission
	return list, p.db.Find(&list).Error
}

func (p *PermissionRepo) First(id int) (model.Permission, error) {
	var permission model.Permission
	err := p.db.First(&permission, id).Error
	return permission, err
}
