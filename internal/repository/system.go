package repository

import (
	"tw_platform/pkg/model"

	"gorm.io/gorm"
)

type SystemRepo struct {
	db *gorm.DB
}

func NewSystemRepo(db *gorm.DB) *SystemRepo {
	return &SystemRepo{
		db: db,
	}
}

func (s *SystemRepo) First(id int) (model.SystemInfo, error) {
	var info model.SystemInfo
	err := s.db.Where("id = ?", id).First(&info).Error
	return info, err
}

func (s *SystemRepo) Update(info model.SystemInfo) error {
	return s.db.Model(info).
		Where("id = ?", info.ID).
		Select("name", "copyright").
		Updates(info).
		Error
}
