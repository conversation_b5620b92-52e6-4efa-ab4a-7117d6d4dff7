package repository

import (
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/system/config"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type DoorRepo struct {
	db     *gorm.DB
	config *config.Config
	l      *zap.Logger
}

func NewDoorRepo(db *gorm.DB, config *config.Config, log *zap.Logger) *DoorRepo {
	return &DoorRepo{
		db:     db,
		config: config,
		l:      log.Named("door_repository:"),
	}
}

func (d *DoorRepo) GetDoorInfo(id int) (*model.DoorInfo, error) {
	var (
		info model.Config
	)
	if err := d.db.Model(model.Config{}).Where("id = ?", id).First(&info).Error; err != nil {
		return nil, err
	}
	return info.Config.DoorDevice, nil
}

func (d *DoorRepo) GetDoorList() (response.GetDoorListRes, error) {
	var (
		doorDevice []model.DeviceWithAreaName
		doorConfig []model.Config
		configIds  []int64
		total      int64
		respList   = make([]response.GetDoorListResItem, 0)
	)

	//获取device
	info := d.db.Model(model.Device{}).
		Select(
			"devices.id",
			"devices.name",
			"devices.type",
			"devices.activity",
			"devices.status",
			"devices.area_id",
			"a.name as area_name",
			"devices.business_type_id",
			"devices.created_at",
			"devices.updated_at",
		).
		Joins("left join areas as a on devices.area_id = a.id").
		Where("type = ?", "door").
		Order("id asc").
		Find(&doorDevice)
	if info.Error != nil {
		return nil, info.Error
	}
	err := info.Count(&total).Error
	if err != nil {
		return nil, err
	}
	for i := range doorDevice {
		configIds = append(configIds, int64(doorDevice[i].ID))
	}
	//获取config
	if err = d.db.
		Model(model.Config{}).
		Where("id in ?", configIds).
		Order("id asc").
		Find(&doorConfig).Error; err != nil {
		return nil, err
	}

	for i := 0; i < len(doorConfig); i++ {
		respList = append(respList, response.GetDoorListResItem{
			Device: doorDevice[i],
			Config: *doorConfig[i].Config.DoorDevice,
		})
	}
	return respList, nil
}
