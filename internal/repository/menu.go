package repository

import (
	"tw_platform/pkg/model"

	"gorm.io/gorm"
)

const (
	defaultAdminRoleID = 1
)

type MenuRepo struct {
	db *gorm.DB
}

func NewMenuRepo(db *gorm.DB) *MenuRepo {
	return &MenuRepo{
		db: db,
	}
}

func (m *MenuRepo) Create(menu model.Menu) error {
	return m.db.Create(&menu).Error
}

func (m *MenuRepo) List(rid int) ([]model.Menu, error) {
	var (
		list    []model.Menu
		builder = m.db.Model(model.Menu{})
	)
	if rid != defaultAdminRoleID {
		builder = builder.Where("exists (select 1 from role_menus where role_id = ? and menu_id = id)", rid)
	}
	err := builder.Find(&list).Error
	return list, err
}

func (m *MenuRepo) Delete(id int) error {
	return m.db.Where("id = ?", id).Delete(&model.Menu{}).Error
}

func (m *MenuRepo) Detail(id int) (model.Menu, error) {
	var menu model.Menu
	err := m.db.Where("id = ?", id).First(&menu).Error
	return menu, err
}

func (m *MenuRepo) Update(menu model.Menu) error {
	return m.db.Updates(&menu).Error
}

func (m *MenuRepo) IDs(rid int) ([]int, error) {
	var (
		list []int
	)
	err := m.db.Model(model.Menu{}).Pluck("id", &list).Error
	return list, err
}

func (m *MenuRepo) ListByPID(pid int) ([]model.Menu, error) {
	var (
		list []model.Menu
	)
	err := m.db.Model(model.Menu{}).
		Where("parent_id = ?", pid).
		Select("id", "name").
		Find(&list).
		Error
	return list, err
}
