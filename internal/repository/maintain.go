package repository

import (
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"gorm.io/gorm"
)

type MaintainRepo struct {
	db *gorm.DB
}

func NewMaintainRepo(db *gorm.DB) *MaintainRepo {
	return &MaintainRepo{
		db: db,
	}
}

func (m *MaintainRepo) Create(maintain model.Maintain, plans []model.MaintainPlan) error {
	return m.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(model.Maintain{}).Create(&maintain).Error
		if err != nil {
			return err
		}

		for i := 0; i < len(plans); i++ {
			plans[i].MaintainID = maintain.ID
		}
		return tx.Model(model.MaintainPlan{}).Create(&plans).Error
	})
}

func (m *MaintainRepo) List(param request.MaintainList) ([]model.Maintain, int64, error) {
	var (
		list  []model.Maintain
		total int64
	)
	err := m.db.Model(model.Maintain{}).
		Count(&total).
		Limit(param.Size).
		Offset((param.Page - 1) * param.Size).
		Order("id desc").
		Find(&list).
		Error
	return list, total, err
}

func (m *MaintainRepo) PlanList(filter model.MaintainPlan) ([]response.MaintainPlanList, error) {
	var list []response.MaintainPlanList
	err := m.db.Model(model.MaintainPlan{}).
		Where(filter).
		Preload("ExecuteUser", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "nickname")
		}).
		Find(&list).
		Error
	return list, err
}

func (m *MaintainRepo) Execute(update model.MaintainPlan) error {
	return m.db.Model(model.MaintainPlan{}).
		Where("id = ?", update.ID).
		Updates(update).
		Error
}
