package repository

import (
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"gorm.io/gorm"
)

type InspectRepo struct {
	db *gorm.DB
}

func NewInspectRepo(db *gorm.DB) *InspectRepo {
	return &InspectRepo{
		db: db,
	}
}

func (i *InspectRepo) Create(param request.CreateInspect) error {
	var (
		inspect = model.Inspect{
			AreaID:        param.AreaID,
			Title:         param.Title,
			Content:       param.Content,
			Detail:        param.Detail,
			Comment:       param.Comment,
			Cycle:         param.Cycle,
			CreatedUserID: param.UID,
			CheckUserID:   param.CheckUserID,
		}
		plans      = make([]model.InspectPlan, 0, len(param.Plans))
		executeIds = make([]model.InspectExecuteUser, 0, len(param.ExecuteUserIDs))
		leadingIds = make([]model.InspectLeadingUser, 0, len(param.LeadingUserIDs))
	)
	return i.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(model.Inspect{}).
			Create(&inspect).
			Error
		if err != nil {
			return err
		}

		for _, id := range param.Plans {
			plans = append(plans, model.InspectPlan{
				InspectID: inspect.ID,
				Date:      id.Date,
				StartAt:   id.StartAt,
				EndAt:     id.EndAt,
				Status:    model.InspectPlanStatusUnfinished,
			})
		}

		err = tx.Model(model.InspectPlan{}).
			Create(&plans).
			Error
		if err != nil {
			return err
		}
		for _, id := range param.ExecuteUserIDs {
			executeIds = append(executeIds, model.InspectExecuteUser{
				InspectID: inspect.ID,
				UserID:    id,
			})
		}

		err = tx.Model(model.InspectExecuteUser{}).
			Create(&executeIds).
			Error
		if err != nil {
			return err
		}
		for _, id := range param.LeadingUserIDs {
			leadingIds = append(leadingIds, model.InspectLeadingUser{
				InspectID: inspect.ID,
				UserID:    id,
			})
		}

		return tx.Model(model.InspectLeadingUser{}).
			Create(&leadingIds).
			Error
	})
}

func (i *InspectRepo) List(param request.InspectList) ([]response.InspectList, int64, error) {
	var (
		list  []response.InspectList
		total int64
	)

	err := i.db.Model(model.Inspect{}).
		Count(&total).
		Limit(param.Size).
		Offset((param.Page-1)*param.Size).
		Order("id desc").
		Preload("AreaInfo", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "name")
		}).
		Preload("CreatedUserInfo", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "nickname")
		}).
		Preload("CheckUserInfo", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "nickname")
		}).
		Preload("ExecuteUsers", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "nickname")
		}).
		Preload("LeadingUsers", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "nickname")
		}).
		Find(&list).
		Error
	return list, total, err
}

func (i *InspectRepo) PlanList(param request.InspectPlanList) ([]response.InspectPlanList, error) {
	var (
		list    []response.InspectPlanList
		builder = i.db.Model(model.InspectPlan{}).Debug()
	)
	if param.InspectionID != 0 {
		builder = builder.Where("inspect_id = ?", param.InspectionID)
	}
	err := builder.
		Order("id desc").
		Preload("InspectInfo", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "title")
		}).
		Preload("ExecutedUserInfo", func(tx *gorm.DB) *gorm.DB {
			return tx.Select("id", "nickname")
		}).
		Preload("Images").
		Find(&list).
		Error
	return list, err
}

func (i *InspectRepo) TotalByUser(uid int) (int64, error) {
	var total int64
	err := i.db.Model(model.InspectExecuteUser{}).
		Where("user_id = ?", uid).
		Count(&total).
		Error
	return total, err
}

func (i *InspectRepo) Execute(plan model.InspectPlan, images []model.InspectPlanImage) error {
	return i.db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(model.InspectPlan{}).
			Where("id = ?", plan.ID).
			Updates(&plan).
			Error
		if err != nil {
			return err
		}

		return tx.Model(model.InspectPlanImage{}).
			Create(&images).
			Error
	})
}
