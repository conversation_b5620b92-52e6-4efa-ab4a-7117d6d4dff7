package controller

import (
	"encoding/json"
	"errors"
	"net/http"
	"time"
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/utils"

	mqtt "github.com/eclipse/paho.mqtt.golang"
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

type GatewayController struct {
	gatewayService *service.GatewayService
}

func NewGatewayController(gatewayService *service.GatewayService) *GatewayController {
	return &GatewayController{
		gatewayService: gatewayService,
	}
}

func (g *GatewayController) Create(c *gin.Context) {
	var param request.CreateGateway
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = g.gatewayService.Create(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (g *GatewayController) IsAccept(c *gin.Context) {
	var param request.IsAccept
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	response.OkWithData(c, response.IsAccept{
		Connected: utils.IsAccept(param.IP, param.Port),
	})
}

func (g *GatewayController) Update(c *gin.Context) {
	var param request.UpdateGateway
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = g.gatewayService.Update(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (g *GatewayController) RegisterPceM(fid string, m mqtt.Message) error {
	var param request.RegisterPceM
	err := json.Unmarshal(m.Payload(), &param)
	if err != nil {
		return err
	}

	param.FlashID = fid

	return g.gatewayService.RegisterPceM(param)
}

func (g *GatewayController) Register(fid string, m mqtt.Message) error {
	var param request.Register
	err := json.Unmarshal(m.Payload(), &param)
	if err != nil {
		return err
	}

	param.FID = fid

	return g.gatewayService.Register(param)
}

func (g *GatewayController) List(c *gin.Context) {
	var param request.GatewayList
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, total, err := g.gatewayService.List(param.Page, param.Size, param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithPage(c, list, total, param.Page, param.Size)
}

func (g *GatewayController) IsAcceptMulti(c *gin.Context) {
	var param request.IsAcceptMulti
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	result := g.gatewayService.IsAcceptMulti(param)

	response.OkWithData(c, result)
}

func (g *GatewayController) UpdatePort(c *gin.Context) {
	var param request.UpdateGatewayPort
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = g.gatewayService.UpdatePort(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (g *GatewayController) AddPort(c *gin.Context) {
	var param request.AddGatewayPort
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = g.gatewayService.AddPort(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (g *GatewayController) DeletePort(c *gin.Context) {
	var param request.DeleteGatewayPort
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = g.gatewayService.DeletePort(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (g *GatewayController) PortList(c *gin.Context) {
	var param request.GatewayPortList
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := g.gatewayService.PortList(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (g *GatewayController) Delete(c *gin.Context) {
	var param request.DeleteGateway
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = g.gatewayService.Delete(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (g *GatewayController) OPCNodeList(c *gin.Context) {
	var param request.GatewayOPCNodeList
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := g.gatewayService.OPCNodeList(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}
func (g *GatewayController) OPCNodeVariable(c *gin.Context) {
	var param request.GetOPCNodeVariable
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	data, err := g.gatewayService.OPCNodeVariable(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, data)
}

func (g *GatewayController) ModelList(c *gin.Context) {
	list, err := g.gatewayService.ModelList()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (g *GatewayController) Scan(c *gin.Context) {
	var param request.ScanGateway
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	resp, err := g.gatewayService.Scan(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, resp)
}

func (g *GatewayController) AdapterList(c *gin.Context) {
	list, err := g.gatewayService.AdapterList()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (g *GatewayController) AlarmBinding(c *gin.Context) {
	var param request.GatewayAlarmBinding
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = g.gatewayService.AlarmBinding(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (g *GatewayController) AlarmBindingList(c *gin.Context) {
	var param request.GetGatewayAlarmBinding
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := g.gatewayService.AlarmBindingList(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (g *GatewayController) CreateGatewayBatch(c *gin.Context) {
	var param []request.CreateGateway
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if len(param) == 0 {
		response.Ok(c)
		return
	}
	err = g.gatewayService.CreateGatewayBatch(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (g *GatewayController) Statistic(c *gin.Context) {
	var (
		upgrader  websocket.Upgrader
		tokenInfo request.TokenInfo
	)

	tokenInfo.ParseToken(c)

	upgrader.CheckOrigin = func(r *http.Request) bool {
		return true
	}

	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	defer conn.Close()
	type responseT struct {
		Code int         `json:"code"`
		Msg  string      `json:"msg"`
		Data interface{} `json:"data"`
	}

	var (
		ticket  = time.NewTicker(5 * time.Second)
		firstCh = make(chan struct{}, 1)
	)
	firstCh <- struct{}{}
	for {
		select {
		case <-ticket.C:
		case <-firstCh:
		}

		resp, err := g.gatewayService.Statistic(tokenInfo)
		if err != nil {
			conn.WriteJSON(&responseT{
				Code: 10,
				Msg:  err.Error(),
			})
			continue
		}
		conn.WriteJSON(&responseT{
			Code: 1,
			Msg:  "success",
			Data: resp,
		})
	}
}

func (g *GatewayController) Replace(fid string, msg mqtt.Message) error {
	var param request.ReplaceGateway
	err := json.Unmarshal(msg.Payload(), &param)
	if err != nil {
		return err
	}
	if param.FID == "" {
		return errors.New("sn is empty")
	}
	return g.gatewayService.Replace(param)
}

func (g *GatewayController) NTP(fid string, msg mqtt.Message) error {
	return g.gatewayService.NTP(fid)
}

func (g *GatewayController) Replace1(c *gin.Context) {
	var param request.ReplaceGateway1

	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	err = g.gatewayService.Replace1(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
