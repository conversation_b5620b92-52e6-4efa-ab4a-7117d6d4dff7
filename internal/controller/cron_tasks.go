package controller

import (
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type CronTaskController struct {
	service *service.CronTaskService
	l       *zap.Logger
}

func NewCronTaskController(us *service.CronTaskService, log *zap.Logger) *CronTaskController {
	return &CronTaskController{
		service: us,
		l:       log.Named("cronTask_controller:"),
	}
}
func (d *CronTaskController) Create(c *gin.Context) {
	var param request.CreateCronTask
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = d.service.Create(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (d *CronTaskController) GetCronTaskList(c *gin.Context) {
	list, err := d.service.GetCronTaskList()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}
func (d *CronTaskController) GetCronTaskDetail(c *gin.Context) {
	var param request.DeleteCronTask
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := d.service.GetCronTaskDetail(param.ID)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}
func (d *CronTaskController) GetCronTaskAll(c *gin.Context) {
	list, err := d.service.GetCronTaskAll()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}
func (dc *CronTaskController) Delete(c *gin.Context) {
	var param request.DeleteCronTask
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = dc.service.Delete(param.ID)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (d *CronTaskController) Update(c *gin.Context) {
	var param request.UpdateCronTask
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = d.service.Update(param.ID, param.CreateCronTask)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
