package controller

import (
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/utils"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type UserController struct {
	us   *service.UserService
	qywx *service.QywxService
	l    *zap.Logger
}

func NewUserController(
	us *service.UserService,
	log *zap.Logger,
	qywx *service.QywxService,
) *UserController {
	return &UserController{
		us:   us,
		qywx: qywx,
		l:    log.Named("user_controller:"),
	}
}

func (uc *UserController) Create(c *gin.Context) {
	var param request.CreateUser
	if err := c.ShouldBindJSON(&param); err != nil {
		response.FailWithError(c, err)
		return
	}

	if !utils.ValidatePhoneNum(param.Phone) {
		response.FailWithMsg(c, "手机号格式不正确")
		return
	}

	err := uc.us.Create(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (uc *UserController) Login(c *gin.Context) {
	var param request.Login
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	result, err := uc.us.Login(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, result)
}

func (uc *UserController) List(c *gin.Context) {
	var param request.UserList
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, total, err := uc.us.List(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithPage(c, list, total, param.Page, param.Size)
}

func (uc *UserController) SetArea(c *gin.Context) {
	var param request.SetUserArea
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = uc.us.SetArea(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (uc *UserController) GetArea(c *gin.Context) {
	var param request.GetUserArea
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)
	result, err := uc.us.GetArea(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, result)
}

func (uc *UserController) SetDepartment(c *gin.Context) {
	var param request.SetUserDepartment
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = uc.us.SetDepartment(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (uc *UserController) GetDepartment(c *gin.Context) {
	var param request.GetUserDepartment
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)
	result, err := uc.us.GetDepartment(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, result)
}

func (uc *UserController) SetBusinessType(c *gin.Context) {
	var param request.SetUserBusinessType
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = uc.us.SetBusinessType(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (uc *UserController) GetBusinessType(c *gin.Context) {
	var param request.GetUserBusinessType
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)
	result, err := uc.us.GetBusinessType(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, result)
}

func (uc *UserController) Update(c *gin.Context) {
	var param request.UpdateUser
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	if param.Phone != "" && !utils.ValidatePhoneNum(param.Phone) {
		response.FailWithMsg(c, "手机号格式不正确")
		return
	}

	err = uc.us.Update(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (uc *UserController) ChangePassword(c *gin.Context) {
	var param request.ChangePassword
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)
	err = uc.us.ChangePassword(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (uc *UserController) ResetPassword(c *gin.Context) {
	var param request.ResetPassword
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = uc.us.ResetPassword(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (uc *UserController) ListSimplify(c *gin.Context) {
	var param request.UserListSimplify
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	list, err := uc.us.ListSimplify(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
	return
}

func (uc *UserController) Delete(c *gin.Context) {
	var param request.DeleteUser
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = uc.us.Delete(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (uc *UserController) ListByID(c *gin.Context) {
	var param request.UserListByID
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := uc.us.ListByID(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (uc *UserController) ListByMenuID(c *gin.Context) {
	var param request.UserListByMenuID
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := uc.us.ListByMenuID(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (uc *UserController) QyFetch(c *gin.Context) {
	err := uc.qywx.QyFetch()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (uc *UserController) QyLogin(c *gin.Context) {
	var param request.QyLogin
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	login, err := uc.qywx.QyLogin(param.Code)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, login)
}

func (uc *UserController) SelfUpdate(c *gin.Context) {
	var param request.UserSelfUpdate
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)
	err = uc.us.SelfUpdate(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (uc *UserController) Info(c *gin.Context) {
	var param request.UserInfo
	param.ParseToken(c)
	info, err := uc.us.Info(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, info)
}

func (uc *UserController) ListWithoutPage(c *gin.Context) {
	var param request.UserListWithoutPage
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := uc.us.ListWithoutPage(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}
