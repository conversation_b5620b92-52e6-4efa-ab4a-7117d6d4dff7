package controller

import (
	"bufio"
	"bytes"
	"encoding/json"
	"errors"
	"io"
	"strconv"
	"strings"
	"tw_platform/internal/service"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type DoorController struct {
	us *service.DoorService
	rs *service.RegisterService
	l  *zap.Logger
}

const (
	DOOR_TYPE = "门禁设备数"
)

func NewDoorController(us *service.DoorService, rs *service.RegisterService, log *zap.Logger) *DoorController {
	return &DoorController{
		us: us,
		rs: rs,
		l:  log.Named("video_controller:"),
	}
}

func (d *DoorController) GetDoorList(c *gin.Context) {
	list, err := d.us.GetDoorList()
	if err != nil {
		response.FailWithError(c, err)
	}
	response.OkWithData(c, list)
}

func (d *DoorController) NewDoorList(c *gin.Context) {
	var (
		permDoorNum = 0
		AddDoorNum  = 0
	)

	req := request.NewDoorReq{}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithError(c, err)
		return
	}

	nowList, err := d.us.GetDoorList()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	AddDoorNum = len(nowList) + len(req.Devices)

	// permInfo, err := d.rs.GetTypeRegCnt(DOOR_TYPE)
	// if err != nil {
	// 	response.FailWithError(c, err)
	// 	return
	// }
	// if permInfo.Enable == false {
	// 	response.FailWithError(c, errors.New("门禁功能未能授权"))
	// 	return
	// }
	// permDoorNum = permInfo.Number
	_ = permDoorNum
	_ = AddDoorNum

	// if AddDoorNum > permDoorNum {
	// 	response.FailWithError(c, fmt.Errorf("%s超过授权数量,授权数量为:%d,添加后的数量为:%d", "门禁", permDoorNum, AddDoorNum))
	// 	return
	// }

	if err := d.us.NewDoorList(req); err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (d *DoorController) EditDoor(c *gin.Context) {
	req := request.EditDoorReq{}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	if err := d.us.EditDoor(req); err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (d *DoorController) CtrlDoor(c *gin.Context) {
	req := request.CtrDoorReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.Id)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if err := d.us.CtrlDoor(req, dinfo); err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)

}

func (d *DoorController) GetDoorStatus(c *gin.Context) {
	req := request.GetDoorStatusReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	status, err := d.us.GetDoorStatus(dinfo)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, response.GetDoorStatusRes{DoorStatus: status})

}

func (d *DoorController) GetDoorUserList(c *gin.Context) {
	req := request.GetDoorUserListReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := d.us.GetUserList(dinfo)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}
func (d *DoorController) NewDoorUser(c *gin.Context) {
	req := request.AddDoorUserReq{}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if err := d.us.AddUser(req, dinfo); err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (d *DoorController) EditDoorUser(c *gin.Context) {
	req := request.EditDoorUserReq{}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if err := d.us.EditUser(req, dinfo); err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (d *DoorController) DelDoorUser(c *gin.Context) {
	req := request.DelDoorUserReq{}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if err := d.us.DelUser(req, dinfo); err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (d *DoorController) GetCard(c *gin.Context) {
	req := request.GetCardReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.Id)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := d.us.GetCard(req, dinfo)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}
func (d *DoorController) AddCard(c *gin.Context) {
	req := request.AddCardReq{}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if err = d.us.AddCard(req, dinfo); err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (d *DoorController) DelCard(c *gin.Context) {
	req := request.DelCardReq{}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if err = d.us.DelCard(req, dinfo); err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (d *DoorController) GetFingerPrint(c *gin.Context) {
	req := request.GetFpDataReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := d.us.GetFPData(req, dinfo)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)

}
func (d *DoorController) AddFingerPrint(c *gin.Context) {
	req := request.AddFpDataReq{}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if err = d.us.AddFpData(&req, dinfo); err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (d *DoorController) GetFingerAddProcess(c *gin.Context) {
	req := request.GetFpDataProcessReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	res, err := d.us.GetFpProcess(dinfo)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, res)
}
func (d *DoorController) GetFingerDelProcess(c *gin.Context) {
	req := request.GetFpDataProcessReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	res, err := d.us.GetDelFpProcess(dinfo)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, res)

}
func (d *DoorController) DelFinger(c *gin.Context) {
	req := request.DelFingerReq{}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if err = d.us.DelFinger(&req, dinfo); err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (d *DoorController) GetFace(c *gin.Context) {
	req := request.GetFaceReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	data, err := d.us.GetFace(&req, dinfo)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, data)
}

func (d *DoorController) DelFace(c *gin.Context) {
	req := request.DelFaceReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if err = d.us.DelFace(&req, dinfo); err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (d *DoorController) UpFace(c *gin.Context) {
	doorstr := c.DefaultQuery("doorId", "-1")
	doorId, _ := strconv.Atoi(doorstr)
	dinfo, err := d.us.GetDoorInfo(doorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	file, fh, err := c.Request.FormFile("FaceImage")
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	ftype := fh.Header["Content-Type"][0]
	fname := fh.Filename

	//人脸数据
	var b bytes.Buffer
	fdata := bufio.NewWriter(&b)
	io.Copy(fdata, file)

	//获取人员编号
	uinfo := c.PostForm("FaceDataRecord")
	f := &request.WebCmdFafceInfo{}
	json.Unmarshal([]byte(uinfo), f)

	ap := model.HkAddFaceParam{}
	ap.ImageName = fname
	ap.ImageType = ftype

	d.us.AddFace(dinfo, f, b, ap)
	response.Ok(c)
}

func (d *DoorController) GetRecord(c *gin.Context) {
	c.Request.URL.RawQuery = strings.ReplaceAll(c.Request.URL.RawQuery, "+", "%2b")
	doorstr := c.DefaultQuery("doorId", "-1")
	postr := c.DefaultQuery("pos", "0")
	pos, _ := strconv.Atoi(postr)
	doorId, _ := strconv.Atoi(doorstr)
	dinfo, err := d.us.GetDoorInfo(doorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	startTime := c.DefaultQuery("startTime", "1")
	endTime := c.DefaultQuery("endTime", "0")
	list, err := d.us.GetEventRecord(dinfo, doorId, pos, startTime, endTime)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}
func (d *DoorController) StartSyncInfo(c *gin.Context) {
	req := request.StartSyncReq{}
	doorConf := []model.Door{}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithError(c, err)
		return
	}

	doorDevList, err := d.us.GetDoorList()
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	for i := range doorDevList {
		doorConf = append(doorConf, model.Door{
			Id:       int64(doorDevList[i].Device.ID),
			DoorInfo: doorDevList[i].Config,
		})
	}
	d.us.StartSync(req, doorConf)
	response.Ok(c)
}
func (d *DoorController) GetSyncProcess(c *gin.Context) {
	succ, ret, errinfo := d.us.GetSyncResult()
	if succ {
		response.OkWithData(c, ret)
	} else {
		response.FailWithError(c, errors.New(errinfo.Msg))
	}
}
