package controller

import (
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type MediaMtxController struct {
	me *service.MediaMtxService
	l  *zap.Logger
}

func NewMediaMtxController(me *service.MediaMtxService, log *zap.Logger) *MediaMtxController {
	return &MediaMtxController{
		me: me,
		l:  log.Named("mediamtx_controller:"),
	}
}
func (uc *MediaMtxController) GenerateMedia(c *gin.Context) {
	err := uc.me.GenerateMedia()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (uc *MediaMtxController) SetTurn(c *gin.Context) {
	var (
		param request.SetTurn
	)
	err := c.ShouldBindBodyWithJSON(&param)
	if err != nil {
		response.FailWithMsg(c, "failed")
		return
	}
	err = uc.me.SetTurn(param.Url, param.Name, param.Password)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
