package controller

import (
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
)

type DepartmentController struct {
	service *service.DepartmentService
}

func NewDepartmentController(service *service.DepartmentService) *DepartmentController {
	return &DepartmentController{
		service: service,
	}
}

func (d *DepartmentController) Create(c *gin.Context) {
	var param request.CreateDepartment
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = d.service.Create(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (d *DepartmentController) All(c *gin.Context) {
	list, err := d.service.All()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (d *DepartmentController) AllWithTypeCounts(c *gin.Context) {
	list, err := d.service.AllWithTypeCounts()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (d *DepartmentController) TreeOnly(c *gin.Context) {
	var param request.DepartmentTreeByUserID
	param.ParseToken(c)
	list, err := d.service.TreeOnly(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (d *DepartmentController) Tree(c *gin.Context) {
	var param request.DepartmentTreeByUserID
	param.ParseToken(c)
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	// todo 暂时给定默认值
	if param.BusinessTypeID == 0 {
		param.BusinessTypeID = 1
	}
	list, err := d.service.Tree(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (d *DepartmentController) Update(c *gin.Context) {
	var param request.UpdateDepartment
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = d.service.Update(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (d *DepartmentController) Delete(c *gin.Context) {
	var param request.DeleteDepartment
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = d.service.Delete(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
