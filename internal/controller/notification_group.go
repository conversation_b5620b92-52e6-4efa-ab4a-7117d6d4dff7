package controller

import (
	"encoding/json"
	"fmt"
	"time"
	"tw_platform/internal/service"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
	"github.com/go-resty/resty/v2"
	"go.uber.org/zap"
)

type NotifacationGroupController struct {
	service *service.NotificationGroupService
	l       *zap.Logger
}

func NewNotifacationGroupController(us *service.NotificationGroupService, log *zap.Logger) *NotifacationGroupController {
	return &NotifacationGroupController{
		service: us,
		l:       log.Named("group_controller:"),
	}
}
func (d *NotifacationGroupController) Create(c *gin.Context) {
	var param request.CreateNotificationGroup
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = d.service.Create(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (d *NotifacationGroupController) GetNotifacationGroupList(c *gin.Context) {
	list, err := d.service.GetNotificationGroupList()
	if err != nil {
		response.FailWithError(c, err)
	}
	response.OkWithData(c, list)
}
func (dc *NotifacationGroupController) Delete(c *gin.Context) {
	var param request.DeleteNotificationGroup
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = dc.service.Delete(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (d *NotifacationGroupController) Update(c *gin.Context) {
	var param request.UpdateNotificationGroup
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = d.service.Update(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (d *NotifacationGroupController) GetGprsConfig(c *gin.Context) {
	gprs, err := d.service.GetGprsConfig()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, gprs)
}
func (d *NotifacationGroupController) UpdateGprsConfig(c *gin.Context) {
	var param model.Gprs
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = d.service.UpdateGprsConfig(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (d *NotifacationGroupController) GetEmailConfig(c *gin.Context) {
	email, err := d.service.GetEmailConfig()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, email)
}
func (d *NotifacationGroupController) UpdateEmailConfig(c *gin.Context) {
	var param model.EmailClient
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = d.service.UpdateEmailConfig(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (d *NotifacationGroupController) Get4GDetail(c *gin.Context) {
	gprs, err := d.service.GetGprsConfig()
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	if !gprs.Gprsenable {
		response.OkWithData(c, nil)
		return
	}
	client := resty.New()
	client.SetTimeout(5 * time.Second)
	url := fmt.Sprintf("http://%s:80/cgi-bin/sysinfo", gprs.GprsIp)
	resp, err := client.R().
		Get(url)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if resp.StatusCode() != 200 {
		response.FailWithError(c, fmt.Errorf("failed to get 4g valid response: %s", resp.Status()))
		return
	}
	var jsonResponse interface{}
	if err := json.Unmarshal(resp.Body(), &jsonResponse); err != nil {
		response.OkWithData(c, resp.Body())
		return
	}
	response.OkWithData(c, jsonResponse)
}
func (d *NotifacationGroupController) Get4GYE(c *gin.Context) {
	gprs, err := d.service.GetGprsConfig()
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	if !gprs.Gprsenable {
		response.FailWithError(c, err)
		return
	}
	if !gprs.Gprsenable {
		response.OkWithData(c, nil)
		return
	}
	client := resty.New()
	client.SetTimeout(5 * time.Second)
	url := fmt.Sprintf("http://%s:80/cgi-bin/sysinfo", gprs.GprsIp)
	_, err = client.R().
		Get(url)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, nil)
}
