package controller

import (
	"errors"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

/*
门禁控制器
*/

func (d *DoorController) GetDoorControllerStatus(c *gin.Context) {
	req := request.GetDoorStatusReq{}
	if err := c.Should<PERSON>ind<PERSON>uery(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if dinfo.Type != "控制器" {
		response.FailWithError(c, errors.New("这不是控制器"))
		return
	}
	status, err := d.us.GetDoorControllerStatus(dinfo)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, status)

}
func (d *DoorController) CtrlDoorController(c *gin.Context) {
	req := request.CtrDoorControllerReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(*req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if dinfo.Type != "控制器" {
		response.FailWithError(c, errors.New("这不是控制器"))
		return
	}
	err = d.us.CtrlDoorDoorController(dinfo, req)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)

}
func (d *DoorController) GetDoorControllerCard(c *gin.Context) {
	req := request.DoorControllerCardReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if dinfo.Type != "控制器" {
		response.FailWithError(c, errors.New("这不是控制器"))
		return
	}
	status, err := d.us.GetDoorControllerCard(dinfo)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, status)

}
func (d *DoorController) AddDoorControllerCard(c *gin.Context) {
	req := request.DoorControllerCardReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	creq2 := &request.AddDoorControllerCardReq{}
	if err := c.ShouldBindJSON(creq2); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if dinfo.Type != "控制器" {
		response.FailWithError(c, errors.New("这不是控制器"))
		return
	}
	err = d.us.AddDoorControllerCard(dinfo, creq2.CardNo, creq2.Door)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)

}
func (d *DoorController) DelDoorControllerCard(c *gin.Context) {
	req := request.DoorControllerCardReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	creq2 := &request.DelDoorControllerCardReq{}
	if err := c.ShouldBindJSON(creq2); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if dinfo.Type != "控制器" {
		response.FailWithError(c, errors.New("这不是控制器"))
		return
	}
	err = d.us.DelDoorControllerCard(dinfo, creq2.CardNo)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)

}
func (d *DoorController) GetDoorControllerRecord(c *gin.Context) {
	req := request.GetDoorControllerLogReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(*req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if dinfo.Type != "控制器" {
		response.FailWithError(c, errors.New("这不是控制器"))
		return
	}
	record, err := d.us.GetDoorControllerRecord(dinfo, *req.StartTime, *req.EndTime)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, record)

}
func (d *DoorController) DoorControllerStartSyncInfo(c *gin.Context) {
	syncReq := &request.SyncReq{}
	err := c.ShouldBind(syncReq)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	go func() {
		err := d.us.DoorControllerdoSync(syncReq)
		if err != nil {
			d.l.Error("门禁控制器同步失败", zap.Error(err))
		}
	}()
}
func (d *DoorController) GetControllerSyncProcess(c *gin.Context) {
	succ, ret, errinfo := d.us.GetDoorControllerSyncResult()
	if succ {
		response.OkWithData(c, ret)
	} else {
		response.FailWithError(c, errors.New(errinfo.Msg))
	}
}
