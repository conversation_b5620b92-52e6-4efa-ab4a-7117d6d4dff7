package controller

import (
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"go.uber.org/zap"
	"net/http"
	"sync"
	"time"
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
)

type VideoController struct {
	us *service.VideoService
	l  *zap.Logger
}

func NewVideoController(us *service.VideoService, log *zap.Logger) *VideoController {
	return &VideoController{
		us: us,
		l:  log.Named("video_controller:"),
	}
}

func (uc *VideoController) GetVideoList(c *gin.Context) {
	list, err := uc.us.GetVideoList()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
	return
}

func (uc *VideoController) SetVideoList(c *gin.Context) {
	var param []request.CreateVideo
	err := c.ShouldBindBodyWithJSON(&param)
	if err != nil {
		response.FailWithMsg(c, "failed")
		return
	}
	err = uc.us.SetVideoList(param)
	if err != nil {
		uc.l.Error("failed to set video list", zap.Error(err))
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (uc *VideoController) ModifyVideo(c *gin.Context) {
	var param request.ModifyVideo
	err := c.ShouldBindBodyWithJSON(&param)
	if err != nil {
		response.FailWithMsg(c, "failed")
		return
	}
	err = uc.us.ModifyVideo(param)
	if err != nil {
		//uc.l.Error("failed to set video list", zap.Error(err))
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (uc *VideoController) PlayVideo(c *gin.Context) {
	var param = request.RealTimePlayRequest{}
	var responseData = response.PlayVideoResponse{}
	if err := c.ShouldBindJSON(&param); err != nil {
		response.FailWithError(c, err)
		return
	}

	url, processCh, err := uc.us.PlayVideo(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	responseData.Url = url
	responseData.ProcessCh = processCh
	response.OkWithData(c, responseData)
}
func (uc *VideoController) PlaybackVideo(c *gin.Context) {
	var param = request.PlaybackRequest{}
	var responseData = response.PlayVideoResponse{}
	if err := c.ShouldBindJSON(&param); err != nil {
		response.FailWithError(c, err)
		return
	}
	url, processCh, err := uc.us.PlaybackVideo(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	responseData.Url = url
	responseData.ProcessCh = processCh
	response.OkWithData(c, responseData)
}
func (uc *VideoController) PreviewVideo(c *gin.Context) {
	var param = request.PreviewVideoRequest{}
	var responseData = response.PlayVideoResponse{}
	if err := c.ShouldBindJSON(&param); err != nil {
		response.FailWithError(c, err)
		return
	}

	url, processCh, err := uc.us.PreviewVideo(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	responseData.Url = url
	responseData.ProcessCh = processCh
	response.OkWithData(c, responseData)
}
func (uc *VideoController) StopPlayingFFMPEG(c *gin.Context) {
	var param = request.VideoProcessChannel{}
	if err := c.ShouldBindJSON(&param); err != nil {
		response.FailWithError(c, err)
		return
	}
	uc.us.StopPlayingFFMPEG(param)
	response.Ok(c)
}
func (uc *VideoController) KeepAliveFFMPEG(c *gin.Context) {
	var param = request.VideoProcessChannel{}
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	uc.us.KeepAliveFFMPEG(param)
	response.Ok(c)
}

var SSEMap sync.Map

func (uc *VideoController) CaptureImage(c *gin.Context) {
	var (
		upgrade websocket.Upgrader
		param   request.CaptureImage
		isFirst = true
		paramCh = make(chan request.CaptureImage)
	)
	upgrade.CheckOrigin = func(r *http.Request) bool {
		return true
	}

	type resp struct {
		Code int         `json:"code"`
		Msg  string      `json:"msg"`
		Data interface{} `json:"data"`
	}
	uc.l.Info("start capture image")
	conn, err := upgrade.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	defer conn.Close()
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	go func() {
		var temp request.CaptureImage
		for {
			err := conn.ReadJSON(&temp)
			if err != nil {
				conn.Close()
				return
			}

			paramCh <- temp
			uc.l.Info("receive capture image request", zap.Any("param", temp))
			ticker.Reset(1 * time.Second)
			isFirst = false
		}
	}()

	for {
		select {
		case temp := <-paramCh:
			param = temp
		case <-ticker.C:
			if isFirst {
				conn.Close()
			}
		}

		results, err := uc.us.CaptureImage(param.ID)
		if err != nil {
			conn.WriteJSON(resp{
				Code: 10,
				Msg:  err.Error(),
			})
			continue
		}
		err = conn.WriteJSON(resp{
			Code: 1,
			Msg:  "success",
			Data: results,
		})
		if err != nil {
			break
		}

	}

}
