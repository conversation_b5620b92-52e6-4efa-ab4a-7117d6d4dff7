package controller

import (
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
)

type LogController struct {
	service *service.LogService
}

func NewLogController(service *service.LogService) *LogController {
	return &LogController{service: service}
}

func (l *LogController) OperationList(c *gin.Context) {
	var param request.OperationList

	if err := c.ShouldBindQuery(&param); err != nil {
		response.FailWithError(c, err)
		return
	}
	list, total, err := l.service.OperationList(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithPage(c, list, total, param.Page, param.Size)
}

func (l *LogController) AlarmList(c *gin.Context) {
	var param request.AlarmLogList
	if err := c.ShouldBindQuery(&param); err != nil {
		response.FailWithError(c, err)
		return
	}
	list, total, err := l.service.AlarmList(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithPage(c, list, total, param.Page, param.Size)
}

func (l *LogController) Usage(c *gin.Context) {
	var param request.TableUseage
	if err := c.ShouldBindQuery(&param); err != nil {
		response.FailWithError(c, err)
		return
	}
	usage, err := l.service.Usage(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, usage)
}

func (l *LogController) Truncate(c *gin.Context) {
	var param request.TableTruncate
	if err := c.ShouldBindJSON(&param); err != nil {
		response.FailWithError(c, err)
		return
	}
	err := l.service.Truncate(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
