package controller

import (
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
)

type RoleController struct {
	rs *service.RoleService
}

func NewRoleController(rs *service.RoleService) *RoleController {
	return &RoleController{
		rs: rs,
	}
}

func (r *RoleController) Create(c *gin.Context) {
	var param request.CreateRole
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = r.rs.Create(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (r *RoleController) List(c *gin.Context) {
	var param request.RoleList
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, total, err := r.rs.List(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithPage(c, list, total, param.Page, param.Size)
}

func (r *RoleController) Update(c *gin.Context) {
	var param request.UpdateRole
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = r.rs.Update(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (r *RoleController) Delete(c *gin.Context) {
	var param request.DeleteRole
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = r.rs.Delete(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (r *RoleController) SetMenus(c *gin.Context) {
	var param request.SetmenusToRole
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = r.rs.SetMenus(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (r *RoleController) GetMenus(c *gin.Context) {
	var param request.GetMenusByRole
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)

	ids, err := r.rs.GetMenus(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, ids)
}

func (r *RoleController) AllocableList(c *gin.Context) {
	var param request.AllocableList
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)
	list, err := r.rs.AllocableList(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (r *RoleController) Permissions(c *gin.Context) {
	var param request.GetPermissionsByRole
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)
	list, err := r.rs.Permissions(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (r *RoleController) SetPermissions(c *gin.Context) {
	var param request.SetPermissionsToRole
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = r.rs.SetPermissions(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
