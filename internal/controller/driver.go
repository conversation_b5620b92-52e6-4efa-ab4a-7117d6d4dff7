package controller

import (
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type DriverController struct {
	ds *service.DriverService
	l  *zap.Logger
}

func NewDriverController(ds *service.DriverService, l *zap.Logger) *DriverController {
	return &DriverController{
		ds: ds,
		l:  l,
	}
}

func (dc *DriverController) Create(c *gin.Context) {
	var param request.CreateDriver
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = dc.ds.Create(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (dc *DriverController) List(c *gin.Context) {
	var param request.DriverList
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, total, err := dc.ds.List(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithPage(c, list, total, param.Page, param.Size)
}

func (dc *DriverController) Detail(c *gin.Context) {
	var param request.FindByID
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	d, err := dc.ds.Detail(param.ID)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, d)

}

func (dc *DriverController) Delete(c *gin.Context) {
	var param request.FindByID
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	err = dc.ds.Delete(param.ID)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (dc *DriverController) TypeList(c *gin.Context) {
	list, err := dc.ds.TypeList()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (dc *DriverController) ManuFacturerList(c *gin.Context) {
	list, err := dc.ds.ManuFacturerList()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (dc *DriverController) AgreeList(c *gin.Context) {
	list, err := dc.ds.AgreeList()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}
