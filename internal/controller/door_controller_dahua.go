package controller

import (
	"errors"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
)

/*
大华门禁控制器
使用二代协议 cgi接口
*/
func (d *DoorController) GetDoorControlleStatus_Dahua(c *gin.Context) {
	req := request.GetDoorStatusReq{}
	if err := c.ShouldBind<PERSON>uery(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if dinfo.Type != "控制器" || dinfo.ManuFacturer != "大华" {
		response.FailWithError(c, errors.New("这不是大华控制器"))
		return
	}
	status, err := d.us.GetDoorControllerStatus_Dahua(dinfo)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, status)

}
func (d *DoorController) CtrlDoorController_Dahua(c *gin.Context) {
	req := request.CtrDoorControllerReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(*req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if dinfo.Type != "控制器" || dinfo.ManuFacturer != "大华" {
		response.FailWithError(c, errors.New("这不是大华控制器"))
		return
	}
	err = d.us.CtrlDoorDoorController_Dahua(dinfo, req)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)

}
func (d *DoorController) InsertUser_Dahua(c *gin.Context) {
	req := request.GetDoorStatusReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if dinfo.Type != "控制器" || dinfo.ManuFacturer != "大华" {
		response.FailWithError(c, errors.New("这不是大华控制器"))
		return
	}
	reqbody := request.InserUserDahuaReq{}
	if err := c.ShouldBind(&reqbody); err != nil {
		response.FailWithError(c, err)
		return
	}
	err = d.us.InsertUser_Dahua(dinfo, reqbody)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (d *DoorController) AccessUser_Dahua(c *gin.Context) {
	req := request.GetDoorStatusReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if dinfo.Type != "控制器" || dinfo.ManuFacturer != "大华" {
		response.FailWithError(c, errors.New("这不是大华控制器"))
		return
	}
	info, err := d.us.AccessUser_Dahua(dinfo)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, info)

}
func (d *DoorController) RemoveUser_Dahua(c *gin.Context) {
	req := request.GetCardDahuaReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if dinfo.Type != "控制器" || dinfo.ManuFacturer != "大华" {
		response.FailWithError(c, errors.New("这不是大华控制器"))
		return
	}
	err = d.us.RemoveUser_Dahua(dinfo, req)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (d *DoorController) InsertCard_Dahua(c *gin.Context) {
	req := request.GetDoorStatusReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if dinfo.Type != "控制器" || dinfo.ManuFacturer != "大华" {
		response.FailWithError(c, errors.New("这不是大华控制器"))
		return
	}
	reqbody := request.InserCardDahuaReq{}
	if err := c.ShouldBind(&reqbody); err != nil {
		response.FailWithError(c, err)
		return
	}
	err = d.us.InsertCard_Dahua(dinfo, reqbody)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (d *DoorController) AccessCard_Dahua(c *gin.Context) {
	req := request.GetCardDahuaReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if dinfo.Type != "控制器" || dinfo.ManuFacturer != "大华" {
		response.FailWithError(c, errors.New("这不是大华控制器"))
		return
	}
	info, err := d.us.AccessCard_Dahua(dinfo, req.UserID)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, info)

}
func (d *DoorController) RemoveCard_Dahua(c *gin.Context) {
	req := request.RemoveCardDahuaReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if dinfo.Type != "控制器" || dinfo.ManuFacturer != "大华" {
		response.FailWithError(c, errors.New("这不是大华控制器"))
		return
	}
	err = d.us.RemoveCard_Dahua(dinfo, req)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (d *DoorController) GetRecord_Dahua(c *gin.Context) {
	req := request.GetRecordDahuaReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	dinfo, err := d.us.GetDoorInfo(req.DoorId)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if dinfo.Type != "控制器" || dinfo.ManuFacturer != "大华" {
		response.FailWithError(c, errors.New("这不是大华控制器"))
		return
	}
	data, err := d.us.GetRecord_Dahua(dinfo, req)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, data)
}
