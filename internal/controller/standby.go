package controller

import (
	"net/http"
	"strconv"
	"strings"
	"time"
	"tw_platform/internal/service"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

type StandbyController struct {
	standbyService *service.StandbyService
}

func NewStandbyController(standbyService *service.StandbyService) *StandbyController {
	return &StandbyController{
		standbyService: standbyService,
	}
}

func (s *StandbyController) SyncTables(c *gin.Context) {
	var param request.StandbySyncTables
	if err := c.ShouldBind(&param); err != nil {
		response.FailWithError(c, err)
		return
	}
	var now = time.Now().Unix()
	if param.Timestamp < now-5*60 || param.Timestamp > now+5*60 {
		response.FailWithMsg(c, "两机时间相差超过五分钟")
		return
	}
	tables, err := s.standbyService.SyncTables()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, tables)
}

func (s *StandbyController) Sync(c *gin.Context) {
	err := s.standbyService.Sync()
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	response.Ok(c)
}
func (s *StandbyController) FetchPush(c *gin.Context) {
	var param request.FetchTables
	err := c.ShouldBind(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	t, ok := c.GetQuery("timestamp")
	if !ok {
		response.FailWithMsg(c, "timestamp field is required")
		return
	}

	timestamp, err := strconv.ParseInt(t, 10, 64)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	if time.Now().Unix()-timestamp > 5*60 {
		response.FailWithMsg(c, "两机相差时间大于五分钟")
		return
	}

	if nameArr := strings.Split(param.Tables.Filename, "."); len(nameArr) != 2 || nameArr[1] != "sql" {
		response.FailWithMsg(c, "文件名格式错误")
		return
	}

	failed := s.standbyService.FetchPush(param)
	response.OkWithData(c, failed)
}

func (s *StandbyController) PushTables(c *gin.Context) {
	err := s.standbyService.PushTables()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (s *StandbyController) Stats(c *gin.Context) {
	var (
		upgrade websocket.Upgrader
		ticker  = time.NewTicker(5 * time.Second)
		enabled bool
		role    model.StandbyStatus
	)

	upgrade.CheckOrigin = func(r *http.Request) bool {
		return true
	}

	conn, err := upgrade.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	defer conn.Close()

	type data struct {
		Enabled bool                `json:"enabled"`
		Type    model.StandbyStatus `json:"type"`
	}
	type resp struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data data   `json:"data"`
	}

	for {
		enabled, role = s.standbyService.Stats()

		err = conn.WriteJSON(resp{
			Code: 1,
			Msg:  "success",
			Data: data{
				Enabled: enabled,
				Type:    role,
			},
		})
		if err != nil {
			break
		}
		_ = <-ticker.C
	}
}

func (s *StandbyController) SetStandby(c *gin.Context) {
	var param request.SetStandby
	if err := c.ShouldBindJSON(&param); err != nil {
		response.FailWithError(c, err)
		return
	}
	err := s.standbyService.SetStandby(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (s *StandbyController) GetStandby(c *gin.Context) {
	standby, err := s.standbyService.GetStandby()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, standby)
}

func (s *StandbyController) DeviceData(c *gin.Context) {
	var param request.StandbyDataReq
	if err := c.ShouldBindQuery(&param); err != nil {
		response.FailWithError(c, err)
		return
	}

	data, err := s.standbyService.DeviceData(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, data)
}
