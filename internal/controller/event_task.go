package controller

import (
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type EventTaskController struct {
	service *service.EventTaskService
	l       *zap.Logger
}

func NewEventTaskController(us *service.EventTaskService, log *zap.Logger) *EventTaskController {
	return &EventTaskController{
		service: us,
		l:       log.Named("eventTask_controller:"),
	}
}
func (d *EventTaskController) Create(c *gin.Context) {
	var param request.CreateEventTask
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = d.service.Create(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (d *EventTaskController) GetEventTaskList(c *gin.Context) {
	list, err := d.service.GetEventTaskList()
	if err != nil {
		response.FailWithError(c, err)
	}
	response.OkWithData(c, list)
}
func (d *EventTaskController) GetEventTaskDetail(c *gin.Context) {
	var param request.DeleteEventTask
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := d.service.GetEventTaskDetail(param.ID)
	if err != nil {
		response.FailWithError(c, err)
	}
	response.OkWithData(c, list)
}
func (dc *EventTaskController) Delete(c *gin.Context) {
	var param request.DeleteCronTask
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = dc.service.Delete(param.ID)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (d *EventTaskController) Update(c *gin.Context) {
	var param request.UpdateEventTask
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = d.service.Update(param.ID, param.CreateEventTask)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
