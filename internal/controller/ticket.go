package controller

import (
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
)

type TicketController struct {
	TicketService *service.TicketService
}

func NewTicketController(ticketService *service.TicketService) *TicketController {
	return &TicketController{
		TicketService: ticketService,
	}
}

func (t *TicketController) Create(c *gin.Context) {
	var param request.CreateTicket
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)
	err = t.TicketService.Create(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (t *TicketController) List(c *gin.Context) {
	var param request.TicketList
	err := c.ShouldBind<PERSON>uery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)
	list, total, err := t.TicketService.List(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithPage(c, list, total, param.Page, param.Size)
}

func (t *TicketController) Timeline(c *gin.Context) {
	var param request.TicketTimeline
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	list, err := t.TicketService.Timeline(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (t *TicketController) Update(c *gin.Context) {
	var param request.UpdateTicket
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)
	err = t.TicketService.Update(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (t *TicketController) Detail(c *gin.Context) {
	var param request.TicketDetail
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	detail, err := t.TicketService.Detail(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, detail)
}

func (t *TicketController) UpdateAsset(c *gin.Context) {
	var param request.UpdateTicketAsset
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)
	err = t.TicketService.UpdateAsset(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
