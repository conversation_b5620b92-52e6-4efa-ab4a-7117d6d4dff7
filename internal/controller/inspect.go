package controller

import (
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
)

type InspectController struct {
	service *service.InspectService
}

func NewInspectController(inspectService *service.InspectService) *InspectController {
	return &InspectController{
		service: inspectService,
	}
}

func (ic *InspectController) Create(c *gin.Context) {
	var param request.CreateInspect
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)

	err = ic.service.Create(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (ic *InspectController) List(c *gin.Context) {
	var param request.InspectList
	err := c.ShouldBind<PERSON>uery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, total, err := ic.service.List(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithPage(c, list, total, param.Page, param.Size)
}

func (ic *InspectController) PlanList(c *gin.Context) {
	var param request.InspectPlanList
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := ic.service.PlanList(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (ic *InspectController) Execute(c *gin.Context) {
	var param request.ExecuteInspectPlan
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)
	err = ic.service.Execute(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (ic *InspectController) Upload(c *gin.Context) {
	var param request.InspectionUpload
	err := c.ShouldBind(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if param.File.Size > 1024*1024*30 {
		response.FailWithMsg(c, "文件大小不能超过30M")
		return
	}
	path, err := ic.service.Upload(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	var data = struct {
		Path string `json:"path"`
	}{
		Path: path,
	}
	response.OkWithData(c, data)
}

func (ic *InspectController) Delete(c *gin.Context) {
	var param request.InspectionDeleteFile
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = ic.service.Delete(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
