package controller

import (
	"io"
	"strings"
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type RegisterController struct {
	us *service.RegisterService
	l  *zap.Logger
}

func NewRegisterController(us *service.RegisterService, log *zap.Logger) *RegisterController {
	return &RegisterController{us: us, l: log}
}

func (r *RegisterController) GetRegisterInfo(c *gin.Context) {
	info, err := r.us.GetRegisterInfo()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, &response.RegisterRes{
		UniqueCode: info.RegCode,
		RegCode:    info.RegInfo,
		Version:    "",
	})
}

func (r *RegisterController) PostRegister(c *gin.Context) {
	req := request.RegisterReq{}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	if err := r.us.SetRegInfo(req.RegisteCode); err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (r *RegisterController) SetFuncPermission(c *gin.Context) {
	var req request.SetFunctionReq
	if err := c.ShouldBind(&req); err != nil {
		response.FailWithError(c, err)
		return
	}

	if req.File == nil {
		if err := r.us.SetFunction(req.Function); err != nil {
			response.FailWithError(c, err)
			return
		}
		response.Ok(c)
		return
	}

	if req.File.Size > 1024*1024*1 {
		response.FailWithMsg(c, "文件大小不能超过1M")
		return
	}

	fd, err := req.File.Open()
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	defer fd.Close()

	var sb strings.Builder
	data, err := io.ReadAll(fd)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	_, err = sb.Write(data)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if err := r.us.SetFunction(sb.String()); err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)

}

func (r *RegisterController) GetFunCode(c *gin.Context) {
	code, err := r.us.GetFuncCode()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, gin.H{"functioncode": code})
}

func (r *RegisterController) GetFuncPermissionInfo(c *gin.Context) {
	items, err := r.us.GetFuncPermission()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, items)
}

func (r *RegisterController) Get3DPermission(c *gin.Context) {
	ret, err := r.us.Get3dPermission()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, gin.H{"threereg": ret})
}
