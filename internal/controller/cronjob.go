package controller

import (
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
)

type CronjobController struct {
	service *service.CronjobService
}

func NewCronjobController(cs *service.CronjobService) *CronjobController {
	return &CronjobController{
		service: cs,
	}
}

func (cc *CronjobController) Create(c *gin.Context) {
	var param request.CreateCronjob
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = cc.service.Create(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (cc *CronjobController) Update(c *gin.Context) {
	var param request.UpdateCronjob
	err := c.ShouldBind<PERSON>(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = cc.service.Update(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (cc *CronjobController) List(c *gin.Context) {
	var param request.CronjobList
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, total, err := cc.service.List(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithPage(c, list, total, param.Page, param.Size)
}

func (cc *CronjobController) Delete(c *gin.Context) {
	var param request.CronjobDelete
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = cc.service.Delete(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
