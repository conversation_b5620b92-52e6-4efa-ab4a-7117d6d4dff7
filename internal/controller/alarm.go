package controller

import (
	"net/http"
	"time"
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

type AlarmController struct {
	AlarmService *service.AlarmService
}

func NewAlarmController(alarmRepo *service.AlarmService) *AlarmController {
	return &AlarmController{
		AlarmService: alarmRepo,
	}
}

func (ac *AlarmController) HistoryList(c *gin.Context) {
	var param request.HistoryAlarmList
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	param.ParseToken(c)

	list, total, err := ac.AlarmService.HistoryList(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	response.OkWithPage(c, list, total, param.Page, param.Size)
}

func (ac *AlarmController) ExportHistoryList(c *gin.Context) {
	var param request.ExportHistoryAlarm
	err := c.Should<PERSON>ind<PERSON>uery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	param.ParseToken(c)

	if param.DateType == 0 && param.Begin == "" && param.End == "" {
		response.FailWithMsg(c, "请选择时间范围！")
		return
	}

	list, err := ac.AlarmService.ExportHistoryList(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (ac *AlarmController) RealtimeAlarmList(c *gin.Context) {
	var param request.RealtimeAlarmList
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)

	list, total, err := ac.AlarmService.RealtimeAlarmList(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithPage(c, list, total, param.Page, param.Size)
}

func (ac *AlarmController) Handle(c *gin.Context) {
	var param request.HandleAlarm
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = ac.AlarmService.Handle(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (ac *AlarmController) SetStatus(c *gin.Context) {
	var param request.SetAlarmStatus
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = ac.AlarmService.SetAlarmStatus(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (ac *AlarmController) Sync(c *gin.Context) {
	var param request.SyncAlarm
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = ac.AlarmService.Sync(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (ac *AlarmController) TotalOfTime(c *gin.Context) {
	var (
		param    request.AlarmTotalOfTime
		upgrader websocket.Upgrader
	)
	param.ParseToken(c)

	upgrader.CheckOrigin = func(r *http.Request) bool {
		return true
	}

	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	defer conn.Close()
	type responseT struct {
		Code int         `json:"code"`
		Msg  string      `json:"msg"`
		Data interface{} `json:"data"`
	}

	var (
		paramCh = make(chan request.AlarmTotalOfTime, 1)
		ticket  = time.NewTicker(10 * time.Minute)
	)

	go func() {
		var temp request.AlarmTotalOfTime
		for {
			err := conn.ReadJSON(&temp)
			if err != nil {
				conn.Close()
				return
			}
			switch {
			case temp.Type == request.AlarmTotalOfDay:
				ticket = time.NewTicker(24 * time.Hour)
			case temp.Type == request.AlarmTotalOfHour:
				ticket = time.NewTicker(time.Hour)
			case temp.Type == request.AlarmTotalOfMinute:
				ticket = time.NewTicker(time.Minute)
			default:
				conn.WriteJSON(responseT{
					Code: 10,
					Msg:  "参数错误",
				})
				conn.Close()
				return
			}
			param.AreaID = temp.AreaID
			param.Type = temp.Type
			paramCh <- param
		}
	}()

	for {
		select {
		case param = <-paramCh:
		case <-ticket.C:
		}
		if param.Type == 0 {
			continue
		}
		data, err := ac.AlarmService.TotalOfTime(param)
		if err != nil {
			conn.WriteJSON(responseT{
				Code: 10,
				Msg:  err.Error(),
			})
			continue
		}

		err = conn.WriteJSON(responseT{
			Code: 1,
			Msg:  "success",
			Data: data,
		})
		if err != nil {
			break
		}
	}
}

func (ac *AlarmController) RealtimeListWS(c *gin.Context) {
	var (
		param     request.RealtimeAlarmList
		upgrader  websocket.Upgrader
		isFirst   bool
		tokenInfo request.TokenInfo
	)

	tokenInfo.ParseToken(c)
	upgrader.CheckOrigin = func(r *http.Request) bool {
		return true
	}

	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	defer conn.Close()

	type responseT struct {
		Code int         `json:"code"`
		Msg  string      `json:"msg"`
		Data interface{} `json:"data"`
	}

	var (
		paramCh = make(chan request.RealtimeAlarmList, 1)
		ticket  = time.NewTicker(time.Minute)
	)

	go func() {
		var temp request.RealtimeAlarmList
		for {
			err := conn.ReadJSON(&temp)
			if err != nil {
				conn.Close()
				return
			}

			isFirst = true
			paramCh <- temp
		}
	}()

	for {
		select {
		case param = <-paramCh:
		case <-ticket.C:
		}
		if !isFirst {
			conn.Close()
			return
		}

		param.TokenInfo = tokenInfo

		list, err := ac.AlarmService.RealtimeListWS(param)
		if err != nil {
			conn.WriteJSON(responseT{
				Code: 10,
				Msg:  err.Error(),
			})
			continue
		}

		err = conn.WriteJSON(responseT{
			Code: 1,
			Msg:  "success",
			Data: list,
		})

		if err != nil {
			conn.WriteJSON(responseT{
				Code: 10,
				Msg:  err.Error(),
			})
			break
		}
	}
}

func (ac *AlarmController) ExportList(c *gin.Context) {
	var param request.PageInfo
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, total, err := ac.AlarmService.ExportList(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithPage(c, list, total, param.Page, param.Size)
}

func (ac *AlarmController) ApplyPublish(c *gin.Context) {
	var param response.AlarmListPayload
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = ac.AlarmService.ApplyPublish(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (ac *AlarmController) PushToClient(c *gin.Context) {
	var (
		param    request.AlarmPushToClient
		upgrader websocket.Upgrader
	)

	upgrader.CheckOrigin = func(r *http.Request) bool {
		return true
	}

	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	defer conn.Close()

	param.ParseToken(c)

	var (
		addr = conn.RemoteAddr().String()
		resp struct {
			Code int    `json:"code"`
			Msg  string `json:"msg"`
			Data string `json:"data"`
		}
	)
	ch, err := ac.AlarmService.AddPushClient(param.UID, addr)
	if err != nil {
		resp.Code = 10
		resp.Msg = err.Error()
		conn.WriteJSON(resp)
		return
	}

	defer ac.AlarmService.DeletePushClient(addr)

	var (
		interval = time.NewTicker(30 * time.Second)
		timeout  = time.Minute
		lastPong = time.Now()
	)

	conn.SetPongHandler(func(string) error {
		lastPong = time.Now()
		return nil
	})

	go func() {
		for {
			_ = <-interval.C

			if time.Since(lastPong) > timeout {
				conn.Close()
				return
			}

			err = conn.WriteControl(websocket.PingMessage, []byte{}, time.Now().Add(time.Second))
			if err != nil {
				conn.Close()
				return
			}
		}
	}()

	go func() {
		for {
			_, _, err = conn.ReadMessage()
			if err != nil {
				return
			}
		}
	}()

	for {
		msg, ok := <-ch
		if !ok {
			return
		}
		resp.Code = 1
		resp.Msg = "success"
		resp.Data = msg

		err = conn.WriteJSON(resp)
		if err != nil {
			return
		}
	}
}

func (ac *AlarmController) Remove(c *gin.Context) {
	var param request.AlarmRemove
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = ac.AlarmService.Remove(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (ac *AlarmController) Create(c *gin.Context) {
	var param request.CreateAlarmOutside
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = ac.AlarmService.CreateOutside(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (ac *AlarmController) Recovery(c *gin.Context) {
	var param request.RecoveryAlarmOutside
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = ac.AlarmService.RecoveryOutside(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
