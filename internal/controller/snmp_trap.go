package controller

import (
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type SnmpTrapController struct {
	service *service.SnmpTrapService
	l       *zap.Logger
}

func NewSnmpTrapController(us *service.SnmpTrapService, log *zap.Logger) *SnmpTrapController {
	return &SnmpTrapController{
		service: us,
		l:       log.Named("snmp_controller:"),
	}
}
func (d *SnmpTrapController) Create(c *gin.Context) {
	var param request.CreateSnmpTrap
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = d.service.Create(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (d *SnmpTrapController) GetSnmpTrapList(c *gin.Context) {
	list, err := d.service.GetSnmpTrapList()
	if err != nil {
		response.FailWithError(c, err)
	}
	response.OkWithData(c, list)
}
func (dc *SnmpTrapController) Delete(c *gin.Context) {
	var param request.DeleteSnmpTrap
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = dc.service.Delete(param.ID)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (dc *SnmpTrapController) Update(c *gin.Context) {
	var param request.UpdateSnmpTrap
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = dc.service.Update(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
