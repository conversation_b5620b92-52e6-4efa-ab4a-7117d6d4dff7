package controller

import (
	"encoding/json"
	"net/http"
	"sort"
	"time"
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	mqtt "github.com/eclipse/paho.mqtt.golang"
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

type DeviceController struct {
	deviceService *service.DeviceService
}

func NewDeviceController(deviceService *service.DeviceService) *DeviceController {
	return &DeviceController{
		deviceService: deviceService,
	}
}

func (dc *DeviceController) CreatePceMDevice(c *gin.Context) {
	var param request.CreatePceMDevice
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if param.DriverID == 0 && param.Type == "" {
		response.FailWithMsg(c, "驱动和类型不能同时为空")
		return
	}

	err = dc.deviceService.CreatePceMDevice(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (dc *DeviceController) CreateDevice(c *gin.Context) {
	var param request.CreateDevice
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = dc.deviceService.CreateDevice(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (dc *DeviceController) CreateCustomDevice(c *gin.Context) {
	var param request.CreateCustomDevice
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	for i := 0; i < len(param.Devices); i++ {
		sort.Sort(param.Devices[i].Config.Unit)
	}

	err = dc.deviceService.CreateCustomDevice(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (dc *DeviceController) CreateOpcDevice(c *gin.Context) {
	var param request.CreateOpcDevice
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = dc.deviceService.CreateOpcDevice(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (dc *DeviceController) BatchCreateCustomDevice(c *gin.Context) {
	var param request.CreateCustomDevice
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	ids, err := dc.deviceService.BatchCreateCustomDevice(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, ids)
}
func (dc *DeviceController) Delete(c *gin.Context) {
	var param request.DeleteDevice
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = dc.deviceService.Delete(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (dc *DeviceController) Config(c *gin.Context) {
	var param request.DeviceDetail
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	d, err := dc.deviceService.ConfigWithName(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, d)
}

func (dc *DeviceController) Update(c *gin.Context) {
	var param request.UpdateDevice
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = dc.deviceService.Update(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (dc *DeviceController) UpdateUnit(c *gin.Context) {
	var param request.UpdateDeviceUnit
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = dc.deviceService.UpdateUnit(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (dc *DeviceController) UpdateConfig(c *gin.Context) {
	var param request.UpdateConfig
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = dc.deviceService.UpdateConfig(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (dc *DeviceController) List(c *gin.Context) {
	var param request.GetDeviceList
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := dc.deviceService.List(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (dc *DeviceController) DoorList(c *gin.Context) {
	var param request.GetDeviceList
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := dc.deviceService.DoorList(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (dc *DeviceController) ListByFirstType(c *gin.Context) {
	var param request.GetDeviceListByFirstType
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := dc.deviceService.ListByFirstType(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (dc *DeviceController) HistoryData(c *gin.Context) {
	var param request.DeviceHistoryData
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := dc.deviceService.HistoryData(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (dc *DeviceController) RealTimeDataFor3D(c *gin.Context) {
	var param request.RealtimeData
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := dc.deviceService.RealtimeDataFor3D(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (dc *DeviceController) RealtimeData(c *gin.Context) {
	var (
		upgrade websocket.Upgrader
		param   request.RealtimeData
		isFirst = true
		paramCh = make(chan request.RealtimeData)
	)

	upgrade.CheckOrigin = func(r *http.Request) bool {
		return true
	}

	type resp struct {
		Code int         `json:"code"`
		Msg  string      `json:"msg"`
		Data interface{} `json:"data"`
	}

	conn, err := upgrade.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	defer conn.Close()

	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	go func() {
		var temp request.RealtimeData
		for {
			err := conn.ReadJSON(&temp)
			if err != nil {
				conn.Close()
				return
			}

			paramCh <- temp
			ticker.Reset(10 * time.Second)
			isFirst = false
		}
	}()

	for {
		select {
		case temp := <-paramCh:
			param = temp
		case <-ticker.C:
			if isFirst {
				conn.Close()
			}
		}

		results, err := dc.deviceService.RealtimeData(param)
		if err != nil {
			conn.WriteJSON(resp{
				Code: 10,
				Msg:  err.Error(),
			})
			continue
		}
		err = conn.WriteJSON(resp{
			Code: 1,
			Msg:  "success",
			Data: results,
		})
		if err != nil {
			break
		}

	}
}

func (dc *DeviceController) RealtimeStatus(c *gin.Context) {
	var (
		upgrade websocket.Upgrader
		param   request.RealtimeData
		isFirst = true
		paramCh = make(chan request.RealtimeData)
	)

	upgrade.CheckOrigin = func(r *http.Request) bool {
		return true
	}

	type resp struct {
		Code int         `json:"code"`
		Msg  string      `json:"msg"`
		Data interface{} `json:"data"`
	}

	conn, err := upgrade.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	defer conn.Close()

	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	go func() {
		var temp request.RealtimeData
		for {
			err := conn.ReadJSON(&temp)
			if err != nil {
				conn.Close()
				return
			}

			paramCh <- temp
			ticker.Reset(10 * time.Second)
			isFirst = false
		}
	}()

	for {
		select {
		case temp := <-paramCh:
			param = temp
		case <-ticker.C:
			if isFirst {
				conn.Close()
			}
		}

		results, err := dc.deviceService.RealtimeStatus(param)
		if err != nil {
			conn.WriteJSON(resp{
				Code: 10,
				Msg:  err.Error(),
			})
			continue
		}
		err = conn.WriteJSON(resp{
			Code: 1,
			Msg:  "success",
			Data: results,
		})
		if err != nil {
			break
		}

	}
}

func (dc *DeviceController) Control(c *gin.Context) {
	var param request.DeviceControl
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = dc.deviceService.Control(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (dc *DeviceController) NameList(c *gin.Context) {
	var param request.NameList
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := dc.deviceService.NameList(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (dc *DeviceController) ListByAreaId(c *gin.Context) {
	var param request.DeviceListByAreaID
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)
	list, err := dc.deviceService.ListByAreaId(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}
func (dc *DeviceController) ListByAreaIDRecursive(c *gin.Context) {
	var param request.DeviceListByAreaID
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)
	list, err := dc.deviceService.ListByAreaIDRecursive(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (dc *DeviceController) ListWithConfig(c *gin.Context) {
	var param request.DeviceListWithConfig
	if err := c.ShouldBindQuery(&param); err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := dc.deviceService.ListWithConfig(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (dc *DeviceController) ListCustom(c *gin.Context) {

	list, err := dc.deviceService.CustomList()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (dc *DeviceController) ListByPort(c *gin.Context) {
	var param request.DeviceListByPort
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)
	list, err := dc.deviceService.ListByPort(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (dc *DeviceController) AlarmBinding(c *gin.Context) {
	var param request.DeviceAlarmBinding
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := dc.deviceService.AlarmBinding(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (dc *DeviceController) SetAlarmBinding(c *gin.Context) {
	var param request.SetDeviceAlarmBinding
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = dc.deviceService.SetAlarmBinding(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (dc *DeviceController) ReportDataInternal(fid string, msg mqtt.Message) error {
	var param request.DeviceData
	err := json.Unmarshal(msg.Payload(), &param)
	if err != nil {
		return err
	}

	param.HasAlarmed = true

	return dc.deviceService.ReportData(param)
}

func (dc *DeviceController) ResumeReportData(fid string, msg mqtt.Message) error {
	var param []request.ResumeDeviceData

	err := json.Unmarshal(msg.Payload(), &param)
	if err != nil {
		return err
	}

	err = dc.deviceService.ResumeReportData(param)

	return err
}

func (dc *DeviceController) Detail(c *gin.Context) {
	var param request.DeviceDetail
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	data, err := dc.deviceService.Detail(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, data)
}

func (dc *DeviceController) UpdateUnitByDriver(c *gin.Context) {
	var param request.UpdateDeviceUnitByDriver
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	err = dc.deviceService.UpdateUnitByDriver(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	response.Ok(c)
}

func (dc *DeviceController) NameListByDriver(c *gin.Context) {
	var param request.DeviceNameListByDriver
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	names, err := dc.deviceService.NameListByDriverID(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	response.OkWithData(c, names)
}

func (dc *DeviceController) CloseSoundLight(c *gin.Context) {
	err := dc.deviceService.CloseSoundLight()
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	response.Ok(c)
}

func (dc *DeviceController) CreateAsset(c *gin.Context) {
	var param request.CreateAssetDevice
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = dc.deviceService.CreateAsset(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (dc *DeviceController) UpdateAsset(c *gin.Context) {
	var param request.UPdateAssetDevice
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = dc.deviceService.UpdateAsset(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (dc *DeviceController) ReportDeviceData(fid string, msg mqtt.Message) error {
	var param request.ReportDevicesData
	err := json.Unmarshal(msg.Payload(), &param)
	if err != nil {
		return err
	}

	param.FID = fid

	err = dc.deviceService.ReportDeviceData(param)
	return err
}

func (dc *DeviceController) ReportAlarm(fid string, msg mqtt.Message) error {
	var param request.ReportDeviceAlarm
	err := json.Unmarshal(msg.Payload(), &param)
	if err != nil {
		return err
	}

	param.FID = fid

	err = dc.deviceService.ReportAlarm(param)
	if err != nil {
		return err
	}
	return nil
}

func (dc *DeviceController) ReportList(c *gin.Context) {
	var param request.ReportDeviceList
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, total, err := dc.deviceService.ReportList(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithPage(c, list, total, param.Page, param.Size)
}

func (dc *DeviceController) SetReportInfo(c *gin.Context) {
	var param request.SetReportDeviceInfo
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = dc.deviceService.SetReportInfo(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
