package controller

import (
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
)

type BusinessTypeController struct {
	service *service.BusinessTypeService
}

func NewBusinessTypeController(service *service.BusinessTypeService) *BusinessTypeController {
	return &BusinessTypeController{
		service: service,
	}
}

func (b *BusinessTypeController) Create(c *gin.Context) {
	var param request.CreateBusinessType
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = b.service.Create(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (b *BusinessTypeController) TreeOnly(c *gin.Context) {
	tree, err := b.service.TreeOnly()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, tree)
}

func (b *BusinessTypeController) ListWithDevices(c *gin.Context) {
	var param request.BusinessTypeTreeWithDevice
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)
	tree, err := b.service.ListWithDevice(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, tree)
}

func (b *BusinessTypeController) Update(c *gin.Context) {
	var param request.UpdateBusinessType
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = b.service.Update(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (b *BusinessTypeController) Delete(c *gin.Context) {
	var param request.DeleteBusinessType
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = b.service.Delete(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (b *BusinessTypeController) ListByPID(c *gin.Context) {
	var param request.FindBusinessTypeByPID
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)
	list, err := b.service.ListByPID(param)
	if err != nil {
		response.FailWithError(c, err)
	}

	response.OkWithData(c, list)
}

func (b *BusinessTypeController) ListHasDevice(c *gin.Context) {
	var param request.BusinessTypeListHasDevice
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)
	list, err := b.service.ListHasDevice(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (b *BusinessTypeController) TreeOnlyUser(c *gin.Context) {
	var param request.BusinessTypeTreeOnlyUser
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)

	tree, err := b.service.TreeOnlyUser(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, tree)
}
