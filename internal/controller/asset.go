package controller

import (
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
)

type AssetController struct {
	assetService *service.AssetService
}

func NewAssetController(assetService *service.AssetService) *AssetController {
	return &AssetController{
		assetService: assetService,
	}
}

func (ac *AssetController) CreateCabinet(c *gin.Context) {
	var param request.CreateCabinet
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = ac.assetService.CreateCabinet(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (ac *AssetController) UpdateCabinet(c *gin.Context) {
	var param request.UpdateCabinet
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = ac.assetService.UpdateCabinet(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (ac *AssetController) CabinetList(c *gin.Context) {
	var param request.CabinetList
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := ac.assetService.CabinetList(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (ac *AssetController) DeleteCabinet(c *gin.Context) {
	var param request.DeleteCabinet
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = ac.assetService.DeleteCabinet(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (ac *AssetController) CreateAsset(c *gin.Context) {
	var param request.CreateAsset
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = ac.assetService.CreateAsset(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (ac *AssetController) UpdateAsset(c *gin.Context) {
	var param request.UpdateAsset
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = ac.assetService.UpdateAsset(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (ac *AssetController) AssetList(c *gin.Context) {
	var param request.AssetList
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := ac.assetService.AssetList(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (ac *AssetController) DeleteAsset(c *gin.Context) {
	var param request.DeleteAsset
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = ac.assetService.DeleteAsset(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
