package controller

import (
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
)

type MaintainController struct {
	ms *service.MaintainService
}

func NewMaintainController(ms *service.MaintainService) *MaintainController {
	return &MaintainController{
		ms: ms,
	}
}

func (m *MaintainController) Create(c *gin.Context) {
	var param request.CreateMaintain
	err := c.ShouldBind(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	if param.File != nil && param.File.Size > 1024*1024*10 {
		response.FailWithMsg(c, "文件大小不能超过10M")
		return
	}

	err = m.ms.Create(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (m *MaintainController) List(c *gin.Context) {
	var param request.MaintainList
	err := c.ShouldBind<PERSON>uery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	list, total, err := m.ms.List(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithPage(c, list, total, param.Page, param.Size)
}

func (m *MaintainController) PlanList(c *gin.Context) {
	var param request.MaintainPlanList
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := m.ms.PlanList(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	response.OkWithData(c, list)
}

func (m *MaintainController) Execute(c *gin.Context) {
	var param request.ExecuteMaintainPlan
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)
	err = m.ms.Execute(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
