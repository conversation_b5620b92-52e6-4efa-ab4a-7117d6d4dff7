package controller

import (
	"net/http"
	"time"
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"go.uber.org/zap"
)

type AreaController struct {
	as *service.AreaService
	l  *zap.Logger
}

func NewAreaController(as *service.AreaService, log *zap.Logger) *AreaController {
	return &AreaController{
		as: as,
		l:  log.Named("area_controller:"),
	}
}

func (a *AreaController) Create(c *gin.Context) {
	var param request.CreateArea
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = a.as.Create(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (a *AreaController) Tree(c *gin.Context) {
	var param request.AreaTreeByUserID
	param.ParseToken(c)

	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	// todo 暂时给定默认值
	if param.BusinessTypeID == 0 {
		param.BusinessTypeID = 1
	}

	tree, err := a.as.Tree(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, tree)
}

func (a *AreaController) TreeOnlyByScene(c *gin.Context) {
	var param request.AreaTreeByUserIDScene
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)

	tree, err := a.as.TreeOnlyByScene(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, tree)
}
func (a *AreaController) TreeOnly(c *gin.Context) {
	var param request.AreaTreeByUserID
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)

	tree, err := a.as.TreeOnly(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, tree)
}

func (a *AreaController) All(c *gin.Context) {
	tree, err := a.as.All()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, tree)
}

func (a *AreaController) AllWithTypeCounts(c *gin.Context) {
	tree, err := a.as.AllWithTypeCounts()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, tree)
}

func (a *AreaController) AllWithDevices(c *gin.Context) {
	tree, err := a.as.AllWithDevices()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, tree)
}

func (a *AreaController) Delete(c *gin.Context) {
	var param request.DeleteArea
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = a.as.Delete(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (a *AreaController) CreateScene(c *gin.Context) {
	var param request.CreateScene
	err := c.ShouldBind(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	if param.Img != nil {
		if param.Img.Size > 10*1024*1024 {
			response.FailWithMsg(c, "图片不能大于10MB")
			return
		}
	}
	id, err := a.as.CreateScene(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, struct {
		ID int `json:"id"`
	}{
		ID: id,
	})
}

func (a *AreaController) SceneDetail(c *gin.Context) {
	var param request.SceneDetail
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	d, err := a.as.SceneDetail(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, d)
}

func (a *AreaController) UpdateScene(c *gin.Context) {
	var param request.UpdateScene
	err := c.ShouldBind(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	name, err := a.as.UpdateScene(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, struct {
		Name string `json:"name"`
	}{
		Name: name,
	})
}

func (a *AreaController) DeleteScene(c *gin.Context) {
	var param request.DeleteScene
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	err = a.as.DeleteScene(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (a *AreaController) Update(c *gin.Context) {
	var param request.UpdateArea
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = a.as.Update(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (a *AreaController) TreeWithDeviceConfigs(c *gin.Context) {
	var param request.AreaTreeWithDeviceConfigs
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	param.ParseToken(c)

	tree, err := a.as.TreeWithDeviceConfigs(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, tree)
}

func (a *AreaController) PUE(c *gin.Context) {
	var param request.GetPUE
	if err := c.ShouldBindQuery(&param); err != nil {
		response.FailWithError(c, err)
		return
	}
	pue, err := a.as.PUE(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, pue)
}

func (a *AreaController) ListByPID(c *gin.Context) {
	var param request.AreaListByPID
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := a.as.ListByPID(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}
func (a *AreaController) ListByPIDRecursive(c *gin.Context) {
	var param request.AreaListByPID
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := a.as.ListByPIDRecursive(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (a *AreaController) PUEEveryHour(c *gin.Context) {
	var param request.PUEEveryHour
	if err := c.ShouldBindQuery(&param); err != nil {
		response.FailWithError(c, err)
		return
	}
	result, err := a.as.PUEEveryHour(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, result)
}

func (a *AreaController) DefaultSceneType(c *gin.Context) {
	var param request.DefaultSceneType
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = a.as.DefaultSceneType(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (a *AreaController) DefaultSceneTypeList(c *gin.Context) {
	var param request.AreaDefaultSceneType
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := a.as.DefaultSceneTypeList(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (a *AreaController) TreeWithAlarmCounts(c *gin.Context) {
	var param request.AreaTreeWithAlarmCounts
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)
	tree, err := a.as.TreeWithAlarmCounts(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, tree)
}
func (ac *AreaController) ListWithAlarmedByPid(c *gin.Context) {
	var (
		param    request.AreaListWithAlarmedByPid
		upgrader websocket.Upgrader
	)
	param.ParseToken(c)

	upgrader.CheckOrigin = func(r *http.Request) bool {
		return true
	}

	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	defer conn.Close()
	type responseT struct {
		Code int         `json:"code"`
		Msg  string      `json:"msg"`
		Data interface{} `json:"data"`
	}
	var (
		paramCh = make(chan request.AreaListWithAlarmedByPid, 1)
		ticket  = time.NewTicker(30 * time.Second)
		list    []response.AreaListWithAlarmed
		isFirst = true
	)

	go func() {
		var temp request.AreaListWithAlarmedByPid
		for {
			err = conn.ReadJSON(&temp)
			if err != nil {
				conn.Close()
				return
			}

			isFirst = false
			param.BusinessTypeID = temp.BusinessTypeID
			param.ParentID = temp.ParentID
			paramCh <- param
		}
	}()

	for {
		select {
		case param = <-paramCh:
		case <-ticket.C:
			if isFirst {
				conn.WriteJSON(responseT{
					Code: 10,
					Msg:  "未收到参数",
				})
				return
			}
		}
		list, err = ac.as.ListWithAlarmedByPid(param)
		if err != nil {
			conn.WriteJSON(responseT{
				Code: 10,
				Msg:  err.Error(),
			})
			return
		}

		conn.WriteJSON(responseT{
			Code: 1,
			Msg:  "success",
			Data: list,
		})
	}
}

func (a *AreaController) TreeWithDevicesByBtID(c *gin.Context) {
	var (
		param    request.AreaTreeWithDevicesByBtID
		upgrader websocket.Upgrader
		isFirst  bool
	)

	upgrader.CheckOrigin = func(r *http.Request) bool {
		return true
	}

	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	defer conn.Close()

	type responseT struct {
		Code int         `json:"code"`
		Msg  string      `json:"msg"`
		Data interface{} `json:"data"`
	}

	var (
		paramCh = make(chan request.AreaTreeWithDevicesByBtID, 1)
		ticket  = time.NewTicker(time.Minute)
	)

	param.ParseToken(c)

	go func() {
		var temp request.AreaTreeWithDevicesByBtID
		for {
			err = conn.ReadJSON(&temp)
			if err != nil {
				conn.Close()
				return
			}

			isFirst = true
			param.BusinessTypeID = temp.BusinessTypeID
			paramCh <- param
		}
	}()

	for {
		select {
		case param = <-paramCh:
		case <-ticket.C:
		}
		if !isFirst {
			conn.WriteJSON(responseT{
				Code: 10,
				Msg:  "未收到参数",
			})
			return
		}
		list, err := a.as.TreeWithDevicesByBtID(param)
		if err != nil {
			conn.WriteJSON(responseT{
				Code: 10,
				Msg:  err.Error(),
			})
			return
		}

		conn.WriteJSON(responseT{
			Code: 1,
			Msg:  "success",
			Data: list,
		})
	}
}

func (a *AreaController) IDs(c *gin.Context) {
	var param request.AreaIDs
	param.ParseToken(c)
	list, err := a.as.IDs(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (a *AreaController) RealTimeAreaStatus(c *gin.Context) {
	var (
		upgrader websocket.Upgrader
		param    request.RealtimeAreaStatus
		isFirst  = true
		paramCh  = make(chan request.RealtimeAreaStatus)
		d        = 5 * time.Second
	)

	upgrader.CheckOrigin = func(r *http.Request) bool {
		return true
	}

	type respT struct {
		Code int         `json:"code"`
		Msg  string      `json:"msg"`
		Data interface{} `json:"data"`
	}

	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)

	if err != nil {
		response.FailWithError(c, err)
		return
	}
	defer conn.Close()

	ticker := time.NewTicker(d)
	defer ticker.Stop()

	go func() {
		var temp request.RealtimeAreaStatus
		for {
			err = conn.ReadJSON(&temp)
			if err != nil {
				conn.Close()
				return
			}

			paramCh <- temp
			ticker.Reset(d)
			isFirst = false
		}
	}()

	for {
		select {
		case temp := <-paramCh:
			param = temp
		case <-ticker.C:
			if isFirst {
				conn.Close()
			}
		}

		results, err := a.as.RealtimeStatus(param.IDs)
		if err != nil {
			conn.WriteJSON(respT{
				Code: 10,
				Msg:  err.Error(),
			})
			continue
		}
		err = conn.WriteJSON(respT{
			Code: 1,
			Msg:  "success",
			Data: results,
		})
		if err != nil {
			break
		}
	}
}
