package controller

import (
	"tw_platform/internal/service"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type VirtualVariableController struct {
	service *service.VirtualVariableService
	l       *zap.Logger
}

func NewVirtualVariableController(us *service.VirtualVariableService, log *zap.Logger) *VirtualVariableController {
	return &VirtualVariableController{
		service: us,
		l:       log.Named("virtual_controller:"),
	}
}
func (d *VirtualVariableController) Create(c *gin.Context) {
	var param model.VirtualDevice
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = d.service.Create(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (d *VirtualVariableController) List(c *gin.Context) {
	list, err := d.service.List()
	if err != nil {
		response.FailWithError(c, err)
	}
	response.OkWithData(c, list)
}
func (dc *VirtualVariableController) Update(c *gin.Context) {
	var param model.VirtualDevice
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = dc.service.Update(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (dc *VirtualVariableController) UpdateUnit(c *gin.Context) {
	var param request.UpdateVirtualUnit
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = dc.service.UpdateUnit(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (dc *VirtualVariableController) Delete(c *gin.Context) {
	var param request.DeleteVirtual
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = dc.service.Delete(param.ID)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (dc *VirtualVariableController) Detail(c *gin.Context) {
	var param request.DeleteVirtual
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	data, err := dc.service.Detail(param.ID)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, data)
}
