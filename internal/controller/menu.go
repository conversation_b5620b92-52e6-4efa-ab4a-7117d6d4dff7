package controller

import (
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type MenuController struct {
	ms *service.MenuService
	l  *zap.Logger
}

func NewMenuController(ms *service.MenuService, l *zap.Logger) *MenuController {
	return &MenuController{
		ms: ms,
		l:  l,
	}
}

func (m *MenuController) Create(c *gin.Context) {
	var param request.CreateMenu
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = m.ms.Create(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (m *MenuController) Tree(c *gin.Context) {
	var param request.MenuTree
	param.ParseToken(c)

	tree, err := m.ms.Tree(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, tree)
}

func (m *MenuController) Delete(c *gin.Context) {
	var param request.DeleteMenu
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = m.ms.Delete(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (m *MenuController) Detail(c *gin.Context) {
	var param request.FindByID
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	d, err := m.ms.Detail(param.ID)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, d)
}

func (m *MenuController) Update(c *gin.Context) {
	var param request.UpdateMenu
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = m.ms.Update(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (m *MenuController) ListByRole(c *gin.Context) {
	var param request.FindByID
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := m.ms.ListByRole(param.ID)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	response.OkWithData(c, list)
}

func (m *MenuController) ListByPID(c *gin.Context) {
	var param request.MenuListByPID
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := m.ms.ListByPID(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}
