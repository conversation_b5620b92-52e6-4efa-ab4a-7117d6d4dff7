package controller

import (
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
)

type PermissionController struct {
	service *service.PermissionService
}

func NewPermissionController(permissionService *service.PermissionService) *PermissionController {
	return &PermissionController{
		service: permissionService,
	}
}

func (p *PermissionController) Create(c *gin.Context) {
	var req request.CreatePermission
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithError(c, err)
		return
	}
	if err := p.service.Create(req); err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (p *PermissionController) List(c *gin.Context) {
	var param request.PermissionList
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, total, err := p.service.List(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithPage(c, list, total, param.Page, param.Size)
}

func (p *PermissionController) Update(c *gin.Context) {
	var param request.UpdatePermission
	if err := c.ShouldBindJSON(&param); err != nil {
		response.FailWithError(c, err)
		return
	}

	err := p.service.Update(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (p *PermissionController) Delete(c *gin.Context) {
	var param request.DeletePermission
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = p.service.Delete(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
