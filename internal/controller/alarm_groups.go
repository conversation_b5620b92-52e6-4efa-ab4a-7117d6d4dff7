package controller

import (
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AlarmGroupController struct {
	service *service.AlarmGroupService
	l       *zap.Logger
}

func NewAlarmGroupController(us *service.AlarmGroupService, log *zap.Logger) *AlarmGroupController {
	return &AlarmGroupController{
		service: us,
		l:       log.Named("alarm_group_controller:"),
	}
}
func (d *AlarmGroupController) Create(c *gin.Context) {
	var param request.CreateAlarmGroup
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = d.service.Create(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (d *AlarmGroupController) List(c *gin.Context) {
	list, err := d.service.List()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}
func (dc *AlarmGroupController) Delete(c *gin.Context) {
	var param request.DeleteAlarmGroup
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = dc.service.Delete(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (d *AlarmGroupController) Update(c *gin.Context) {
	var param request.UpdateAlarmGroup
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = d.service.Update(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
