package controller

import (
	"encoding/base64"
	"io/ioutil"
	"net/http"
	"time"
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

type LinkageCaptureController struct {
	service *service.LinkageCaptureService
}

func NewLinkageCaptureController(service *service.LinkageCaptureService) *LinkageCaptureController {
	return &LinkageCaptureController{service: service}
}
func (l *LinkageCaptureController) OperationList(c *gin.Context) {
	var param request.LinkageCaptureList

	if err := c.ShouldBindQuery(&param); err != nil {
		response.FailWithError(c, err)
		return
	}
	list, total, err := l.service.GetList(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithPage(c, list, total, param.Page, param.Size)
}
func (dc *LinkageCaptureController) RealtimeData(c *gin.Context) {
	var (
		upgrade websocket.Upgrader
		param   request.LinkageCaptureList
		isFirst = true
		paramCh = make(chan request.LinkageCaptureList)
	)

	upgrade.CheckOrigin = func(r *http.Request) bool {
		return true
	}

	type resp struct {
		Code int         `json:"code"`
		Msg  string      `json:"msg"`
		Data interface{} `json:"data"`
	}

	conn, err := upgrade.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	defer conn.Close()

	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	go func() {
		var temp request.LinkageCaptureList
		for {
			err := conn.ReadJSON(&temp)
			if err != nil {
				conn.Close()
				return
			}

			paramCh <- temp
			ticker.Reset(10 * time.Second)
			isFirst = false
		}
	}()

	for {
		select {
		case temp := <-paramCh:
			param = temp
		case <-ticker.C:
			if isFirst {
				conn.Close()
			}
		}
		if param.Size == 0 {
			param.Size = 10
		}
		list, _, err := dc.service.GetList(param)
		if err != nil {
			conn.WriteJSON(resp{
				Code: 10,
				Msg:  err.Error(),
			})
			continue
		}
		for i, l := range list {
			fileData, err := ioutil.ReadFile(l.Image)
			if err != nil {
				continue
			}
			base64Str := base64.StdEncoding.EncodeToString(fileData)
			l.Image = base64Str
			list[i] = l
		}
		err = conn.WriteJSON(resp{
			Code: 1,
			Msg:  "success",
			Data: list,
		})
		if err != nil {
			break
		}

	}
}
