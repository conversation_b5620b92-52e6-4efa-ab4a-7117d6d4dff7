package controller

import (
	"net/http"
	"time"
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

type LightScheduleController struct {
	lss *service.LightScheduleService
}

func NewLightScheduleController(lss *service.LightScheduleService) *LightScheduleController {
	return &LightScheduleController{
		lss: lss,
	}
}

func (lc *LightScheduleController) Create(c *gin.Context) {
	var param request.CreateLightSchedule
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	var (
		begin *time.Time
		end   *time.Time
	)
	l, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	if param.Begin != "" {
		temp, err := time.ParseInLocation(time.DateTime, param.Begin, l)
		if err != nil {
			response.FailWithError(c, err)
			return
		}
		begin = &temp
	}
	if param.End != "" {
		temp, err := time.ParseInLocation(time.DateTime, param.End, l)
		if err != nil {
			response.FailWithError(c, err)
			return
		}
		end = &temp
	}
	param.BeginAt = begin
	param.EndAt = end

	err = lc.lss.Create(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (lc *LightScheduleController) Update(c *gin.Context) {
	var param request.UpdateLightSchedule
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = lc.lss.Update(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (lc *LightScheduleController) Detail(c *gin.Context) {
	var param request.LightScheduleDetail
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	data, err := lc.lss.Detail(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	response.OkWithData(c, data)
}

func (lc *LightScheduleController) List(c *gin.Context) {
	var param request.LightScheduleList
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	list, err := lc.lss.List(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, list)
}

func (lc *LightScheduleController) Delete(c *gin.Context) {
	var param request.LishtScheduleDelete
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = lc.lss.Delete(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (lc *LightScheduleController) ControlByArea(c *gin.Context) {
	var param request.LightScheduleControlByArea
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = lc.lss.ControlByArea(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (lc *LightScheduleController) Statistic(c *gin.Context) {
	var (
		param    request.LightScheduleStatistic
		upgrader = websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true
			},
		}
		paramCh = make(chan request.LightScheduleStatistic)
		isFirst = true
	)

	type resp struct {
		Code int         `json:"code"`
		Msg  string      `json:"msg"`
		Data interface{} `json:"data"`
	}

	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	defer conn.Close()

	param.ParseToken(c)

	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	go func() {
		var temp request.LightScheduleStatistic
		for {
			err := conn.ReadJSON(&temp)
			if err != nil {
				conn.Close()
				return
			}

			param.AreaID = temp.AreaID
			paramCh <- param
			isFirst = false
			ticker.Reset(10 * time.Second)
		}
	}()

	for {
		select {
		case temp := <-paramCh:
			param.AreaID = temp.AreaID
		case <-ticker.C:
			if isFirst {
				conn.Close()
			}
		}

		result, err := lc.lss.Statistic(param)
		if err != nil {
			conn.WriteJSON(resp{
				Code: 10,
				Msg:  err.Error(),
			})
			continue
		}
		err = conn.WriteJSON(resp{
			Code: 1,
			Msg:  "success",
			Data: result,
		})
		if err != nil {
			break
		}

	}
}
func (lc *LightScheduleController) StatisticAll(c *gin.Context) {
	var (
		param    request.LightScheduleStatistic
		upgrader = websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true
			},
		}
	)

	type resp struct {
		Code int         `json:"code"`
		Msg  string      `json:"msg"`
		Data interface{} `json:"data"`
	}

	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	defer conn.Close()

	param.ParseToken(c)

	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		result, err := lc.lss.StatisticAll(param)
		if err != nil {
			conn.WriteJSON(resp{
				Code: 10,
				Msg:  err.Error(),
			})
			continue
		}
		err = conn.WriteJSON(resp{
			Code: 1,
			Msg:  "success",
			Data: result,
		})
		if err != nil {
			break
		}
		select {
		case <-ticker.C:
		}

	}
}

func (lc *LightScheduleController) ControlAll(c *gin.Context) {
	var param request.LightScheduleControlAll
	err := c.ShouldBindJSON(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)

	err = lc.lss.ControlAll(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
