package controller

import (
	"net/http"
	"os"
	"time"
	"tw_platform/internal/service"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"go.uber.org/zap"
)

type SystemController struct {
	systemService *service.SystemService
	alarmService  *service.AlarmService
	l             *zap.Logger
}

func NewSystemController(
	systemService *service.SystemService,
	alarmService *service.AlarmService,
	l *zap.Logger,
) *SystemController {
	return &SystemController{
		systemService: systemService,
		alarmService:  alarmService,
		l:             l.Named("system_controller"),
	}
}

func (s *SystemController) SystemInfo(c *gin.Context) {

	info, err := s.systemService.SystemInfo()
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, info)
}

func (s *SystemController) Update(c *gin.Context) {
	var param request.UpdateSystem

	if err := c.ShouldBindJSON(&param); err != nil {
		response.FailWithError(c, err)
		return
	}
	err := s.systemService.Update(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (s *SystemController) UpgradePages(c *gin.Context) {
	var param request.UpdateWebPage
	err := c.ShouldBind(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	if param.File.Filename != "dist.zip" {
		response.FailWithMsg(c, "文件名应为\"dist.zip\"")
		return
	}

	param.Path = "./page_dir/"

	path := os.Getenv("PAGE_DIR")
	if path != "" {
		param.Path = path
	}

	err = s.systemService.UpgradePages(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (s *SystemController) SSEUpgradePages(c *gin.Context) {
	var param request.UpdateWebPage
	err := c.ShouldBind(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}

	if param.File.Filename != "dist.zip" {
		response.FailWithMsg(c, "文件名应为\"dist.zip\"")
		return
	}

	param.Path = os.Getenv("PAGE_DIR")
	//todo 调试专用路径
	param.Path = "./page_dir/"

	ch, total, err := s.systemService.SSEUpgradePages(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	var (
		successed, failured int
	)
	for resp := range ch {
		if resp.Err != nil {
			failured += 1
			s.l.Warn("update web page failed", zap.Error(resp.Err))
		} else {
			successed += 1
		}
		resp.Failured = failured
		resp.Successed = successed
		resp.Total = total

		c.SSEvent("progressing", resp)
	}
	c.SSEvent("finished", nil)
}

func (s *SystemController) UpgradeLogo(c *gin.Context) {
	var param request.UpgradeLogo

	if err := c.ShouldBind(&param); err != nil {
		response.FailWithError(c, err)
		return
	}

	err := s.systemService.UpgradeLogo(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}

func (s *SystemController) Statistic1(c *gin.Context) {
	var param request.SystemStatistic
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)

	statistic, err := s.systemService.Statistic(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, statistic)
}

func (s *SystemController) VersionInfo(c *gin.Context) {
	v := s.systemService.VersionInfo()
	response.OkWithData(c, v)
}

func (s *SystemController) Statistic(c *gin.Context) {
	var param request.SystemStatistic
	param.ParseToken(c)

	var (
		upgrade websocket.Upgrader
	)

	upgrade.CheckOrigin = func(r *http.Request) bool {
		return true
	}

	conn, err := upgrade.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	defer conn.Close()

	var (
		interval = 10 * time.Second
		ticker   = time.NewTicker(interval)
		paramCh  = make(chan request.SystemStatistic, 1)
	)
	type responseT struct {
		Code int         `json:"code"`
		Msg  string      `json:"msg"`
		Data interface{} `json:"data"`
	}
	defer ticker.Stop()

	go func() {
		var temp request.SystemStatistic
		for {
			err := conn.ReadJSON(&temp)
			if err != nil {
				conn.Close()
				return
			}

			param.AlarmFilterCommon = temp.AlarmFilterCommon
			param.AlarmID = temp.AlarmID
			paramCh <- param
			ticker.Reset(interval)
		}
	}()

	paramCh <- param

	for {
		select {
		case temp := <-paramCh:
			param = temp
		case <-ticker.C:
		}
		resp, err := s.systemService.Statistic(param)
		if err != nil {
			conn.WriteJSON(responseT{
				Code: 10,
				Msg:  err.Error(),
			})
			continue
		}

		if param.AlarmID != 0 {
			alarms, err := s.alarmService.RealTimePush(param)
			if err != nil {
				conn.WriteJSON(responseT{
					Code: 10,
					Msg:  err.Error(),
				})
			}
			resp.Alarms = alarms
			if len(resp.Alarms) != 0 {
				param.AlarmID = 0
				param.AlarmFilterCommon = request.AlarmFilterCommon{}
			}
		}

		if resp.Alarms == nil {
			resp.Alarms = []response.RealtimeList{}
		}

		err = conn.WriteJSON(responseT{
			Code: 1,
			Msg:  "success",
			Data: resp,
		})
		if err != nil {
			break
		}
	}
}

func (s *SystemController) StatisticApp(c *gin.Context) {
	var param request.SystemStatisticApp
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	param.ParseToken(c)

	statistic, err := s.systemService.StatisticApp(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.OkWithData(c, statistic)
}
