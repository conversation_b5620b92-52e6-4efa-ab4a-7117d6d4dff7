package controller

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"tw_platform/internal/service"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
	"tw_platform/pkg/model/response"
)

type LinkageController struct {
	ls *service.LinkageService
	l  *zap.Logger
}

func NewLinkageController(ls *service.LinkageService, log *zap.Logger) *LinkageController {
	return &LinkageController{
		ls: ls,
		l:  log.Named("linkage_controller:"),
	}
}

func (lc *LinkageController) GetLinkageList(c *gin.Context) {
	var (
		list []model.Linkage
	)
	list, err := lc.ls.GetLinkageList()
	if err != nil {
		return
	}
	response.OkWithData(c, list)
}
func (lc *LinkageController) GetLinkageName(c *gin.Context) {
	var param request.LinkageDetail
	err := c.ShouldBindQuery(&param)

	if err != nil {
		response.FailWithMsg(c, "failed")
		return
	}
	l, err := lc.ls.GetLinkageName(param.ID)
	if err != nil {
		response.FailWithMsg(c, "failed")
		return
	}
	response.OkWithData(c, l)
}
func (lc *LinkageController) GetLinkageDetail(c *gin.Context) {
	var param request.LinkageDetail
	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithMsg(c, "failed")
		return
	}
	l, err := lc.ls.GetLinkageDetail(param.ID)
	if err != nil {
		response.FailWithMsg(c, "failed")
		return
	}
	response.OkWithData(c, l)

}
func (lc *LinkageController) CreateLinkage(c *gin.Context) {
	var (
		param request.CreateLinkage
		id    int
	)
	err := c.ShouldBindBodyWithJSON(&param)
	if err != nil {
		response.FailWithMsg(c, "failed")
		return
	}
	id, err = lc.ls.CreateLinkage(param)
	if err != nil {
		response.FailWithMsg(c, "failed")
		return
	}
	response.OkWithData(c, gin.H{"id": id})
}

func (lc *LinkageController) DeleteLinkage(c *gin.Context) {
	var (
		param request.DeleteLinkage
	)

	err := c.ShouldBindQuery(&param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	err = lc.ls.DeleteLinkage(param.ID)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (lc *LinkageController) UpdateLinkageInfo(c *gin.Context) {
	var (
		param request.UpdateLinkageInfo
	)
	err := c.ShouldBindBodyWithJSON(&param)
	if err != nil {
		response.FailWithMsg(c, "failed")
		return
	}
	err = lc.ls.UpdateLinkageInfo(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
func (lc *LinkageController) UpdateLinkageData(c *gin.Context) {
	var (
		param request.UpdateLinkageData
	)
	err := c.ShouldBindBodyWithJSON(&param)
	if err != nil {
		response.FailWithMsg(c, "failed")
		return
	}
	err = lc.ls.UpdateLinkageData(param)
	if err != nil {
		response.FailWithError(c, err)
		return
	}
	response.Ok(c)
}
