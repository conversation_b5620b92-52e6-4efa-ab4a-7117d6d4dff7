# 泰物平台

## 模块流程及作用

### HTTP、MQTT 处理流程

`internal/route` 下为各个子模块路由文件定义，添加新的路由文件时要同步增加到 `pkg/app/route.go` 文件内函数调用。

`internal/controller` 为解析相关参数使用。

`internal/service` 为处理业务逻辑使用，不涉及数据库、HTTP 调用。

`internal/repository` 读取数据库、HTTP 调用等。

### 结构体映射定义

`pkg/model/` 定义数据库结构结构体。

`pkg/model/request/` 定义请求参数结构体。

`pkg/model/response/` 定义响应结构体。

### cron 任务添加

`internal/route/cron.go` 中增加 cron 表达式。

`internal/service/cron.go` 中为 cron 入口函数

### 其他

`pkg/initialize` 为 MySQL、Redis 等公共组件初始化目录


## 依赖注入工具使用

1. 安装
```bash
go install github.com/google/wire/cmd/wire@latest
```

2. 生成代码

```bash
wire gen $(go list -m)/pkg/app
```

3. 代码内使用

-  增加 controller 时：增加对应结构体定义与 New* 函数，添加在 App 结构体内并在 `pkg/app/wire.go` 中声明。
-  增加 service 时，在 `pkg/app/wire.go` 中对应代码块中声明。
-  增加 repository 时，在 `pkg/app/wire.go` 中对应代码块中声明。

- 增加公共组件时：在 `pkg/initialize/` 中增加对应 New* 函数，并在 `pkg/app/wire.go` 中声明，便可以在要使用的结构体与对应 New* 函数增加依赖声明即可。