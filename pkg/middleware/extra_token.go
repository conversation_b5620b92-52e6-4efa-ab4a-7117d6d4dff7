package middleware

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/binary"
	"encoding/hex"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

var (
	key = []byte{'v', '8', 'd', '8', 'l', '4', 'g', '9', 'b', '1', 'x', '6', 'p', '7', 'z', '5'}
)

func ExtraToken() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.GetHeader("token")
		if token == "" {
			c.Status(http.StatusUnauthorized)
			c.Abort()
			return
		}

		t, err := decrypt(token)
		if err != nil {
			c.Status(http.StatusUnauthorized)
			c.Abort()
			return
		}

		if time.Since(time.Unix(t, 0)) > 10*time.Minute {
			c.Status(http.StatusUnauthorized)
			c.Abort()
			return
		}

	}
}

func encrypt(timestamp int64) (string, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("创建 cipher 失败: %w", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("创建 GCM 失败: %w", err)
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", fmt.Errorf("生成 nonce 失败: %w", err)
	}

	plaintextBytes := make([]byte, 8)
	binary.BigEndian.PutUint64(plaintextBytes, uint64(timestamp))

	ciphertext := gcm.Seal(nonce, nonce, plaintextBytes, nil)
	return hex.EncodeToString(ciphertext), nil
}

func decrypt(ciphertextHex string) (int64, error) {
	ciphertext, err := hex.DecodeString(ciphertextHex)
	if err != nil {
		return 0, fmt.Errorf("十六进制解码密文失败: %w", err)
	}

	block, err := aes.NewCipher(key)
	if err != nil {
		return 0, fmt.Errorf("创建 cipher 失败: %w", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return 0, fmt.Errorf("创建 GCM 失败: %w", err)
	}

	nonceSize := gcm.NonceSize()
	if len(ciphertext) < nonceSize {
		return 0, fmt.Errorf("密文过短，不足以包含 nonce")
	}

	nonce, actualCiphertext := ciphertext[:nonceSize], ciphertext[nonceSize:]

	decryptedBytes, err := gcm.Open(nil, nonce, actualCiphertext, nil)
	if err != nil {
		return 0, fmt.Errorf("解密失败 (可能是密钥错误或密文被篡改): %w", err)
	}

	if len(decryptedBytes) != 8 {
		return 0, fmt.Errorf("解密后的数据长度不符合 int64 (%d bytes found, expected 8)", len(decryptedBytes))
	}

	timestamp := int64(binary.BigEndian.Uint64(decryptedBytes))

	return timestamp, nil
}
