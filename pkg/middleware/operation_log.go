package middleware

import (
	"bytes"
	"io"
	"net/http"
	"tw_platform/pkg/model"
	"tw_platform/pkg/system/permission"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var postReplaceGet = map[string]struct{}{
	"/api/v1/device/name_list":        {},
	"/api/v1/device/history":          {},
	"/api/v1/gateway/is_accept_multi": {},
	"/api/v1/video/play":              {},
	"/api/v1/video/playback":          {},
	"/api/v1/video/stop_playing":      {},
	"/api//v1/video/keep_alive":       {},
	"/api/v1/video/preview":           {},

	//密码
	"/api/v1/user/change_password": {},
}

func HttpOperationLog(db *gorm.DB, l *zap.Logger, p *permission.Permissions) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if ctx.Request.Method == http.MethodGet {
			return
		}
		if ctx.Request.Header.Get("Content-Type") != "application/json" {
			//todo form-data
			return
		}

		_, ok := postReplaceGet[ctx.Request.URL.Path]
		if ok {
			return
		}

		body, err := io.ReadAll(ctx.Request.Body)
		if err != nil {
			l.Warn("read request body failed", zap.Error(err))
			return
		}
		ctx.Request.Body = io.NopCloser(bytes.NewBuffer(body))

		ctx.Next()

		var (
			uid = ctx.GetInt("uid")
			pid = p.ID(ctx.Request.Method, ctx.Request.URL.Path)
		)
		var detail = model.OperationLog{
			UserID:       uid,
			Param:        body,
			PermissionID: pid,
		}

		db.Model(detail).Create(&detail)
	}
}
