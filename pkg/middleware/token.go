package middleware

import (
	"fmt"
	"strings"
	"time"
	"tw_platform/internal/service"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/system/config"
	"tw_platform/pkg/utils"

	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

const (
	unauthorized = 401
)

func CheckToken(conf config.JWT, redis *redis.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.GetHeader("Authorization")
		if token == "" {
			token = c.Query("token")
		}
		if token == "" {
			response.FailWithCode(c, unauthorized, "token filed is empty")
			c.Abort()
			return
		}
		tokenArr := strings.Split(token, " ")
		if len(tokenArr) != 2 || tokenArr[0] != "Bearer" {
			response.FailWithCode(c, unauthorized, "token format error")
			c.Abort()
			return
		}
		token = tokenArr[1]
		var tokenInfo service.Token
		_, err := jwt.ParseWithClaims(token, &tokenInfo, func(t *jwt.Token) (interface{}, error) {
			return []byte(conf.SecretKey), nil
		})
		if err != nil {
			response.FailWithCode(c, unauthorized, "unauthorized")
			c.Abort()
			return
		}

		c.Set("uid", tokenInfo.ID)
		c.Set("rid", tokenInfo.RoleID)

		ctx, cancel := utils.RedisCtx()
		defer cancel()

		if !redis.SetNX(ctx, fmt.Sprintf("token_lock:%d", tokenInfo.ID), 1, time.Minute).Val() {
			c.Next()
			return
		}
		defer redis.Del(ctx, fmt.Sprintf("token_lock:%d", tokenInfo.ID))

		token, err = service.NewToken(tokenInfo.ID, tokenInfo.RoleID, conf)
		if err != nil {
			response.FailWithCode(c, unauthorized, err.Error())
			c.Abort()
			return
		}

		c.Header("new_token", token)
	}
}
