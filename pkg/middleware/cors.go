package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

var (
	allowHosts = map[string]string{
		"http://************:5173/": "http://************:5173",
		"http://localhost:5173/":    "http://localhost:5173",
		"http://**********:8082/":   "http://**********:8082",
	}
)

func Cors() gin.HandlerFunc {
	return func(c *gin.Context) {
		referer := c.Request.Header.Get("Referer")

		origin, ok := allowHosts[referer]
		if !ok {
			c.Next()
			return
		}

		c.<PERSON><PERSON>("Access-Control-Allow-Origin", origin)
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Content-Type,AccessToken,X-CSRF-Token, Authorization, Token,Custom-Path")
		c.<PERSON><PERSON>("Access-Control-Allow-Methods", "POST, GET, OPTIONS,PUT,DELETE,PATCH")
		c.<PERSON><PERSON>("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type")
		c.<PERSON><PERSON>("Access-Control-Allow-Credentials", "true")

		//放行所有OPTIONS方法
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
		}

		// 处理请求
		c.Next()
	}
}
