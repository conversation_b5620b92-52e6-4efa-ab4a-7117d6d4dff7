package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

var post2get = map[string]struct{}{
	"/api/v1/device/name_list":            {},
	"/api/v1/device/unit_name":            {},
	"/api/v1/device/history":              {},
	"/api/v1/video/play":                  {},
	"/api/v1/video/keep_alive":            {},
	"/api/v1/device/realtime_data_for_3d": {},
	"/api/v1/area/scene/update":           {},
	"/api/v1/user/list_by_id":             {},
}

const (
	visitorID = 9990
)

func Visitor() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if ctx.GetInt("rid") < visitorID {
			return
		}

		if _, ok := post2get[ctx.Request.URL.Path]; ok {
			return
		}

		if ctx.Request.Method == http.MethodGet {
			return
		}

		ctx.JSON(http.StatusOK, gin.H{
			"code": http.StatusForbidden,
			"msg":  "访客无权操作",
		})
		ctx.Abort()
		return
	}
}
