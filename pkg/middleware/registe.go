package middleware

import (
	"time"
	"tw_platform/internal/service"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/system/device_capture"

	"github.com/gin-gonic/gin"
)

const (
	HAS_EXPRIED = 30000
)

// 检查注册的中间件
func CheckExpireMiddle(dc *device_capture.DeviceCapture) gin.HandlerFunc {
	return func(c *gin.Context) {
		if service.ExpireTS == service.LOGREMAIN {
			c.Next()
			return
		}

		if service.ExpireTS <= time.Now().Unix() {
			if service.IsExpired == service.NOEXPIRED {
				service.IsExpired = service.HASEXPIRED
				dc.SetStatus(model.SetDeviceCaptureStatusReq{
					Status: model.DeviceCaptureStatusOff,
				})
			}

			response.FailWithCode(c, HAS_EXPRIED, "expired")
			c.Abort()
			return
		}
		c.Next()
	}
}
