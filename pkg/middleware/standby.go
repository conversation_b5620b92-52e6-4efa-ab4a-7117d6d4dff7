package middleware

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
	"tw_platform/pkg/model"
	"tw_platform/pkg/system/standby"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

var unHandle = map[string]struct{}{
	"/api/v1/device/name_list":            {},
	"/api/v1/device/unit_name":            {},
	"/api/v1/device/history":              {},
	"/api/v1/video/play":                  {},
	"/api/v1/video/keep_alive":            {},
	"/api/v1/device/realtime_data_for_3d": {},
	"/api/v1/login":                       {},

	"/api/v1/standby/info":         {},
	"/api/v1/standby/push_tables":  {},
	"/api/v1/standby/fetch_tables": {},
	"/api/v1/standby/sync":         {},
}

type requestInfo struct {
	host  string
	path  string
	token string
	body  *[]byte
}

type bufferedResponseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w *bufferedResponseWriter) Write(b []byte) (int, error) {
	count, err := w.body.Write(b)
	if err != nil {
		return count, err
	}

	return w.ResponseWriter.Write(b)
}

func (w *bufferedResponseWriter) WriteString(s string) (int, error) {
	count, err := w.body.WriteString(s)
	if err != nil {
		return count, err
	}
	return w.ResponseWriter.WriteString(s)
}

type ResponseT struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data any    `json:"data"`
}

const (
	successCode = 1
)

func Standby(ss *standby.StandbyStats, l *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.Method != http.MethodPost {
			return
		}

		_, ok := unHandle[c.Request.URL.Path]
		if ok {
			return
		}

		if !ss.Enabled() || ss.Role() == model.StandbyTypeSlave {
			return
		}

		body, err := io.ReadAll(c.Request.Body)
		if err != nil {
			l.Warn("read request body failed", zap.String("standby", err.Error()))
			return
		}
		c.Request.Body = io.NopCloser(bytes.NewBuffer(body))

		var (
			originalWriter = c.Writer
			buffer         = bytes.NewBuffer([]byte{})
			writer         = &bufferedResponseWriter{
				ResponseWriter: originalWriter,
				body:           buffer,
			}
		)

		c.Writer = writer

		c.Next()

		if c.Writer.Status() != http.StatusOK {
			return
		}
		var (
			resp ResponseT
		)

		err = json.NewDecoder(writer.body).
			Decode(&resp)
		if err != nil {
			return
		}

		if resp.Code != successCode {
			return
		}

		err = syncRequest(&requestInfo{
			host:  ss.Host(),
			path:  c.Request.URL.Path,
			token: c.GetHeader("Authorization"),
			body:  &body,
		})
		if err != nil {
			l.Warn("sync request failed", zap.Error(err))
		}
	}
}

func syncRequest(param *requestInfo) error {
	var (
		client = http.Client{
			Timeout: 5 * time.Second,
		}
		reader = bytes.NewReader(*param.body)
	)

	req, err := http.NewRequest(
		http.MethodPost,
		fmt.Sprintf("%s%s", param.host, param.path),
		reader,
	)
	req.Header.Set("Authorization", param.token)

	if err != nil {
		return err
	}

	resp, err := client.Do(req)
	if err != nil {
		return err
	}

	var respStruct struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	}
	err = json.NewDecoder(resp.Body).Decode(&respStruct)
	if err != nil {
		return err
	}
	if respStruct.Code != successCode {
		return fmt.Errorf("sync request failed, code: %d, msg: %s", respStruct.Code, respStruct.Msg)
	}

	return nil
}
