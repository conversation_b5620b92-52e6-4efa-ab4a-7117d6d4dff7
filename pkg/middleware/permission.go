package middleware

import (
	"net/http"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/response"
	"tw_platform/pkg/system/role_permission"

	"github.com/gin-gonic/gin"
)

func CheckPermission(rp *role_permission.RolePermissions) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			rid    = c.GetInt("rid")
			path   = c.Request.URL.Path
			method model.PermissionMethod
		)
		switch c.Request.Method {
		case http.MethodGet:
			method = model.PermissionMethodGet
		case http.MethodPost:
			method = model.PermissionMethodPost
		default:
			response.FailWithMsg(c, "method error")
			c.Abort()
			return
		}

		if !rp.Validate(rid, path, method) {
			response.FailWithMsg(c, "forbidden")
			c.Abort()
			return
		}
		c.Next()
	}
}
