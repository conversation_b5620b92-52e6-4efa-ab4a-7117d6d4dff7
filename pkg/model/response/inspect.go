package response

import "tw_platform/pkg/model"

type InspectList struct {
	model.Inspect
	AreaInfo        model.Area   `json:"area_info" gorm:"foreignKey:AreaID;references:ID"`
	CreatedUserInfo model.User   `json:"created_user_info" gorm:"foreignKey:CreatedUserID;references:ID"`
	CheckUserInfo   model.User   `json:"check_user_info" gorm:"foreignKey:CheckUserID;references:ID"`
	ExecuteUsers    []model.User `json:"execute_users" gorm:"many2many:inspect_execute_users;joinForeignKey:InspectID;joinReferences:UserID"`
	LeadingUsers    []model.User `json:"leadingusers" gorm:"many2many:inspect_leading_users;joinForeignKey:InspectID;joinReferences:UserID"`
}

type InspectPlanList struct {
	model.InspectPlan
	InspectInfo      model.Inspect            `json:"inspect_info" gorm:"foreignKey:InspectID;references:ID"`
	ExecutedUserInfo *model.User              `json:"executed_user_info,omitempty" gorm:"foreignKey:ExecutedUserID;references:ID"`
	Images           []model.InspectPlanImage `json:"images" gorm:"foreignKey:PlanID;references:ID"`
}
