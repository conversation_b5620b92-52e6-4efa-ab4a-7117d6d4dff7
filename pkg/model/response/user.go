package response

import "tw_platform/pkg/model"

type Login struct {
	Token    string `json:"token"`
	UserID   int    `json:"user_id"`
	Nickname string `json:"nickname"`
	RoleName string `json:"role_name"`
}

type QyLogin struct {
	Login
	Status model.UserStatus `json:"status"`
	Email  string           `json:"email"`
	Phone  string           `json:"phone"`
}

type UserList struct {
	model.User
	RoleInfo model.Role `json:"role_info" gorm:"foreignKey:RoleID;references:ID"`
}
