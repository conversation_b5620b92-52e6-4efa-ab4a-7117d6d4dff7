package response

import (
	"tw_platform/pkg/model"
)

// 获取门禁列表
type GetDoorListResItem struct {
	Config model.DoorInfo `json:"config"`
	Device model.DeviceWithAreaName
}
type GetDoorListRes []GetDoorListResItem

// 获取门的状态
type GetDoorStatusRes struct {
	DoorStatus int `json:"doorStatus"`
}

// 获取一体机人员列表
type GetDoorUserResItem struct {
	EmployeeNo     string `json:"employeeNo"`
	Name           string `json:"name"`
	Password       string `json:"password"`
	LocalUIRight   bool   `json:"localUIRight"`
	UserVerifyMode string `json:"userVerifyMode"`
}

type GetDoorUserRes []GetDoorUserResItem

// 人员卡片
type GetDoorCardResItem struct {
	CardNo   string `json:"cardNo"`
	CardType string `json:"cardType"`
}
type GetDoorCardRes []GetDoorCardResItem

//获取人员指纹

type GetFpDataResItem struct {
	FingerPrintID int    `json:"fingerPrintID"`
	FingerType    string `json:"fingerType"`
	FingerData    string `json:"fingerData"`
}
type GetFpDataRes []GetFpDataResItem

type GetFpDataProcessRes struct {
	TotalStatus int `json:"totalStatus"`
}

type GetFpDelProcessRes struct {
	DelFingerProcess string `json:"delFingerProcess"`
}

type GetFaceRes struct {
	Face string `json:"face"`
}
