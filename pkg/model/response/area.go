package response

import (
	"tw_platform/pkg/model"

	"gorm.io/datatypes"
)

type AreaTree struct {
	model.AreaWithTypes
	Children []AreaTree `json:"children"`
}

type AreaTreeOnly struct {
	model.Area
	Children []AreaTreeOnly `json:"children"`
}

type AreaTreeWithDevices struct {
	model.AreaWithDevices
	Children []AreaTreeWithDevices `json:"children"`
}

type AreaTreeWithDeviceConfigs struct {
	model.AreaWithDeviceConfigs
	Children []AreaTreeWithDeviceConfigs `json:"children"`
}

type GetPUE struct {
	PUE string `json:"pue"`
}

type PUEEveryHour struct {
	PUE  interface{} `json:"pue"`
	Time string      `json:"time"`
}

type AreaDefaultSceneType struct {
	ID        int    `json:"id" gorm:"column:id"`
	Name      string `json:"name" gorm:"column:name"`
	SceneType uint8  `json:"scene_type" gorm:"column:scene_type"`
}

type AreaTreeWithAlarmCounts struct {
	model.AreaWithAlarmCounts
	Children []AreaTreeWithAlarmCounts `json:"children"`
}

type AreaListWithAlarmed struct {
	model.Area
	IsAlarmed bool `json:"is_alarmed"`
}

type AreaTreeWithDevicesByBtIDDeviceUnit struct {
	ID       int                `json:"id"`
	Name     string             `json:"name"`
	Value    string             `json:"value"`
	Status   model.DeviceStatus `json:"status"`
	SubValue datatypes.JSON     `json:"sub_value"`
	Flag     string             `json:"flag"`
}

type AreaTreeWithDevicesByBtIDDevice struct {
	ID     int                                   `json:"id"`
	Name   string                                `json:"name"`
	Status model.DeviceStatus                    `json:"status"`
	Data   []AreaTreeWithDevicesByBtIDDeviceUnit `json:"units"`
}

type AreaTreeWithDevicesByBtID struct {
	ID       int                               `json:"id"`
	Name     string                            `json:"name"`
	ParentID int                               `json:"-"`
	Children []AreaTreeWithDevicesByBtID       `json:"children"`
	Devices  []AreaTreeWithDevicesByBtIDDevice `json:"devices"`
}

type AreaTreeWithDevicesByBtIDResp struct {
	Tree         []AreaTreeWithDevicesByBtID `json:"tree"`
	NormalTotal  int                         `json:"normal_total"`
	AlarmedTotal int                         `json:"alarmed_total"`
}

type RealtimeAreaStatus struct {
	ID        int  `json:"id"`
	IsAlarmed bool `json:"is_alarmed"`
}
