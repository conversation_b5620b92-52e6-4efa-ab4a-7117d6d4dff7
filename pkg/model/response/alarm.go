package response

import (
	"tw_platform/pkg/model"
)

type RealtimeList struct {
	model.Alarm
	BusinessTypeID   int    `json:"business_type_id" gorm:"column:business_type_id"`
	BusinessTypeName string `json:"business_type_name" gorm:"column:business_type_name"`
}

type AlarmTotalOfTime struct {
	Total int    `json:"total" gorm:"total"`
	Time  string `json:"time" gorm:"time"`
}

type AlarmTotalOfTimeList []AlarmTotalOfTime

func (a AlarmTotalOfTimeList) Len() int {
	return len(a)
}
func (a AlarmTotalOfTimeList) Less(i, j int) bool {
	if a[i].Time > a[j].Time {
		return false
	}
	return true
}

func (a AlarmTotalOfTimeList) Swap(i, j int) {
	a[i], a[j] = a[j], a[i]
}

type AlarmListPayload struct {
	Type        model.AlarmStatus `json:"type"`
	Path        string            `json:"path"`
	model.Alarm `json:"alarm"`
}
