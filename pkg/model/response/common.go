package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

const (
	msg     = "success"
	errMsg  = "failure"
	code    = 1
	errCode = 10
)

type response struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

func Ok(c *gin.Context) {
	c.JSON(http.StatusOK, response{
		Code: code,
		Msg:  msg,
	})
}

func OkWithMsg(c *gin.Context, msg string) {
	c.JSON(http.StatusOK, response{
		Code: code,
		Msg:  msg,
	})
}

func OkWithData(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, response{
		Code: code,
		Msg:  msg,
		Data: data,
	})
}

func OkWithPage(c *gin.Context, data interface{}, total int64, page, size int) {
	c.JSON(http.StatusOK, response{
		Code: code,
		Msg:  msg,
		Data: gin.H{
			"list":  data,
			"page":  page,
			"total": total,
			"size":  size,
		},
	})
}

func Fail(c *gin.Context) {
	c.J<PERSON>(http.StatusOK, response{
		Code: errCode,
		Msg:  errMsg,
	})
}

func FailWithMsg(c *gin.Context, msg string) {
	c.JSON(http.StatusOK, response{
		Code: errCode,
		Msg:  msg,
	})
}

func FailWithError(c *gin.Context, err error) {
	c.JSON(http.StatusOK, response{
		Code: errCode,
		Msg:  err.Error(),
	})
}

func FailWithCode(c *gin.Context, code int, msg string) {
	c.JSON(http.StatusOK, response{
		Code: code,
		Msg:  msg,
	})
}
