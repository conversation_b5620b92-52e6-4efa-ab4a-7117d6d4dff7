package response

import (
	"time"
)

type UpdateWebPage struct {
	Total     int `json:"total"`
	Successed int `json:"successed"`
	Failured  int `json:"failured"`
	// FileName  string `json:"fileName"`
	Err error `json:"-"`
}

type Statistic struct {
	DeviceTotal int64          `json:"device_total"`
	AlarmTotal  int64          `json:"alarm_total"`
	TicketTotal int64          `json:"ticket_total"`
	Duration    time.Duration  `json:"duration"`
	Alarms      []RealtimeList `json:"alarms"`
}

type VersionInfo struct {
	CommitID  string `json:"commit_id"`
	BuildDate string `json:"build_date"`
}

type SystemStatisticApp struct {
	AlarmTotal     int64 `json:"alarm_total"`
	TicketTotal    int64 `json:"ticket_total"`
	InspectTotal   int64 `json:"inspect_total"`
	GatewayTotal   int64 `json:"gateway_total"`
	DeviceTotal    int64 `json:"device_total"`
	SubSystemTotal int64 `json:"sub_system_total"`
}
