package response

import (
	"time"
	"tw_platform/pkg/model"
)

type LightScheduleStatistic struct {
	DeviceID       int    `json:"device_id"`
	UnitID         int    `json:"unit_id"`
	BusinessTypeID int    `json:"business_type_id"`
	Value          string `json:"value"`
	LightCount     int    `json:"light_count"`
}

type LightScheduleAction struct {
	Name   string     `json:"name"`
	Value  string     `json:"value"`
	ExecAt *time.Time `json:"exec_at"`
}

type LightScheduleStatisticResp struct {
	Statistics []LightScheduleStatistic    `json:"statistics"`
	Schedules  []model.LightSchedule       `json:"schedules"`
	Actions    []LightScheduleAction       `json:"actions"`
}
