package response

import (
	"tw_platform/pkg/model"
)

type CreateNotificationGroup struct {
	model.NotificationGroup
	NotificationUsers []model.NotificationUsers `json:"notification_users"`
}
type ListNotificationGroup struct {
	model.NotificationGroup
	NotificationUsers []NotificationUsersWithName `json:"notification_users"`
}
type ListNotificationGroupDetail struct {
	model.NotificationGroup
	NotificationUsers []model.User
}
type NotificationUsersWithName struct {
	model.NotificationUsers
	Name string `json:"name"`
}
