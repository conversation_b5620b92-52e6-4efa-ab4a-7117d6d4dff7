package response

import (
	"time"
	"tw_platform/pkg/model"
)

type PlayVideoResponse struct {
	Url       string `json:"url"`
	ProcessCh string `json:"process_ch"`
}
type VideoListResponse struct {
	ID             int                  `json:"id" `
	Type           string               `json:"type"`
	Activity       model.DeviceActivity `json:"activity" `
	Status         model.DeviceStatus   `json:"status" `
	AreaID         int                  `json:"area_id" `
	AreaName       string               `json:"area_name"`
	BusinessTypeID int                  `json:"business_type_id" `
	CreatedAt      *time.Time           `json:"created_at" `
	UpdatedAt      *time.Time           `json:"updated_at" `
	DeviceName     string               `json:"device_name"`
	Brand          string               `json:"brand" `
	ChannelID      int                  `json:"channel_id"`
	IP             string               `json:"ip"`
	Name           string               `json:"name" `
	Password       string               `json:"password"`
	Stream         string               `json:"stream" `
}

type UpdateImage struct {
	Name  string `json:"name"`
	Image string `json:"image"`
}
type VideoConfigWithDeviceName struct {
	Id     int                `json:"id"`
	Name   string             `json:"name"`
	Type   model.DeviceType   `json:"type"`
	Config model.DeviceConfig `json:"config,omitempty" gorm:"column:config;serializer:json"`
}
