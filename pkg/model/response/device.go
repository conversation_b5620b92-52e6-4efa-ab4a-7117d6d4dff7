package response

import (
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"

	jsoniter "github.com/json-iterator/go"
	"gorm.io/datatypes"
)

type DeviceHistoryDataUnit struct {
	Time  string  `json:"time"`
	Value float64 `json:"value"`
}

type DeviceHistoryData struct {
	UnitID string                  `json:"unit_id"`
	Name   string                  `json:"name"`
	Data   []DeviceHistoryDataUnit `json:"data"`
}

type DeviceAlarmUnit struct {
	ID           int      `json:"id"`
	Name         string   `json:"name"`
	Activity     bool     `json:"activity"`
	AlarmEnable  bool     `json:"alarm_enable"`
	AlarmLevel   string   `json:"alarm_level"`
	FilterCnt    int      `json:"filter_cnt"`
	High         int      `json:"high"`
	HighName     string   `json:"high_name"`
	HighHyse     int      `json:"high_hyse"`
	HighHigh     int      `json:"high_high"`
	HighHighName string   `json:"high_high_name"`
	Low          int      `json:"low"`
	LowName      string   `json:"low_name"`
	LowHyse      int      `json:"low_hyse"`
	LowLow       int      `json:"low_low"`
	LowLowName   string   `json:"low_low_name"`
	Priority     int      `json:"priority"`
	SaveEnable   bool     `json:"save_enable"`
	SaveTime     int      `json:"save_time"`
	EffectTime   []string `json:"effect_time"`
	Type         string   `json:"type"`
	Action       int      `json:"action"`
}

type DeviceAlarmLimit struct {
	ID    int                     `json:"id"`
	Units map[int]DeviceAlarmUnit `json:"units"`
}

func (dal DeviceAlarmLimit) MarshalBinary() ([]byte, error) {
	return jsoniter.Marshal(dal)
}

func (dal *DeviceAlarmLimit) UnmarshalBinary(data []byte) error {
	return jsoniter.Unmarshal(data, dal)
}

type DeviceDataUnitFor3D struct {
	Point  int                `json:"point"`
	Name   string             `json:"name"`
	Value  string             `json:"value"`
	Status model.DeviceStatus `json:"status"`
}

type DeviceDataFor3D struct {
	Type   string                `json:"type"`
	ID     int                   `json:"id"`
	Name   string                `json:"name"`
	Status model.DeviceStatus    `json:"status"`
	Units  []DeviceDataUnitFor3D `json:"units"`
}

type DeviceData struct {
	request.DeviceData
}

func (dd DeviceData) MarshalBinary() ([]byte, error) {
	return jsoniter.Marshal(dd)
}

func (dd *DeviceData) UnmarshalBinary(data []byte) error {
	return jsoniter.Unmarshal(data, dd)
}

type RealtimeDeviceStatus struct {
	Type   string             `json:"type"`
	ID     int                `json:"id"`
	Status model.DeviceStatus `json:"status"`
}

func (ds RealtimeDeviceStatus) MarshalBinary() ([]byte, error) {
	return jsoniter.Marshal(ds)
}

func (ds *RealtimeDeviceStatus) UnmarshalBinary(data []byte) error {
	return jsoniter.Unmarshal(data, ds)
}

type DeviceNames struct {
	Devices []model.Device   `json:"devices"`
	Units   []DeviceUnitName `json:"units"`
}

type DeviceUnitName struct {
	ID       int            `json:"id" gorm:"column:id"`
	Name     string         `json:"name" gorm:"column:name"`
	UnitID   int            `json:"unit_id" gorm:"column:unit_id"`
	SubValue datatypes.JSON `json:"sub_value" gorm:"column:sub_value"`
	Activity bool           `json:"activity" gorm:"column:activity"`
	Flag     string         `json:"flag" gorm:"column:flag"`
}

type DeviceList struct {
	model.Device
	Departments []model.DepartmentDevice `json:"departments" gorm:"foreignKey:DeviceID;references:ID"`
	PortID      int                      `json:"port_id" gorm:"column:port_id"`
	PortName    string                   `json:"port_name" gorm:"column:port_name"`
	Port        string                   `json:"port" gorm:"column:port"`
	GatewayName string                   `json:"gateway_name" gorm:"column:gateway_name"`
	GatewayIP   string                   `json:"gateway_ip" gorm:"column:gateway_ip"`
}

type DoorDeviceList struct {
	model.Device
	DeviceType string `json:"device_type" gorm:"column:device_type"`
}

type DeviceConfigWithName struct {
	model.Config
	DeviceInfo model.Device `json:"device_info" gorm:"foreignKey:ID;references:ID"`
}

type DeviceListWithConfig struct {
	model.DeviceWithConfig
	Departments   []model.DepartmentDevice `json:"-" gorm:"foreignKey:DeviceID;references:ID"`
	DepartmentIDs []int                    `json:"department_ids" gorm:"-"`
}

type DeviceDetail struct {
	model.Device
	DepartmentIDs []int `json:"department_ids" gorm:"-"`
}
