package response

import "tw_platform/pkg/model"

type IsAccept struct {
	Connected bool `json:"connected"`
}

type IsAcceptMulti struct {
	Port      string `json:"port"`
	Connected bool   `json:"connected"`
}
type OPCNodeVariable struct {
	VariableInfo string `json:"variable_info"`
}

type ScanGateway struct {
	IP      string `json:"ip"`
	Mac     string `json:"mac"`
	Version string `json:"version"`
	Type    string `json:"type"`
}

type AdapterList struct {
	Name  string `json:"name"`
	Addrs []string
}

type GatewayStatistic struct {
	Total   int64           `json:"total"`
	Online  int64           `json:"online"`
	Offline int64           `json:"offline"`
	List    []model.Gateway `json:"list"`
}
