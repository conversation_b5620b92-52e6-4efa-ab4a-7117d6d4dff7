package model

type BranchInfoItem struct {
	Name     string `json:"name" gorm:"column:name"`
	Id       int    `json:"id" gorm:"column:id"`
	AreaID   int    `json:"area_id" gorm:"column:area_id"`
	AreaName string `json:"area_name" gorm:"column:area_name"`
}

type Video struct {
	ID         int    `json:"id" gorm:"column:id;primaryKey"`
	Type       string `json:"type" gorm:"column:type;not null;type:varchar(32)"`
	Brand      string `json:"brand" gorm:"column:brand;type:varchar(256)"`
	DeviceName string `json:"device_name" gorm:"column:device_name;type:varchar(64)"`
	ChannelID  int    `json:"channel_id" gorm:"column:channel_id;not null;type:int"`
	Ip         string `json:"ip" gorm:"column:ip;type:varchar(64)"`
	Name       string `json:"name" gorm:"column:name;type:varchar(64)"`
	Password   string `json:"password" gorm:"column:password;type:varchar(64)"`
	Stream     string `json:"stream" gorm:"column:stream;type:varchar(64)"`
}

type VideoConfig struct {
	ID     int          `json:"id" gorm:"column:id"`
	Type   DeviceType   `json:"type" gorm:"column:type"`
	Config DeviceConfig `json:"config,omitempty" gorm:"column:config;serializer:json"`
}

func (Video) TableName() string {
	return "videos"
}
