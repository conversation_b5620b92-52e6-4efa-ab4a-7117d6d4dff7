package model

import "time"

type Day struct {
	ExecutionTime  string `json:"execution_time"`
	ExpirationTime int    `json:"expiration_time"` //分钟
}
type Always struct {
	Type     int       `json:"type"`      //0 全时间  1 时间段
	Cycle    int       `json:"cycle"`     //分钟
	TimeSlot *TimeSlot `json:"time_slot"` //
}
type TimeSlot struct {
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
}
type CronTaskCondition struct {
	TimeType string  `json:"time_type"` //HOUR DAY  WEEK MONTH ALWAYS
	Day      *Day    `json:"day"`       //每天定点执行
	Always   *Always `json:"always"`    //周期执行
	Week     []int   `json:"week"`      //每周几
	// ExecutionTime     string    `json:"execution_time"`
	NextExecutionTime time.Time `json:"next_execution_time"`
}
type CronTaskAction struct {
	ActionType   string `json:"action_type" binding:"required"` //NOTICE LINK
	NoticeGroups []int  `json:"notice_groups"`
	LinkId       int    `json:"link_id"`
}
type CronTask struct {
	ID          int        `gorm:"column:id;primaryKey" json:"id"`
	Name        string     `gorm:"column:name;type:varchar(64);not null" json:"name"`
	Enabled     string     `gorm:"column:enabled;not null;type:varchar(8);" json:"enabled"` // ON OFF
	Description *string    `gorm:"column:description;type:varchar(255)" json:"description"`
	CreatedAt   *time.Time `gorm:"column:created_at;autoCreateTime;not null;type:timestamptz" json:"created_at"`
	UpdatedAt   *time.Time `gorm:"column:updated_at;autoUpdateTime;not null;type:timestamptz" json:"updated_at"`
}

func (CronTask) TableName() string {
	return "cron_task"
}

type CronTaskConfig struct {
	ID        int                `gorm:"column:id;primaryKey" json:"id"`
	Condition *CronTaskCondition `json:"condition,omitempty" gorm:"column:condition;serializer:json;type:jsonb"`
	Action    *CronTaskAction    `json:"action,omitempty" gorm:"column:action;serializer:json;type:jsonb"`
}

func (CronTaskConfig) TableName() string {
	return "cron_task_configs"
}

//	type CronTaskAll struct {
//		CronTask
//		Condition *CronTaskCondition `json:"condition,omitempty" gorm:"column:condition;serializer:json"`
//		Action    *CronTaskAction    `json:"action,omitempty" gorm:"column:action;serializer:json"`
//	}
type CronTaskActionWithName struct {
	ActionType        string   `json:"action_type" binding:"required"` //NOTICE LINK
	NoticeGroups      []int    `json:"notice_groups"`
	LinkId            int      `json:"link_id"`
	LinkName          string   `json:"link_name"`
	NoticeGroupsNames []string `json:"notice_groups_names"`
}
type CronTaskAll struct {
	CronTask
	Condition *CronTaskCondition      `json:"condition,omitempty" gorm:"column:condition;serializer:json"`
	Action    *CronTaskActionWithName `json:"action,omitempty" gorm:"column:action;serializer:json"`
}
