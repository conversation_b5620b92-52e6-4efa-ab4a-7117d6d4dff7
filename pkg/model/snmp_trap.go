package model

type Module struct {
	ModuleId   int64  `json:"module_id"`
	ModuleName string `json:"module_name"`
}
type SnmpConfig struct {
	DevId  *int     `json:"dev_id,omitempty"`
	Module []Module `json:"module,omitempty"`
}
type SnmpTrap struct {
	ID       int          `gorm:"column:id;primaryKey" json:"id"`
	Name     string       `gorm:"column:name;type:varchar(64)" json:"name"`
	Port     string       `gorm:"column:port;not null;type:varchar(64)" json:"port"`
	Ip       string       `gorm:"column:ip;type:varchar(64)" json:"ip"`
	Type     int          `gorm:"column:type;type:int" json:"type"`
	Original bool         `gorm:"column:original;type:bool" json:"original"` //是否原文推送
	Config   []SnmpConfig `json:"config,omitempty" gorm:"column:config;serializer:json;type:jsonb"`
}

func (SnmpTrap) TableName() string {
	return "snmp_trap"
}
