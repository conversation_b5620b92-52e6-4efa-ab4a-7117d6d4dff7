package model

import "time"

const (
	RoleIDAdmin = 1
)

type Role struct {
	ID        int        `json:"id,omitempty" gorm:"column:id;primaryKey"`
	Name      string     `json:"name,omitempty" gorm:"column:name;type:varchar(64);not null"`
	CreatedAt *time.Time `json:"created_at,omitempty" gorm:"column:created_at;autoCreatetime;not null;type:timestamptz"`
	UpdatedAt *time.Time `json:"updated_at,omitempty" gorm:"column:updated_at;autoUpdatetime;not null;type:timestamptz"`
}

func (r *Role) TableName() string {
	return "roles"
}

type RoleMenu struct {
	RoleID int `json:"role_id" gorm:"column:role_id;primaryKey"`
	MenuID int `json:"menu_id" gorm:"column:menu_id;primaryKey"`
}

func (r *RoleMenu) TableName() string {
	return "role_menus"
}

type RoleWithPermissions struct {
	Role
	Permissions []Permission `gorm:"many2many:role_permissions;joinForeignKey:RoleID;joinReferences:PermissionID"`
}
