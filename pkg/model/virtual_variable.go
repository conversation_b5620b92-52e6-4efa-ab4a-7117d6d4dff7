package model

import "time"

type VirtualUnit struct {
	Id    int    `json:"id"`
	Name  string `json:"name"`
	Value string `json:"value"`
}
type VirtualDevice struct {
	ID        int           `gorm:"column:id;primaryKey" json:"id"`
	Name      string        `gorm:"column:name" json:"name"`
	Unit      []VirtualUnit `json:"unit,omitempty" gorm:"column:unit;serializer:json"`
	CreatedAt *time.Time    `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt *time.Time    `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

func (VirtualDevice) TableName() string {
	return "virtual_devices"
}
