package model

import (
	"time"
)

type GatewayStatus = uint8

const (
	GatewayStatusOnline GatewayStatus = iota + 1
	GatewayStatusOffline
)

type GatewayType uint8

const (
	GatewayTypePceM   GatewayType = iota + 1 //存在flash_id字段
	GatewayTypeModbus                        //存在ip和port字段
	GatewayTypeSNMP                          //todo
	GatewayTypeOPC                           // opc网关
	GatewayTypeReport                        //pce-m主动上报设备
)

type GatewayPortType uint8

const (
	PortTypeModbus GatewayPortType = iota + 1
	PortTypePceMProto
	PortTypePceMBoard
	PortTypePceMNet
)

type Gateway struct {
	ID        int           `json:"id,omitempty" gorm:"column:id;primaryKey"`
	Name      string        `json:"name,omitempty" gorm:"column:name;type:varchar(64);not null"`
	Version   string        `json:"version,omitempty" gorm:"column:version;type:varchar(64);not null"`
	Status    GatewayStatus `json:"status,omitempty" gorm:"column:status;type:int;not null"`
	Type      GatewayType   `json:"type,omitempty" gorm:"column:type;type:int;not null"`
	FlashID   *string       `json:"flash_id,omitempty" gorm:"column:flash_id;type:varchar(64)"`
	IP        *string       `json:"ip,omitempty" gorm:"column:ip;type:varchar(32)"`
	Model     string        `json:"model,omitempty" gorm:"column:model;type:varchar(64);not null"`
	CreatedAt *time.Time    `json:"created_at,omitempty" gorm:"column:created_at;autoCreatetime;not null;type:timestamptz"`
	UpdatedAt *time.Time    `json:"updated_at,omitempty" gorm:"column:updated_at;autoUpdatetime;not null;type:timestamptz"`
}

func (Gateway) TableName() string {
	return "gateways"
}

type GatewayPort struct {
	ID        int             `json:"id" gorm:"column:id;primaryKey"`
	GatewayID int             `json:"-" gorm:"column:gateway_id;type:int;index"`
	Name      string          `json:"name" gorm:"column:name;type:varchar(64);not null"`
	Port      string          `json:"port" gorm:"column:port;type:varchar(64);not null"`
	Type      GatewayPortType `json:"type" gorm:"column:type;type:int;not null"`
}

func (GatewayPort) TableName() string {
	return "gateway_ports"
}

type GatewayWithPorts struct {
	Gateway
	Ports     []GatewayPort `json:"-" gorm:"foreignKey:GatewayID;references:ID"`
	PortTotal int           `json:"port_total" gorm:"-"`
}

type PortWithGateway struct {
	GatewayPort
	GatewayInfo Gateway `json:"gateway" gorm:"foreignKey:ID;references:gateway_id"`
}

type GatewayPortDevices struct {
	Gateway
	Ports []struct {
		GatewayPort
		Devices []Device `json:"devices" gorm:"foreignKey:PortID;references:ID"`
	} `json:"ports" gorm:"foreignKey:GatewayID;references:ID"`
}

type GatewayAlarmBinding struct {
	GatewayID int `json:"gateway_id" gorm:"column:gateway_id;primary_key;type:int"`
	BindingID int `json:"binding_id" gorm:"column:binding_id;primary_key;type:int"`
}

func (GatewayAlarmBinding) TableName() string {
	return "gateway_alarm_bindings"
}

func (gab GatewayAlarmBinding) Type() string {
	return "gateway"
}

func (gab GatewayAlarmBinding) Trigger() int {
	return gab.GatewayID
}

func (gab GatewayAlarmBinding) DOID() int {
	return gab.BindingID
}

type AreaGateway struct {
	AreaID    int `json:"area_id" gorm:"column:area_id;primaryKey"`
	GatewayID int `json:"gateway_id" gorm:"column:gateway_id;primaryKey;index:gateway_id_idx"`
}

func (AreaGateway) TableName() string {
	return "area_gateways"
}

type DepartmentGateway struct {
	DepartmentID int `json:"department_id" gorm:"column:department_id;primaryKey"`
	GatewayID    int `json:"gateway_id" gorm:"column:gateway_id;primaryKey;index:gateway_id_idx"`
}

func (DepartmentGateway) TableName() string {
	return "department_gateways"
}
