package model

type AlarmConfig struct {
	AlarmType []string `json:"alarmType,omitempty"` //alarm recovery
	Priority  int      `json:"priority,omitempty"`  //优先级
}
type AlarmGroup struct {
	ID          int         `gorm:"column:id;primaryKey" json:"id"`
	Name        string      `gorm:"column:name;not null;type:varchar(64)" json:"name"`
	Enabled     string      `gorm:"column:enabled;not null;type:varchar(8);" json:"enabled"`                      // ON OFF
	Config      AlarmConfig `json:"alarm_config,omitempty" gorm:"column:alarm_config;serializer:json;type:jsonb"` // 通知配置
	Description *string     `gorm:"column:description;type:varchar(255)" json:"description"`
}

func (AlarmGroup) TableName() string {
	return "alarm_groups"
}

type AlarmGroupNotification struct {
	AlarmGroupId        int `gorm:"column:alarm_group_id;primaryKey" json:"-"`
	NotificationGroupId int `gorm:"column:notification_group_id;primaryKey" json:"notification_group_id"`
}

func (AlarmGroupNotification) TableName() string {
	return "alarm_notification_groups"
}

type AlarmGroupDevice struct {
	AlarmGroupId int `gorm:"column:alarm_group_id;primaryKey" json:"-"`
	DeviceId     int `gorm:"column:device_id;primaryKey" json:"device_id"`
}

func (AlarmGroupDevice) TableName() string {
	return "alarm_group_devices"
}

type AlarmGroupDetail struct {
	AlarmGroup
	Notifications []AlarmGroupWithNotificationName `json:"notifications,omitempty" gorm:"foreignKey:AlarmGroupId;references:ID"`
	Devices       []AlarmGroupWithDeviceName       `json:"devices,omitempty" gorm:"foreignKey:AlarmGroupId;references:ID"`
}

type AlarmGroupWithNotificationName struct {
	AlarmGroupNotification
	Name string `gorm:"column:name" json:"name"`
}

type AlarmGroupWithDeviceName struct {
	AlarmGroupDevice
	Name string `gorm:"column:name" json:"name"`
}
