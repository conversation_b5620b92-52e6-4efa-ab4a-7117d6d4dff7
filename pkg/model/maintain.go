package model

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

type MaintainPlanStatus = uint8

const (
	MaintainPlanStatusCreated MaintainPlanStatus = iota + 1
	MaintainPlanStatusCompanied
)

type Maintain struct {
	ID        int        `json:"id" gorm:"column:id;primaryKey"`
	DeviceID  int        `json:"device_id" gorm:"column:device_id;not null;type:int;index"`
	Title     string     `json:"title" gorm:"column:title;not null;type:varchar(128)"`
	Content   string     `json:"content" gorm:"column:content;not null;type:varchar(512)"`
	Amount    int        `json:"amount" gorm:"column:amount;not null;type:int"`
	SignedAt  time.Time  `json:"signed_at" gorm:"column:signed_at;not null;type:timestamptz"`
	Contract  *string    `json:"contract" gorm:"column:contract;type:varchar(512)"`
	Duration  int        `json:"duration" gorm:"column:duration;not null;type:int"`
	CreatedAt *time.Time `json:"created_at" gorm:"column:created_at;autoCreateTime;not null;type:timestamptz"`
	UpdatedAt *time.Time `json:"updated_at" gorm:"column:updated_at;autoUpdateTime;not null;type:timestamptz"`
}

func (Maintain) TableName() string {
	return "maintains"
}

func (m *Maintain) AfterFind(tx *gorm.DB) error {
	if m.Contract == nil {
		return nil
	}
	path := fmt.Sprint(StaticContract, *m.Contract)
	m.Contract = &path
	return nil
}

type MaintainPlan struct {
	ID            int                `json:"id" gorm:"column:id;primaryKey"`
	MaintainID    int                `json:"-" gorm:"column:maintain_id;not null;type:int;index"`
	Status        MaintainPlanStatus `json:"status" gorm:"column:status;not null;type:int"`
	Result        *string            `json:"result" gorm:"column:result;type:varchar(512)"`
	Suggestion    *string            `json:"suggestion" gorm:"column:suggestion;type:varchar(512)"`
	Comment       *string            `json:"comment" gorm:"column:comment;type:varchar(512)"`
	ExecuteDate   time.Time          `json:"execute_date" gorm:"column:execute_date;not null;type:timestamptz"`
	ExecuteUserID *int               `json:"-" gorm:"column:execute_user_id;type:int"`
	CreatedAt     *time.Time         `json:"created_at" gorm:"column:created_at;autoCreateTime;not null;type:timestamptz"`
	ExecutedAt    *time.Time         `json:"execute_at" gorm:"column:executed_at;type:date"`
}

func (MaintainPlan) TableName() string {
	return "maintain_plans"
}
