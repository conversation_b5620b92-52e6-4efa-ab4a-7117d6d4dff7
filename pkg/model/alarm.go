package model

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

type AlarmType = uint8

const (
	AlarmTypeCommon AlarmType = iota + 1
	AlarmTypeGateway
	AlarmTypeAsset
)

type AlarmStatus = uint8

const (
	AlarmStatusHappened AlarmStatus = iota + 1
	AlarmStatusRecovered
	AlarmStatusHandled
	AlarmStatusBlocked
)

type Alarm struct {
	ID          int         `json:"id" gorm:"column:id;primaryKey;type:int;not null"`
	Type        AlarmType   `json:"type" gorm:"column:type;type:int;not null"`
	Status      AlarmStatus `json:"status" gorm:"column:status;type:int;not null"`
	GatewayID   *int        `json:"gateway_id,omitempty" gorm:"column:gateway_id;type:int;index"`
	DeviceID    *int        `json:"device_id,omitempty" gorm:"column:device_id;type:int;index"`
	UnitID      *int        `json:"unit_id,omitempty" gorm:"column:unit_id;type:int"`
	DeviceName  *string     `json:"device_name,omitempty" gorm:"column:device_name;type:varchar(64)"`
	UnitName    *string     `json:"unit_name,omitempty" gorm:"column:unit_name;type:varchar(64)"`
	GatewayName *string     `json:"gateway_name,omitempty" gorm:"column:gateway_name;type:varchar(64)"`
	Value       *string     `json:"value,omitempty" gorm:"column:value;type:varchar(16)"`
	Flag        *string     `json:"flag,omitempty" gorm:"column:flag;type:varchar(16)"`
	AreaID      *int        `json:"area_id,omitempty" gorm:"column:area_id;type:int;index"`
	AreaName    *string     `json:"area_name,omitempty" gorm:"column:area_name;type:varchar(64)"`
	Level       string      `json:"level,omitempty" gorm:"column:level;type:varchar(16)"`
	Message     string      `json:"message,omitempty" gorm:"column:message;type:varchar(255)"`
	CreatedAt   *time.Time  `json:"created_at,omitempty" gorm:"column:created_at;autoCreatetime;not null;type:timestamptz"`
	UpdatedAt   *time.Time  `json:"updated_at,omitempty" gorm:"column:updated_at;autoUpdatetime;not null;type:timestamptz"`
}

func (Alarm) TableName() string {
	return "alarms"
}

type HistoryAlarm struct {
	AlarmID int `json:"alarm_id" gorm:"column:alarm_id;primaryKey"`
	Alarm
}

func (HistoryAlarm) TableName() string {
	return "history_alarms"
}

type AlarmTimeLine struct {
	ID        int         `json:"id" gorm:"column:id;primaryKey"`
	AlarmID   int         `json:"-" gorm:"column:alarm_id;type:int;not null;index"`
	Status    AlarmStatus `json:"status" gorm:"column:status;type:int;not null"`
	CreatedAt *time.Time  `json:"created_at" gorm:"column:created_at;autoCreatetime;not null;type:timestamptz"`
}

func (*AlarmTimeLine) TableName() string {
	return "history_alarm_timelines"
}

type HistoryAlarmWithTimeline struct {
	HistoryAlarm
	Timelines []AlarmTimeLine `json:"timelines" gorm:"foreignKey:AlarmID;references:ID"`
	TicketID  int             `json:"ticket_id" gorm:"column:ticket_id"`
}

type HistoryAlarmTicket struct {
	HistoryAlarmID int `json:"history_alarm_id" gorm:"history_alarm_id;primaryKey"`
	TicketID       int `json:"ticket_id" gorm:"ticket_id;primaryKey"`
}

func (HistoryAlarmTicket) TableName() string {
	return "history_alarm_tickets"
}

type HistoryAlarmExport struct {
	HistoryAlarm
	Timelines []AlarmTimeLine `json:"timelines" gorm:"foreignKey:AlarmID;references:ID"`
}

type ExportAlarm struct {
	ID        int        `json:"id" gorm:"column:id;primaryKey;type:int;not null"`
	Path      string     `json:"path" gorm:"column:path;type:varchar(255);not null"`
	Month     string     `json:"month" gorm:"column:month;type:varchar(8);not null"`
	CreatedAt *time.Time `json:"created_at" gorm:"column:created_at;autoCreatetime;not null;type:timestamptz"`
}

func (ExportAlarm) TableName() string {
	return "export_alarms"
}

func (ea *ExportAlarm) AfterFind(tx *gorm.DB) (err error) {
	ea.Path = fmt.Sprintf("%s%s", StaticExportAlarm, ea.Path)
	return nil
}
