package model

import "time"

type LinkageCapture struct {
	ID          int        `json:"id,omitempty" gorm:"column:id;primaryKey"`
	Image       string     `json:"image,omitempty" gorm:"column:image;type:varchar(64)"`
	DeviceID    int        `json:"dev_id,omitempty" gorm:"column:dev_id;type:int"`
	DeviceName  string     `json:"dev_name,omitempty" gorm:"column:dev_name;type:varchar(64)"`
	Description string     `json:"description,omitempty" gorm:"column:description;type:varchar(255)"`
	CreatedAt   *time.Time `json:"created_at" gorm:"column:created_at;autoCreatetime;not null;type:timestamptz"`
}

func (LinkageCapture) TableName() string {
	return "linkage_capture"
}
