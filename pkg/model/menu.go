package model

import "time"

type Menu struct {
	ID        int        `json:"id" gorm:"column:id;primaryKey"`
	Name      string     `json:"name,omitempty" gorm:"column:name;type:varchar(64);not null"`
	ParentID  int        `json:"parent_id,omitempty" gorm:"column:parent_id;type:int;not null;index"`
	Type      int8       `json:"type,omitempty" gorm:"column:type;not null;type:int"`
	Path      string     `json:"path,omitempty" gorm:"column:path;type:varchar(255);not null"`
	MatchPath string     `json:"match_path,omitempty" gorm:"column:match_path;type:varchar(127)"`
	Component string     `json:"component,omitempty" gorm:"column:component;type:varchar(127);not null"`
	Visible   int8       `json:"visible,omitempty" gorm:"column:visible;not null;type:int"`
	Redirect  string     `json:"redirect,omitempty" gorm:"column:redirect;type:varchar(127);not null"`
	Perm      string     `json:"perm,omitempty" gorm:"column:perm;type:varchar(127);not null"`
	Sort      int        `json:"sort,omitempty" gorm:"column:sort;not null;type:int"`
	Icon      string     `json:"icon,omitempty" gorm:"column:icon;type:varchar(32);not null"`
	CreatedAt *time.Time `json:"created_at,omitempty" gorm:"column:created_at;autoCreateTime;not null;type:timestamptz"`
	UpdatedAt *time.Time `json:"updated_at,omitempty" gorm:"column:updated_at;autoUpdateTime;not null;type:timestamptz"`
}

func (Menu) TableName() string {
	return "menus"
}
