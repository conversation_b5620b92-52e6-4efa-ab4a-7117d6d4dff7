package model

import (
	"time"

	"github.com/robfig/cron/v3"
	"gorm.io/datatypes"
)

type CronJobStatus uint8

const (
	CronJobStatusActive CronJobStatus = iota + 1
	CronJobStatusInactive
)

type CronJobType uint8

const (
	CronJobTypeHTTPGet CronJobType = iota + 1
	CronJobTypeHTTPPost
	CronJobTypeMQTT
)

type CronJob struct {
	ID        int            `json:"id" gorm:"cloumn:id;primaryKey;not null;type:int"`
	JobID     cron.EntryID   `json:"_" gorm:"column:job_id;not null;type:int"`
	Name      string         `json:"name" gorm:"column:name;not null;type:varchar(64)"`
	Comment   string         `json:"comment" gorm:"column:comment;type:varchar(255)"`
	Cron      string         `json:"cron" gorm:"column:cron;not null;type:varchar(64)"`
	Status    CronJobStatus  `json:"status" gorm:"column:status;not null;type:int;default:1"`
	Type      CronJobType    `json:"type" gorm:"column:type;not null;type:int"`
	Begin     *string        `json:"begin" gorm:"column:begin;type:date"`
	End       *string        `json:"end" gorm:"column:end;type:date"`
	Topic     string         `json:"topic" gorm:"column:topic;not null;type:varchar(64)"`
	Data      datatypes.JSON `json:"data" gorm:"column:data;type:jsonb"`
	CreatedAt *time.Time     `json:"created_at" gorm:"column:created_at;autoCreateTime;not null;type:timestamptz"`
	UpdatedAt *time.Time     `json:"updated_at" gorm:"column:updated_at;autoUpdateTime;not null;type:timestamptz"`
}

func (CronJob) TableName() string {
	return "cron_jobs"
}

func (c CronJob) Run() {

}
