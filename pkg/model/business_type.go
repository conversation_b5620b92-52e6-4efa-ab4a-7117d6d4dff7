package model

import "time"

type BusinessType struct {
	ID        int        `json:"id,omitempty" gorm:"column:id;primaryKey"`
	ParentID  int        `json:"parent_id,omitempty" gorm:"column:parent_id;type:int;not null"`
	Name      string     `json:"name,omitempty" gorm:"column:name;type:varchar(64);not null"`
	Sort      int        `json:"sort" gorm:"column:sort;default:100;not null;type:int"`
	Number    int64      `json:"number" gorm:"column:number;default:0;not null;type:int"`
	CreatedAt *time.Time `json:"created_at,omitempty" gorm:"column:created_at;aotuCreatetime;not null;type:timestamptz"`
	UpdatedAt *time.Time `json:"updated_at,omitempty" gorm:"column:updated_at;autoUpdatetime;not null;type:timestamptz"`
}

func (BusinessType) TableName() string {
	return "business_types"
}

type BusinessTypeRelation struct {
	ParentID int `json:"parent_id" gorm:"column:parent_id;primaryKey"`
	ChildID  int `json:"child_id" gorm:"column:child_id;primaryKey;index"`
	Depth    int `json:"depth" gorm:"column:depth;not null;type:int"`
}

func (BusinessTypeRelation) TableName() string {
	return "business_type_relations"
}

type BusinessTypeWithDevice struct {
	BusinessType
	Devices []Device `json:"-" gorm:"foreignKey:BusinessTypeID;references:ID"`
	Count   int      `json:"count" gorm:"-"`
	Alarmed bool     `json:"alarmed" gorm:"-"`
}

type BusinessTypeTree struct {
	BusinessType
	Children []BusinessTypeTree `json:"children"`
}
