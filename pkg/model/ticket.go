package model

import "time"

type TicketPriority = uint8

const (
	TicketPriorityLow TicketPriority = iota + 1
	TicketPriorityHigh
	TicketPriorityimmediate
)

type TicketStatus = uint8

const (
	TicketStatusCreated TicketStatus = iota + 1
	TicketStatusProcessing
	TicketStatusSolved
	TicketStatusCanceled
)

type TicketType = uint8

const (
	TicketTypeCommon TicketType = iota + 1
	TicketTypeAsset
)

type Ticket struct {
	ID             int            `json:"id" gorm:"column:id;primaryKey"`
	Title          string         `json:"title" gorm:"column:title;not null;type:varchar(128)"`
	Detail         string         `json:"detail" gorm:"column:detail;not null;type:varchar(512)"`
	Type           TicketType     `json:"type" gorm:"column:type;not null;type:int;default:1"`
	Priority       TicketPriority `json:"priority" gorm:"column:priority;not null;type:int"`
	Phone          string         `json:"phone" gorm:"column:phone;not null;type:varchar(32)"`
	CreateUserID   int            `json:"-" gorm:"column:create_user_id;not null;type:int"`
	AssignedUserID int            `json:"-" gorm:"column:assigned_user_id;not null;type:int;index"`
	Status         TicketStatus   `json:"status" gorm:"column:status;not null;type:int"`
	FlowID         int            `json:"flow_id" gorm:"column:flow_id;not null;type:int;default:0;index"`
	CreatedAt      *time.Time     `json:"created_at" gorm:"column:created_at;autoCreatetime;not null;type:timestamptz"`
	UpdatedAt      *time.Time     `json:"updated_at" gorm:"column:updated_at;autoUpdatetime;not null;type:timestamptz"`
}

func (Ticket) TableName() string {
	return "tickets"
}

type TicketTimeLine struct {
	ID        int          `json:"id" gorm:"column:id;primaryKey"`
	TicketID  int          `json:"-" gorm:"column:ticket_id;not null;type:int"`
	Status    TicketStatus `json:"status" gorm:"column:status;not null;type:int"`
	UserId    int          `json:"-" gorm:"column:user_id;not null;type:int"`
	Comment   *string      `json:"comment" gorm:"column:comment;type:varchar(512)"`
	CreatedAt *time.Time   `json:"created_at" gorm:"column:created_at;autoCreatetime;not null;type:timestamptz"`
}

func (TicketTimeLine) TableName() string {
	return "ticket_timelines"
}

type TicketTimelineWithUser struct {
	TicketTimeLine
	UserInfo User `json:"user_info" gorm:"foreignKey:UserId;references:ID"`
}

type TicketWithUser struct {
	Ticket
	CreatedUserInfo  User                     `json:"created_user_info" gorm:"foreignKey:CreateUserID;references:ID"`
	AssignedUserInfo User                     `json:"assigned_user_info" gorm:"foreignKey:AssignedUserID;references:ID"`
	Timelines        []TicketTimelineWithUser `json:"timelines" gorm:"foreignKey:TicketID;references:ID"`
}
