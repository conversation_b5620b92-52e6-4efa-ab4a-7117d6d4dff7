package model

type Cabinet struct {
	ID               int     `json:"id" gorm:"column:id;primaryKey"`
	CustomID         string  `json:"custom_id" gorm:"column:custom_id;type:varchar(64);not null;unique"`
	Name             string  `json:"name" gorm:"column:name;type:varchar(64);not null"`
	UMax             *int    `json:"u_max" gorm:"column:u_max;type:int"`
	UStart           *int    `json:"u_start" gorm:"column:u_start;type:int"`
	UType            *int    `json:"u_type" gorm:"column:u_type;type:int"`
	Bearing          *int    `json:"bearing" gorm:"column:bearing;type:int"`
	Power            *int    `json:"power" gorm:"column:power;type:int"`
	ManuFacturer     *string `json:"manu_facturer" gorm:"column:manu_facturer;type:varchar(64)"`
	Brand            *string `json:"brand" gorm:"column:brand;type:varchar(64)"`
	MaintainPhone    *string `json:"maintain_phone" gorm:"column:maintain_phone;type:varchar(64)"`
	MaintainDuration *int    `json:"maintain_duration" gorm:"column:maintain_duration;type:int"`
	MaintainUser     *string `json:"maintain_user" gorm:"column:maintain_user;type:varchar(64)"`
	FacturedTime     *string `json:"factured_time" gorm:"column:factured_time;type:varchar(64)"`
	BoughtTime       *string `json:"bought_time" gorm:"column:bought_time;type:varchar(64)"`
	Model            *string `json:"model" gorm:"column:model;type:varchar(64)"`
	Field            *string `json:"field" gorm:"column:field;type:varchar(64)"`
	Usage            *string `json:"usage" gorm:"column:usage;type:varchar(64)"`
	Comment          *string `json:"comment" gorm:"column:comment;type:varchar(64)"`
	AreaID           int     `json:"area_id" gorm:"column:area_id;not null;type:int;index"`
	BusinessTypeID   int     `json:"business_type_id" gorm:"column:business_type_id;not null;type:int"`
}

func (Cabinet) TableName() string {
	return "cabinets"
}

type Asset struct {
	ID               int     `json:"id" gorm:"column:id;primaryKey"`
	CabinetID        string  `json:"cabinet_id" gorm:"column:cabinet_id;type:varchar(64);not null;index"`
	AreaID           int     `json:"area_id" gorm:"column:area_id;not null;type:int;index"`
	BusinessTypeID   int     `json:"business_type_id" gorm:"column:business_type_id;not null;type:int"`
	IP               *string `json:"ip" gorm:"column:ip;type:varchar(64)"`
	Name             string  `json:"name" gorm:"column:name;type:varchar(64)"`
	Brand            *string `json:"brand" gorm:"column:brand;type:varchar(64)"`
	Type             *string `json:"type" gorm:"column:type;type:varchar(64)"`
	UStart           *int    `json:"u_start" gorm:"column:u_start;type:int"`
	UEnd             *int    `json:"u_end" gorm:"column:u_end;type:int"`
	Weight           *int    `json:"weight" gorm:"column:weight;type:int"`
	Power            *int    `json:"power" gorm:"column:power;type:int"`
	CascadeDevice    *string `json:"cascade_device" gorm:"column:cascade_device;type:varchar(64)"`
	ManuFacturer     *string `json:"manu_facturer" gorm:"column:manu_facturer;type:varchar(64)"`
	Number           *string `json:"number" gorm:"column:number;type:varchar(64)"`
	MaintainUser     *string `json:"maintain_user" gorm:"column:maintain_user;type:varchar(64)"`
	MaintainPhone    *string `json:"maintain_phone" gorm:"column:maintain_phone;type:varchar(64)"`
	MaintainDuration *int    `json:"maintain_duration" gorm:"column:maintain_duration;type:int"`
	FacturedTime     *string `json:"factured_time" gorm:"column:factured_time;type:varchar(64)"`
	BoughtTime       *string `json:"bought_time" gorm:"column:bought_time;type:varchar(64)"`
	Model            *string `json:"model" gorm:"column:model;type:varchar(64)"`
	Field            *string `json:"field" gorm:"column:field;type:varchar(64)"`
	Usage            *string `json:"usage" gorm:"column:usage;type:varchar(64)"`
	Comment          *string `json:"comment" gorm:"column:comment;type:varchar(254)"`
	ZCID             *string `json:"zcid" gorm:"column:zcid;type:varchar(64)"`
}

func (Asset) TableName() string {
	return "assets"
}
