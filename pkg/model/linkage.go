package model

type TimeInfo struct {
	TimeType   string `json:"time_type,omitempty"`    // 时间类型
	WeekSlot   []int  `json:"week_slot,omitempty" `   // 星期几
	StartTime  string `json:"start_time,omitempty"`   // 时间段开始时间
	EndTime    string `json:"end_time,omitempty" `    // 时间段结束时间
	Time       string `json:"time,omitempty"`         // 时间段结束时间
	Cycles     int    `json:"cycles,omitempty"`       // 周期时间
	CyclesUnit string `json:"cycles_unit,omitempty" ` // 周期单位 s min h d w m y
	Condition  string `json:"condition,omitempty" `   // 时间条件 inrange outrange
}

type Linkage struct {
	ID          int    `json:"id,omitempty" gorm:"column:id;primaryKey"`
	Name        string `json:"name,omitempty" gorm:"column:name;type:varchar(64);not null"`
	Description string `json:"description,omitempty" gorm:"column:description;type:varchar(255)"`
}

func (Linkage) TableName() string {
	return "linkage_info"
}

type LinkageData struct {
	ID       int       `json:"id" gorm:"column:id;primaryKey"`
	TimeInfo *TimeInfo `json:"time_info,omitempty" gorm:"column:time_info;serializer:json;type:jsonb"`
	Script   string    `json:"script,omitempty" gorm:"column:script;type:text"`
	RawData  string    `json:"raw_data,omitempty" gorm:"column:raw_data;type:text"`
}

func (LinkageData) TableName() string {
	return "linkage_data"
}
