package model

type PermissionMethod uint8

const (
	PermissionMethodGet PermissionMethod = iota + 1
	PermissionMethodPost
)

type Permission struct {
	ID     int              `json:"id" gorm:"column:id;primaryKey"`
	Name   string           `json:"name,omitempty" gorm:"column:name;type:varchar(64);not null"`
	Path   string           `json:"path,omitempty" gorm:"column:path;type:varchar(255);not null;index:path_method,unique"`
	Method PermissionMethod `json:"method,omitempty" gorm:"column:method;type:int2;not null;index:path_method,unique"`
}

func (Permission) TableName() string {
	return "permissions"
}

type RolePermission struct {
	RoleID       int `json:"role_id" gorm:"column:role_id;primaryKey"`
	PermissionID int `json:"permission_id" gorm:"column:permission_id;primaryKey"`
}

func (RolePermission) TableName() string {
	return "role_permissions"
}
