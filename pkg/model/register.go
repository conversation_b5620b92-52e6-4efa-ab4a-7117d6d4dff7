package model

type RegisterRepo struct {
	ID      int    `json:"id" gorm:"column:id;primaryKey"`
	RegInfo string `json:"registecode" gorm:"column:registecode;type:text"`
	ThreeD  string `json:"threed"      gorm:"column:threed;type:varchar(255)"`
}

func (RegisterRepo) TableName() string {
	return "registe"
}

type RegisterOut struct {
	RegCode string
	RegInfo string
}

type Function struct {
	Type   string `json:"type"   gorm:"column:type;type:varchar(64)"`
	Enable bool   `json:"enable" gorm:"column:enable;type:bool"`
	Number int    `json:"number" gorm:"column:number;type:int"`
}

func (Function) TableName() string {
	return "funcitem"
}
