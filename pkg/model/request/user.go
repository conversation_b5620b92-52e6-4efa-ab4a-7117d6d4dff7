package request

type Login struct {
	Account  string `json:"account" binding:"required"`
	Password string `json:"password" binding:"required"`
}
type CreateUser struct {
	Name     string `json:"name" binding:"required"`
	Nickname string `json:"nickname" binding:"required"`
	Email    string `json:"email" binding:"required"`
	Phone    string `json:"phone" binding:"required"`
	RoleID   int    `json:"role_id" binding:"required"`
}

type UserList struct {
	PageInfo
	NickName string `form:"name"`
	Phone    string `form:"phone"`
	RoleID   int    `form:"role_id"`
}

type SetUserArea struct {
	ID    int   `json:"id" binding:"required"`
	Areas []int `json:"areas" binding:"required"`
}

type GetUserArea struct {
	ID int `form:"id"`
	TokenInfo
}

type SetUserDepartment struct {
	ID          int   `json:"id" binding:"required"`
	Departments []int `json:"departments" binding:"required"`
}
type GetUserDepartment struct {
	ID int `form:"id"`
	TokenInfo
}
type SetUserBusinessType struct {
	ID            int   `json:"id" binding:"required"`
	BusinessTypes []int `json:"business_types" binding:"required"`
}

type GetUserBusinessType struct {
	ID int `form:"id"`
	TokenInfo
}

type UpdateUser struct {
	ID       int    `json:"id" binding:"required"`
	Nickname string `json:"nickname"`
	RoleID   int    `json:"role_id"`
	Status   uint8  `json:"status"`
	Phone    string `json:"phone"`
	Email    string `json:"email"`
}

type ChangePassword struct {
	Old string `json:"old" binding:"required"`
	New string `json:"new" binding:"required"`
	TokenInfo
}

type ResetPassword struct {
	ID       int    `json:"id" binding:"required"`
	Password string `json:"password" binding:"required"`
}

type DeleteUser struct {
	FindByID
}

type UserListSimplify struct {
	ID int `form:"id"`
}

type UserListByID struct {
	IDs []int `json:"ids" binding:"required"`
}

type UserListByMenuID struct {
	MenuID int `form:"menu_id" binding:"required"`
}

type QyLogin struct {
	Code string `json:"code" binding:"required"`
}

type UserSelfUpdate struct {
	TokenInfo
	Nickname string `json:"nickname"`
	Phone    string `json:"phone"`
	Email    string `json:"email"`
}

type UserInfo struct {
	TokenInfo
}

type UserListWithoutPage struct {
	NickName string `form:"nickname"`
}
