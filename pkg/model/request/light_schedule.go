package request

import (
	"time"
	"tw_platform/pkg/model"
)

type LightScheduleAction struct {
	DeviceID int    `json:"device_id" binding:"required"`
	UnitID   int    `json:"unit_id" binding:"required"`
	Value    string `json:"value" binding:"required"`
}
type CreateLightSchedule struct {
	AreaID  int                       `json:"area_id" binding:"required"`
	Name    string                    `json:"name" binding:"required"`
	Cron    string                    `json:"cron" binding:"required"`
	Status  model.LightScheduleStatus `json:"status" binding:"required"`
	Actions []LightScheduleAction     `json:"actions" binding:"required"`
	Type    model.LightScheduleType   `json:"type" binding:"required"`
	BeginAt *time.Time                `json:"-"`
	EndAt   *time.Time                `json:"-"`
	Begin   string                    `json:"begin_at"`
	End     string                    `json:"end_at"`
}

type UpdateLightSchedule struct {
	ID      int                       `json:"id"`
	Cron    string                    `json:"cron"`
	Name    string                    `json:"name"`
	Status  model.LightScheduleStatus `json:"status"`
	Actions []LightScheduleAction     `json:"actions"`
	Type    model.LightScheduleType   `json:"type"`
	BeginAt *time.Time                `json:"-"`
	EndAt   *time.Time                `json:"-"`
	Begin   string                    `json:"begin_at"`
	End     string                    `json:"end_at"`
}

type LightScheduleDetail struct {
	FindByID
}

type LightScheduleList struct {
	AreaID int `form:"area_id" binding:"required"`
}

type LishtScheduleDelete struct {
	FindByID
}

type LightScheduleControlByArea struct {
	AreaID         int    `json:"area_id" binding:"required"`
	BusinessTypeID int    `json:"business_type_id" binding:"required"`
	Value          string `json:"value" binding:"required"`
}

type LightScheduleStatistic struct {
	AreaID int `json:"area_id"`
	TokenInfo
}

type LightScheduleControlAll struct {
	BusinessTypeID int    `json:"business_type_id" binding:"required"`
	Value          string `json:"value" binding:"required"`
	TokenInfo
}
