package request

import "tw_platform/pkg/model"

type CreateEventTask struct {
	Name        string                    ` json:"name"`
	Enabled     string                    ` json:"enabled"` // ON OFF
	Description *string                   ` json:"description"`
	Condition   *model.EventTaskCondition `json:"condition,omitempty" `
	Action      *model.EventTaskAction    `json:"action,omitempty"`
}
type DeleteEventTask struct {
	ID int `json:"id" form:"id" binding:"required"`
}
type UpdateEventTask struct {
	FindByID
	CreateEventTask
}
