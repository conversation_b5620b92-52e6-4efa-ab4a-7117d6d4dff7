package request

import (
	"mime/multipart"
	"time"
	"tw_platform/pkg/model"
)

type CreateMaintain struct {
	DeviceId    int                   `form:"device_id" binding:"required"`
	Title       string                `form:"title" binding:"required"`
	Content     string                `form:"content" binding:"required"`
	Amount      int                   `form:"amount" binding:"required"`
	File        *multipart.FileHeader `form:"file" binding:"required"`
	Duration    int                   `form:"duration" binding:"required"`
	SignedAt    string                `form:"signed_at" binding:"required"`
	ExecuteDate []time.Time           `form:"execute_date" binding:"required"  time_format:"2006-01-02"`
}

type MaintainList struct {
	PageInfo
}

type MaintainPlanList struct {
	FindByID
	Status model.MaintainPlanStatus `json:"status"`
}

type ExecuteMaintainPlan struct {
	PlanID     int    `json:"plan_id" binding:"required"`
	Result     string `json:"result"`
	Suggestion string `json:"suggestion"`
	Comment    string `json:"comment"`
	TokenInfo
}
