package request

import "tw_platform/pkg/model"

type CreateAlarmGroup struct {
	Name               string            ` json:"name"  binding:"required"`
	Enabled            string            `json:"enabled"  binding:"required"` // ON OFF
	Description        *string           ` json:"description"`
	Config             model.AlarmConfig `json:"alarm_config"` // 通知配置
	Devices            []int             `json:"devices"`
	NotificationGroups []int             `json:"notification_groups"`
}
type DeleteAlarmGroup struct {
	FindByID
}
type UpdateAlarmGroup struct {
	FindByID
	CreateAlarmGroup
}
