package request

import "tw_platform/pkg/model"

type DeleteSnmpTrap struct {
	FindByID
}
type CreateSnmpTrap struct {
	Name   string             ` json:"name"`
	Port   string             ` json:"port"`
	Ip     string             ` json:"ip"`
	Type   int                `json:"type"`
	Config []model.SnmpConfig `json:"config,omitempty"`
}
type UpdateSnmpTrap struct {
	FindByID
	Name   string             ` json:"name"`
	Port   string             ` json:"port"`
	Ip     string             ` json:"ip"`
	Type   int                `json:"type"`
	Config []model.SnmpConfig `json:"config,omitempty"`
}
