package request

import "tw_platform/pkg/model"

type CreateNotificationGroup struct {
	Name              string                    `json:"name" binding:"required"`
	NotificationType  string                    ` json:"notification_type"` // 通知类型MEMBER-成员通知 EMAIL-邮箱通知 SMS-短信通知 CALL-电话通知
	Status            string                    ` json:"status"`
	Config            model.NotificationConfig  `json:"notification_config,omitempty" `
	Description       *string                   `json:"description"`
	NotificationUsers []model.NotificationUsers `json:"notification_users"`
}
type DeleteNotificationGroup struct {
	FindByID
}
type UpdateNotificationGroup struct {
	FindByID
	CreateNotificationGroup
}
