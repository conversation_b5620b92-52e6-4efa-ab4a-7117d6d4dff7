package request

import "tw_platform/pkg/model"

type OperationList struct {
	UserId         int    `form:"user_id"`
	BeginAt        string `form:"begin_at"`
	EndAt          string `form:"end_at"`
	PermissionName string `form:"permission_name"`

	PageInfo
}

type AlarmLogList struct {
	DeviceID int    `form:"device_id"`
	BeginAt  string `form:"begin_at"`
	EndAt    string `form:"end_at"`

	PageInfo
}

type TableUseage struct {
	Type model.LogTableType `form:"type" json:"type" binding:"required"`
}

type TableTruncate struct {
	Type model.LogTableType `form:"type" binding:"required"`
}
