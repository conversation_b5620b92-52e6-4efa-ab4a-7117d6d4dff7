package request

import "tw_platform/pkg/model"

type CreatePermission struct {
	Name   string                 `json:"name" binding:"required"`
	Path   string                 `json:"path" binding:"required"`
	Method model.PermissionMethod `json:"method" binding:"required"`
}

type PermissionList struct {
	PageInfo
	Name   string `form:"name"`
	Method uint8  `form:"method"`
	Path   string `form:"path"`
}

type UpdatePermission struct {
	ID     int                    `json:"id" binding:"required"`
	Name   string                 `json:"name"`
	Path   string                 `json:"path"`
	Method model.PermissionMethod `json:"method"`
}

type DeletePermission struct {
	FindByID
}
