package request

import (
	"tw_platform/pkg/model"
)

type RealTimePlayRequest struct {
	Id        int `json:"id"`
	ChannelID int `json:"channel_id"`
}
type PreviewVideoRequest struct {
	Index      int    `json:"index"`
	Brand      string `json:"brand"`
	ChannelID  int    `json:"channel_id"`
	IP         string `json:"ip"`
	Password   string `json:"password"`
	Stream     string `json:"stream"`
	DeviceName string `json:"device_name"`
	Type       string `json:"type"`
	Name       string `json:"name"`
}
type VideoProcessChannel struct {
	ProcessChannel string `form:"process_channel" json:"process_channel"`
}

type PlaybackRequest struct {
	Id        int    `json:"id"`
	ChannelID int    `json:"channel_id"`
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
}

type CreateVideo struct {
	CreateDeviceBasic
	Devices []struct {
		Type       string            `json:"type" binding:"required"`
		DeviceName string            `json:"device_name" binging:"required"`
		Config     model.VideoDevice `json:"config" binding:"required"`
	} `json:"devices" binding:"required"`
}

type ModifyVideo struct {
	ID         int               `json:"id"`
	Type       string            `json:"type"`
	DeviceName string            `json:"device_name"`
	Config     model.VideoDevice `json:"config"`
}

type CaptureImage struct {
	ID int `json:"id"`
}

type CreateVideoDevice struct {
	CreateDeviceBasic
	DeviceList []model.Device `json:"device_list" binding:"required"`
	ConfigList []model.Config `json:"config_list" binding:"required"`
}

type VideoDetail struct {
	FindByID
}
