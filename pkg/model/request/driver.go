package request

type CreateDriver struct {
	Agree        string `json:"agree" binding:"required"`
	Use          string `json:"use" binding:"required"`
	ManuFacturer string `json:"manu_facturer" binding:"required"`
	DevType      string `json:"dev_type" binding:"required"`
	Model        string `json:"model" binding:"required"`
	Name         string `json:"name" binding:"required"`
	Content      string `json:"content" binding:"required"`
}

type DriverList struct {
	DevType      string `form:"dev_type"`
	ManuFacturer string `form:"manu_facturer"`
	Use          string `form:"use"`
	Agree        string `form:"agree"`
	Order        int8   `form:"order,default=-1"`
	PageInfo
}
