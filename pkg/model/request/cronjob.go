package request

import (
	"encoding/json"
	"tw_platform/pkg/model"
)

type CreateCronjob struct {
	Name    string            `json:"name" binding:"required"`
	Comment string            `json:"comment"`
	Cron    string            `json:"cron" binding:"required"`
	Type    model.CronJobType `json:"type" binding:"required"`
	Topic   string            `json:"topic" binding:"required"`
	Data    json.RawMessage   `json:"data"`
	Begin   string            `json:"begin"`
	End     string            `json:"end"`
}

type UpdateCronjob struct {
	ID      int                 `json:"id" binding:"required"`
	Name    string              `json:"name"`
	Comment string              `json:"commnet"`
	Cron    string              `json:"cron"`
	Topic   string              `json:"topic"`
	Data    json.RawMessage     `json:"data"`
	Type    model.CronJobType   `json:"type"`
	Status  model.CronJobStatus `json:"status"`
	Begin   string              `json:"begin"`
	End     string              `json:"end"`
}

type CronjobList struct {
	PageInfo
}

type CronjobDelete struct {
	FindByID
}
