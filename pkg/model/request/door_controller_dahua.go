package request

type InserUserDahuaReq struct {
	UserID   string `json:"UserID" binding:"required"`
	UserName string `json:"UserName"`
	Password string `json:"Password"`
	Doors    []int  `json:"Doors"`
}
type InserCardDahuaReq struct {
	UserID   string `json:"UserID" binding:"required"`
	CardNo   string `json:"CardNo" binding:"required"`
	CardName string `json:"CardName"`
	CardType int    `json:"CardType"`
}
type GetCardDahuaReq struct {
	DoorId int    `form:"doorId" binding:"required"`
	UserID string `form:"UserID" binding:"required"`
}
type RemoveCardDahuaReq struct {
	DoorId int    `form:"doorId" binding:"required"`
	CardNo string `json:"CardNo" binding:"required"`
}
type GetRecordDahuaReq struct {
	DoorId    int    `form:"doorId" binding:"required"`
	StartTime string `form:"StartTime" binding:"required"`
	EndTime   string `form:"EndTime" binding:"required"`
}
