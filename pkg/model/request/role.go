package request

type CreateRole struct {
	FindByID
	Name string `json:"name" binding:"required"`
}

type RoleList struct {
	PageInfo
}

type UpdateRole struct {
	ID    int    `json:"id" binding:"required"`
	NewID int    `json:"new_id"`
	Name  string `json:"name"`
}

type DeleteRole struct {
	FindByID
}

type SetmenusToRole struct {
	RoleID int   `json:"role_id" binding:"required"`
	IDs    []int `json:"ids" binding:"required"`
}

type GetMenusByRole struct {
	RoleID int `form:"role_id"`
	TokenInfo
}

type AllocableList struct {
	RoleID int `json:"role_id"`
	TokenInfo
}

type GetPermissionsByRole struct {
	RoleID int `form:"role_id"`
	TokenInfo
}

type SetPermissionsToRole struct {
	RoleID int   `json:"role_id" binding:"required"`
	IDs    []int `json:"ids" binding:"required"`
}
