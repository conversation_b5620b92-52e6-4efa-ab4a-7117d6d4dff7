package request

type CtrDoorControllerReq struct {
	DoorId *int   `form:"door_id" binding:"required"`
	SubId  *int   `form:"sub_id" binding:"required"`
	Way    string `form:"way" binding:"required"`
}
type DoorControllerCardReq struct {
	DoorId int `form:"doorId"`
}
type AddDoorControllerCardReq struct {
	CardNo string `json:"cardNo" binding:"required"`
	Door   []int  `json:"door" binding:"required"`
}
type DelDoorControllerCardReq struct {
	CardNo string `json:"cardNo" binding:"required"`
}
type GetDoorControllerLogReq struct {
	DoorId    *int   `form:"door_id" binding:"required"`
	StartTime *int64 `form:"startTime" binding:"required"`
	EndTime   *int64 `form:"endTime" binding:"required"`
}
type SyncReqItem struct {
	CardNo string `json:"cardNo" binding:"required"`
	Door   []int  `json:"door"   binding:"required"`
}
type SyncReq struct {
	Id       []int         `json:"id" binding:"required"`
	CardList []SyncReqItem `json:"cardlist" binding:"required"`
}
