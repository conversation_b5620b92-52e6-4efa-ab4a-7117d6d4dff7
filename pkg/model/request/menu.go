package request

type CreateMenu struct {
	ParentID  int    `json:"parent_id"`
	Name      string `json:"name" binding:"required"`
	Icon      string `json:"icon"`
	Type      int8   `json:"type,default=2"`
	Path      string `json:"path"`
	MatchPath string `json:"match_path"`
	Component string `json:"component"`
	Visible   int8   `json:"visible,default=1"`
	Redirect  string `json:"redirect"`
	Perm      string `json:"perm"`
	Sort      int    `json:"sort,default=10"`
}

type MenuTree struct {
	TokenInfo
}

type UpdateMenu struct {
	ID        int    `json:"id" binding:"required"`
	Name      string `json:"name"`
	ParentID  int    `json:"parent_id"`
	Icon      string `json:"icon"`
	Type      int8   `json:"type"`
	Path      string `json:"path"`
	MatchPath string `json:"match_path"`
	Component string `json:"component"`
	Visible   int8   `json:"visible"`
	Redirect  string `json:"redirect"`
	Perm      string `json:"perm"`
	Sort      int    `json:"sort"`
}

type MenuList struct {
	PageInfo
}

type DeleteMenu struct {
	FindByID
}

type MenuListByPID struct {
	PID int `form:"pid" binding:"required"`
}
