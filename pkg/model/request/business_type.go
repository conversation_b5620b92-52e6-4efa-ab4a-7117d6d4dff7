package request

type CreateBusinessType struct {
	Name     string `json:"name" binding:"required"`
	ParentID int    `json:"parent_id"`
	Sort     int    `json:"sort"`
}

type UpdateBusinessType struct {
	FindByID
	// ParentID int    `json:"parent_id"`
	Name string `json:"name"`
	Sort int    `json:"sort"`
}

type DeleteBusinessType struct {
	FindByID
}

type FindBusinessTypeByPID struct {
	PID int `form:"pid"`

	TokenInfo
}

type BusinessTypeListHasDevice struct {
	FindByID
	TokenInfo
	AreaID int `form:"area_id" binding:"required"`
}

type BusinessTypeByTopLevel struct {
	ID       int `form:"id"`
	ParentID int `form:"parent_id"`
}

type BusinessTypeTreeOnlyUser struct {
	TokenInfo
	PID int `form:"pid"`
}

type BusinessTypeTreeWithDevice struct {
	TokenInfo
	PID int `form:"pid" binding:"required"`
}
