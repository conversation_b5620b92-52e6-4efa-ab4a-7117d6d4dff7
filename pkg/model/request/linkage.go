package request

import (
	"tw_platform/pkg/model"
)

type CreateLinkage struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
}
type DeleteLinkage struct {
	ID int `json:"id" form:"id" binding:"required"`
}
type LinkageDetail struct {
	ID int `json:"id" form:"id" binding:"required"`
}

type UpdateLinkageInfo struct {
	ID          int    `json:"id" form:"id" binding:"required"`
	Name        string `json:"name" binding:"required"`
	Description string `json:"description,omitempty"`
}

type UpdateLinkageData struct {
	ID       int             `json:"id" form:"id" binding:"required"`
	TimeInfo *model.TimeInfo `json:"time_info,omitempty"`
	Script   string          `json:"script,omitempty" `
	RawData  string          `json:"raw_data,omitempty" `
}
