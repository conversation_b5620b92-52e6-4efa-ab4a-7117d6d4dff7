package request

type CreateCabinet struct {
	CustomID string `json:"custom_id" binding:"required"`
	Name     string `json:"name" binding:"required"`
	//UMax             int    `json:"u_max"`
	//UStart           int    `json:"u_start"`
	//UType            int    `json:"u_type"`
	//Bearing          int    `json:"bearing"`
	//Power            int    `json:"power"`
	//ManuFacturer     string `json:"manu_facturer"`
	//Brand            string `json:"brand"`
	//MaintainPhone    string `json:"maintain_phone"`
	//MaintainDuration int    `json:"maintain_duration"`
	//MaintainUser     string `json:"maintain_user" `
	//FacturedTime     string `json:"factured_time"`
	//BoughtTime       string `json:"bought_time"`
	//Model            string `json:"model"`
	//Field            string `json:"field"`
	//Usage            string `json:"usage"`
	//Comment          string `json:"comment"`
	AreaID         int `json:"area_id" binding:"required"`
	BusinessTypeID int `json:"business_type_id" binding:"required"`
}

type UpdateCabinet struct {
	CustomID         string  `json:"custom_id" binding:"required"`
	Name             *string `json:"name"`
	UMax             *int    `json:"u_max"`
	UStart           *int    `json:"u_start"`
	UType            *int    `json:"u_type"`
	Bearing          *int    `json:"bearing"`
	Power            *int    `json:"power"`
	ManuFacturer     *string `json:"manu_facturer"`
	Brand            *string `json:"brand"`
	MaintainPhone    *string `json:"maintain_phone"`
	MaintainDuration *int    `json:"maintain_duration"`
	MaintainUser     *string `json:"maintain_user" `
	FacturedTime     *string `json:"factured_time"`
	BoughtTime       *string `json:"bought_time"`
	Model            *string `json:"model"`
	Field            *string `json:"field"`
	Usage            *string `json:"usage"`
	Comment          *string `json:"comment"`
	AreaID           *int    `json:"area_id"`
	BusinessTypeID   *int    `json:"business_type_id"`
}

type CabinetList struct {
	AreaID         int `form:"area_id"`
	BusinessTypeID int `form:"business_type_id"`
}

type DeleteCabinet struct {
	IDs []string `json:"ids" binding:"required"`
}

type CreateAsset struct {
	CabinetID        string  `json:"cabinet_id"`
	AreaID           int     `json:"area_id" binding:"required"`
	BusinessTypeID   int     `json:"business_type_id" binding:"required"`
	Name             string  `json:"name"`
	IP               *string `json:"ip"`
	Brand            *string `json:"brand"`
	Type             *string `json:"type"`
	UStart           *int    `json:"u_start"`
	UEnd             *int    `json:"u_end"`
	Weight           *int    `json:"weight"`
	Power            *int    `json:"power"`
	CascadeDevice    *string `json:"cascade_device"`
	ManuFacturer     *string `json:"manu_facturer"`
	Number           *string `json:"number"`
	MaintainUser     *string `json:"maintain_user"`
	MaintainPhone    *string `json:"maintain_phone"`
	MaintainDuration *int    `json:"maintain_duration"`
	Comment          *string `json:"comment"`
	FacturedTime     *string `json:"factured_time"`
	BoughtTime       *string `json:"bought_time"`
	Model            *string `json:"model"`
	Field            *string `json:"field"`
	Usage            *string `json:"usage"`
	ZCID             *string `json:"zcid"`
}

type UpdateAsset struct {
	ID               int     `json:"id" binding:"required"`
	CabinetID        *string `json:"cabinet_id"`
	IP               *string `json:"ip"`
	Name             string  `json:"name"`
	Brand            *string `json:"brand"`
	Type             *string `json:"type"`
	UStart           *int    `json:"u_start"`
	UEnd             *int    `json:"u_end"`
	Weight           *int    `json:"weight"`
	Power            *int    `json:"power"`
	CascadeDevice    *string `json:"cascade_device"`
	ManuFacturer     *string `json:"manu_facturer"`
	Number           *string `json:"number"`
	MaintainUser     *string `json:"maintain_user"`
	MaintainPhone    *string `json:"maintain_phone"`
	MaintainDuration *int    `json:"maintain_duration"`
	Comment          *string `json:"comment"`
	FacturedTime     *string `json:"factured_time"`
	BoughtTime       *string `json:"bought_time"`
	Model            *string `json:"model"`
	Field            *string `json:"field"`
	Usage            *string `json:"usage"`
	ZCID             *string `json:"zcid"`
}

type AssetList struct {
	AreaID         int `form:"area_id" binding:"required"`
	BusinessTypeID int `form:"business_type_id" binding:"required"`
}

type DeleteAsset struct {
	IDs []int `json:"ids" binding:"required"`
}
