package request

import "github.com/gin-gonic/gin"

type PageInfo struct {
	Page int `json:"page,default=1" form:"page,default=1"`
	Size int `json:"size,default=20" form:"size,default=20"`
}

type TokenInfo struct {
	UID int //use id
	RID int //role id
}

func (t *TokenInfo) ParseToken(c *gin.Context) {
	t.UID = c.GetInt("uid")
	t.RID = c.GetInt("rid")
}

type FindByID struct {
	ID int `json:"id" form:"id" binding:"required"`
	TokenInfo
}
