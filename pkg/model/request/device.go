package request

import "tw_platform/pkg/model"

type CreateDeviceBasic struct {
	DepartmentIDs  []int `json:"department_ids" binding:"required"`
	AreaID         int   `json:"area_id" binding:"required"`
	BusinessTypeID int   `json:"business_type_id" binding:"required"`
}
type CreatePceMDevice struct {
	PortID   int    `json:"port_id" binding:"required"`
	DriverID int    `json:"driver_id"`
	Type     string `json:"type"`

	BaudRate     int `json:"baud_rate"`
	Timeinterval int `json:"time_interval"`
	TimeOut      int `json:"time_out"`

	Devices []struct {
		Name string `json:"name" binding:"required"`
		Addr int    `json:"addr"`
	} `json:"devices" binding:"required"`

	CreateDeviceBasic
}

type MqttCreatePceDevice struct {
	Port   string   `json:"port"`
	Driver string   `json:"driver"`
	Config []string `json:"config"`
	IDs    []int    `json:"ids"`
}

type CreateDevice struct {
	DriverID int `json:"driver_id" binding:"required"`
	PortID   int `json:"port_id" binding:"required"`

	BaudRate     int `json:"baud_rate"`
	Timeinterval int `json:"time_interval"`
	TimeOut      int `json:"time_out"`

	Devices []struct {
		Name string `json:"name" binding:"required"`
		Addr int    `json:"addr"`
	} `json:"devices" binding:"required"`

	CreateDeviceBasic
}
type CreateCustomDevice struct {
	Devices []struct {
		Name   string              `json:"name" binding:"required"`
		Config *model.CustomDevice `json:"config" binding:"required"`
	} `json:"devices" binding:"required"`

	CreateDeviceBasic
}
type CreateOpcDevice struct {
	PortID  int `json:"port_id" binding:"required"`
	Devices []struct {
		Name   string           `json:"name" binding:"required"`
		Config *model.OpcDevice `json:"config" binding:"required"`
	} `json:"devices" binding:"required"`

	CreateDeviceBasic
}

type BatchCreateCustomDevice struct {
	CreateCustomDevice
}

type DeleteDevice struct {
	FindByID
}

type DeviceDetail struct {
	FindByID
}

type UpdateDevice struct {
	FindByID
	Name     string               `json:"name"`
	Activity model.DeviceActivity `json:"activity"`

	AreaID       int   `json:"area_id"`
	DepartmentID []int `json:"department_id"`
}

type UpdateDeviceUnit struct {
	DeviceID int                    `json:"device_id" binding:"required"`
	UnitID   int                    `json:"unit_id"`
	Fields   map[string]interface{} `json:"fields" binding:"required"`
}

type UpdateConfig struct {
	Type model.DeviceType `json:"type" binding:"required"`
	FindByID
	Fields map[string]interface{} `json:"fields" binding:"required"`
}

type DeviceDataUnit struct {
	Point  int                `json:"point"`
	Value  string             `json:"value"`
	Status model.DeviceStatus `json:"status"`
	TX     string             `json:"tx"`
	RX     string             `json:"rx"`
}

type DeviceData struct {
	Type          string             `json:"type"`
	ID            int                `json:"id"`
	HasAlarmed    bool               `json:"-"`
	Status        model.DeviceStatus `json:"status"`
	Units         []DeviceDataUnit   `json:"units"`
	IsForceRecord bool               `json:"-"`
	GatewayID     int                `json:"gateway_id"`
}

type ResumeDeviceData struct {
	DeviceData
	Timestamp int64 `json:"timestamp"`
}

type GetDeviceList struct {
	AreaID         int `form:"area_id"`
	BusinessTypeID int `form:"business_type_id" binding:"required"`
	DepartmentID   int `form:"department_id"`
}

type GetDeviceListByFirstType struct {
	BusinessTypeID int `form:"business_type_id" binding:"required"`
}

type DeviceHistoryData struct {
	DeviceID int      `json:"device_id" binding:"required"`
	UnitIDs  []string `json:"unit_ids" binding:"required"`
	Begin    string   `json:"begin" binding:"required"`
	End      string   `json:"end" binding:"required"`
}

type DeviceControl struct {
	DeviceID int    `json:"device_id" binding:"required"`
	UnitID   int    `json:"unit_id"`
	Value    string `json:"value" binding:"required"`
}

type RealtimeData struct {
	DeviceIDs []int `json:"device_ids" binding:"required"`
}

type NameList struct {
	DeviceIDs []int `json:"device_ids" binding:"required"`
}

type DeviceListByAreaID struct {
	AreaID int `form:"area_id" binding:"required"`
	TokenInfo
}

type DeviceListWithConfig struct {
	BusinessTypeID int `form:"business_type_id" binding:"required"`
	AreaID         int `form:"area_id"`
	DepartmentID   int `form:"department_id"`
}

type DeviceListByPort struct {
	PortID int `form:"port_id" binding:"required"`
	TokenInfo
}

type DeviceAlarmBinding struct {
	BindingID int `form:"binding_id" binding:"required"`
}

type SetDeviceAlarmBinding struct {
	BindingID int   `json:"binding_id" binding:"required"`
	DeviceIDs []int `json:"device_ids" binding:"required"`
}

type MqttControlDevice struct {
	DeviceID int    `json:"device_id"`
	UnitID   int    `json:"unit_id"`
	Value    string `json:"value"`
}

type MqttDeleteDevice struct {
	DeviceID int `json:"device_id"`
}

type UpdateDeviceUnitByDriver struct {
	DriverID int                    `json:"driver_id" binding:"required"`
	UnitID   int                    `json:"unit_id"`
	Fields   map[string]interface{} `json:"fields" binding:"required"`
}

type DeviceNameListByDriver struct {
	DriverID int `form:"driver_id" binding:"required"`
}

type DeviceListByTopLevel struct {
	ID             int  `form:"id"`
	BusinessTypeID int  `form:"business_type_id"`
	IsRecursive    bool `form:"is_recursive"`
}

type DeviceConfigListByTopLevel struct {
	ID int `form:"id"`
}

type CreateAssetDevice struct {
	Name string `json:"name" binding:"required"`
	Type string `json:"type" binding:"required"`
	IP   string `json:"ip" binding:"required"`
	Port string `json:"port" binding:"required"`
	CreateDeviceBasic
}

type UPdateAssetDevice struct {
	FindByID
	IP   string `json:"ip"`
	Port string `json:"port"`
}

type ReportDeviceDataUnit struct {
	ID     int                `json:"id"`
	Status model.DeviceStatus `json:"status"`
	Value  string             `json:"value"`
}
type ReportDeviceData struct {
	ID     int                    `json:"id"`
	Status model.DeviceStatus     `json:"status"`
	Unit   []ReportDeviceDataUnit `json:"unit"`
}

type ReportDevicesData struct {
	Data []ReportDeviceData `json:"data"`
	FID  string
}

type ReportDeviceAlarm struct {
	DeviceID int    `json:"device_id"`
	UnitID   int    `json:"unit_id"`
	Type     int    `json:"type"`
	Time     int64  `json:"time"`
	Value    string `json:"value"`

	FID string `json:"fid"`
}

type ReportDeviceList struct {
	GatewayID int `form:"gateway_id"`
	PageInfo
}

type SetReportDeviceInfo struct {
	IDs            []int `json:"ids" binding:"required"`
	AreaID         int   `json:"area_id"`
	BusinessTypeID int   `json:"business_type_id"`
	DepartmentIDs  []int `json:"department_ids"`
}
