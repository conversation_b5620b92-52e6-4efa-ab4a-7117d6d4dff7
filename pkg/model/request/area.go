package request

import (
	"mime/multipart"
	"tw_platform/pkg/model"
)

type CreateArea struct {
	ParentID  int    `json:"parent_id" binding:"required"`
	Name      string `json:"name" binding:"required"`
	Longitude string `json:"longitude"`
	Latitude  string `json:"latitude"`
}

type AreaTreeByUserIDScene struct {
	TokenInfo
	BusinessTypeID int `form:"business_type_id"`
}
type AreaTreeByUserID struct {
	TokenInfo
	BusinessTypeID int `form:"business_type_id" binding:"required"`
}

type DeleteArea struct {
	FindByID
}

type CreateScene struct {
	AreaID         int                   `form:"area_id" binding:"required"`
	BusinessTypeID int                   `form:"business_type_id" binding:"required"`
	Img            *multipart.FileHeader `form:"img"`
	Data           string                `form:"data" binding:"required"`
	Type           model.AreaSceneType   `form:"type" binding:"required"`
	ImgPath        string                `form:"img_path"`
	ExType         model.AreaSceneExType `form:"ex_type"`
}

type SceneDetail struct {
	AreaID         int                 `form:"area_id" binding:"required"`
	BusinessTypeID int                 `form:"business_type_id" binding:"required"`
	Type           model.AreaSceneType `form:"type" binding:"required"`
}

type UpdateScene struct {
	ID      int                   `form:"id" binding:"required"`
	Img     *multipart.FileHeader `form:"img"`
	Data    string                `form:"data"`
	ImgPath string                `form:"img_path"`
	ExType  model.AreaSceneExType `form:"ex_type"`
}

type DeleteScene struct {
	FindByID
}

type UpdateArea struct {
	FindByID
	Name      string           `json:"name"`
	Status    model.AreaStatus `json:"status"`
	Longitude string           `json:"longitude"`
	Latitude  string           `json:"latitude"`
}

type AreaTreeWithDeviceConfigs struct {
	TokenInfo
	Type           string `form:"type"`
	BusinessTypeID int    `form:"business_type_id" binding:"required"`
}

type GetPUE struct {
	AreaID int `form:"area_id" binding:"required"`
}

type AreaListByPID struct {
	ParentID       int `form:"parent_id"`
	BusinessTypeID int `form:"business_type_id"`
}

type PUEEveryHour struct {
	AreaID int `form:"area_id" binding:"required"`
}
type DefaultSceneType struct {
	AreaID         int   `json:"area_id" binding:"required"`
	BusinessTypeID int   `json:"business_type_id" binding:"required"`
	SceneType      uint8 `json:"scene_type" binding:"required"`
}

type AreaDefaultSceneType struct {
	AreaID int `form:"area_id" binding:"required"`
}

type AreaTreeWithAlarmCounts struct {
	TokenInfo
}

type AreaListWithAlarmedByPid struct {
	ParentID       int `form:"parent_id" json:"parent_id"`
	BusinessTypeID int `form:"business_type_id" json:"business_type_id"`

	TokenInfo
}

type AreaTreeWithDevicesByBtID struct {
	TokenInfo
	BusinessTypeID int `json:"business_type_id" binding:"required"`
}

type AreaIDs struct {
	TokenInfo
}

type RealtimeAreaStatus struct {
	IDs []int `json:"ids"`
}
