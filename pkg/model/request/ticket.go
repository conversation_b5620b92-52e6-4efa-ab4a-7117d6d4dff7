package request

import "tw_platform/pkg/model"

type CreateTicket struct {
	TokenInfo
	Title          string               `json:"title" binding:"required"`
	Detail         string               `json:"detail" binding:"required"`
	Priority       model.TicketPriority `json:"priority" binding:"required"`
	Phone          string               `json:"phone" binding:"required"`
	AssignedUserID int                  `json:"assigned_user_id" binding:"required"`
	Type           model.TicketType     `json:"type"`
	FlowID         int                  `json:"flow_id"`

	AlarmID int `json:"alarm_id"`
}

type TicketListFilter uint8

const (
	TicketListFilterAll TicketListFilter = iota
	TicketListFilterFromMe
	TicketListFilterToMe
)

type TicketList struct {
	PageInfo
	TokenInfo
	Filter   TicketListFilter     `form:"filter"`
	Priority model.TicketPriority `form:"priority"`
	ID       int                  `form:"id"`
}

type TicketTimeline struct {
	FindByID
}

type UpdateTicket struct {
	FindByID
	TokenInfo
	Status  model.TicketStatus `json:"status" binding:"required"`
	Comment string             `json:"comment"`
	FlowID  int                `json:"flow_id"`
}

type TicketDetail struct {
	FindByID
}

type UpdateTicketAsset struct {
	FlowID int                `json:"flow_id" binding:"required"`
	Status  model.TicketStatus `json:"status" binding:"required"`
	Comment string             `json:"comment"`
	TokenInfo
}
