package request

import (
	"tw_platform/pkg/model"
)

type HistoryAlarmList struct {
	TokenInfo
	PageInfo

	AlarmFilterCommon
}

type AlarmTotalOfTimeType uint8

const (
	AlarmTotalOfMinute AlarmTotalOfTimeType = iota + 1
	AlarmTotalOfHour
	AlarmTotalOfDay
)

type RealtimeAlarmList struct {
	TokenInfo
	PageInfo
	ID int `form:"id"`

	AlarmFilterCommon
}

type ExportType uint8

const (
	LastDay ExportType = iota + 1
	LastWeek
	LastMonth
	LastQuarter
	LastHalfYear
	LastYear
)

type ExportHistoryAlarm struct {
	DateType ExportType `form:"date_type"`
	AlarmFilterCommon
	TokenInfo
}

type AlarmFilterCommon struct {
	BusinessTypeID int               `json:"business_type_id" form:"business_type_id"`
	AreaID         int               `json:"area_id" form:"area_id"`
	DepartmentID   int               `json:"department_id" form:"department_id"`
	Type           model.AlarmType   `json:"type" form:"type"`
	Level          string            `json:"level" form:"level"`
	Status         model.AlarmStatus `json:"status" form:"status"`
	Begin          string            `json:"begin" form:"begin"`
	End            string            `json:"end" form:"end"`
}

type HandleAlarm struct {
	FindByID
}

type SetAlarmStatus struct {
	FindByID
	Status model.AlarmStatus `json:"status" binding:"required"`
}

type SyncAlarm struct {
	model.Alarm
	Path string `json:"path"`
}

type AlarmTotalOfTime struct {
	AreaID int                  `json:"area_id"`
	Type   AlarmTotalOfTimeType `json:"type"`
	TokenInfo
}

type AlarmPushToClient struct {
	TokenInfo
}

type AlarmRemove struct {
	FindByID
}

type CreateAlarmOutside struct {
	Type       model.AlarmType `json:"type"`
	DeviceID   int             `json:"device_id"`
	UnitID     int             `json:"unit_id"`
	DeviceName string          `json:"device_name"`
	UnitName   string          `json:"unit_name"`
	Value      string          `json:"value"`
	Flag       string          `json:"flag"`
	AreaID     int             `json:"area_id"`
	Level      string          `json:"level"`
	Message    string          `json:"message"`
}

type RecoveryAlarmOutside struct {
	Type     model.AlarmType `json:"type"`
	DeviceID int             `json:"device_id"`
	UnitID   int             `json:"unit_id"`
}
