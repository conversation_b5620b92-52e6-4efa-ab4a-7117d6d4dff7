package request

import (
	"tw_platform/pkg/model"
)

// 新建门禁列表
type NewDoorListItemReq struct {
	CreateDeviceBasic
	Devices []struct {
		Type       string         `json:"type,omitempty" gorm:"column:type" binging:"required"`
		DeviceName string         `json:"device_name" binging:"required"`
		Config     model.DoorInfo `json:"config" binging:"required"`
	} `json:"devices" binding:"required"`
}

type NewDoorReq NewDoorListItemReq

type EditDoorReq struct {
	Id         int    `json:"id" binding:"required"`
	DeviceName string `json:"device_name"`
	model.DoorInfo
}

// 一体机远程控门
type CtrDoorReq struct {
	Id  int    `form:"id"`
	Way string `form:"way" binding:"required"`
}

// 获取门的状态
type GetDoorStatusReq struct {
	DoorId int `form:"doorId"`
}

// 获取一体机人员信息
type GetDoorUserListReq struct {
	DoorId int `form:"doorId"`
}

// 添加人员
type AddDoorUserReq struct {
	DoorId         int    `json:"doorId"`
	EmployeeNo     string `json:"employeeNo"`
	Name           string `json:"name"`
	Password       string `json:"password"`
	LocalUIRight   bool   `json:"localUIRight"`
	UserVerifyMode string `json:"userVerifyMode"`
}

// 编辑人员
type EditDoorUserReq struct {
	DoorId         int    `json:"doorId"`
	Emno           string `json:"employeeNo"`
	Name           string `json:"name"`
	Pwd            string `json:"password"`
	UserVerifyMode string `json:"userVerifyMode"`
	LocalUIRight   bool   `json:"localUIRight"`
}

// 删除人员
type DelDoorUserReq struct {
	DoorId         int      `json:"doorId"`
	EmployeeNoList []string `json:"employeeNoList"`
}

// 获取卡片
type GetCardReq struct {
	Id         int    `form:"id"`
	EmployeeNo string `form:"employeeNo"`
}

// 新增卡片
type AddCardReq struct {
	DoorId     int    `json:"doorId"`
	EmployeeNo string `json:"employeeNo"`
	CardNo     string `json:"cardNo"`
	CardType   string `json:"cardType"`
}

// 删除卡片
type _delCardByNo struct {
	CardNo string `json:"cardNo"`
}
type DelCardReq struct {
	DoorId      int            `json:"doorId"`
	DelCardByNo []_delCardByNo `json:"cardNo"`
}

// 获取指纹数据
type GetFpDataReq struct {
	DoorId     int    `form:"doorId"`
	EmployeeNo string `form:"employeeNo"`
}

// 添加指纹
type AddFpDataReq struct {
	DoorId        int    `json:"doorId"`
	EmployeeNo    string `json:"employeeNo"`
	FingerPrintId int    `json:"fingerPrintId"`
	FingerData    string `json:"fingerData"`
	FingerType    string `json:"fingerType"`
}

// 获取添加指纹进度
type GetFpDataProcessReq struct {
	DoorId int `form:"doorId"`
}

// 获取删除指纹进度
type DelFpDataProcessReq struct {
	DoorId int `form:"doorId"`
}

// 删除指纹
type DelFingerReq struct {
	DoorId        int    `json:"doorId"`
	EmployeeNo    string `json:"employeeNo"`
	FingerPrintID []int  `json:"fingerPrintId"`
}

// 获取人脸
type GetFaceReq struct {
	DoorId     int    `form:"doorId"`
	EmployeeNo string `form:"employeeNo"`
}

// 删除人脸
type DelFaceReq struct {
	DoorId     int    `form:"doorId"`
	EmployeeNo string `form:"employeeNo"`
}
type _dcardInfo struct {
	CardNo   string `json:"cardNo"`
	CardType string `json:"cardType"`
}
type WebCmdFafceInfo struct {
	FaceLibType string `json:"faceLibType"`
	FDID        string `json:"FDID"`
	FPID        string `json:"FPID"`
}

type _oneUser struct {
	EmployeeNo        string       `json:"employeeNo"`
	CardList          []_dcardInfo `json:"cardList"`
	FingerPrintIDList []int        `json:"fingerPrintIDList"`
	Face              bool         `json:"face"`
}

type _syncuserinfo struct {
	Id             int        `json:"id"`
	EmployeeNoList []_oneUser `json:"employeeNoList"`
}
type StartSyncReq struct {
	Source      _syncuserinfo `json:"source"`
	Destination []int         `json:"destination"`
}
