package request

import (
	"mime/multipart"
	"tw_platform/pkg/model"
)

type CreateInspect struct {
	AreaID         int                `json:"area_id" binding:"required"`
	Title          string             `json:"title" binding:"required"`
	Content        string             `json:"content" binding:"required"`
	Detail         string             `json:"detail" binding:"required"`
	Comment        string             `json:"comment"`
	Cycle          model.InspectCycle `json:"cycle" binding:"required"`
	CheckUserID    int                `json:"check_user_id" binding:"required"`
	ExecuteUserIDs []int              `json:"execute_user_ids" binding:"required"`
	LeadingUserIDs []int              `json:"leading_user_ids" binding:"required"`
	Plans          []struct {
		Date    string `json:"date" binding:"required"`
		StartAt string `json:"start_at" binding:"required"`
		EndAt   string `json:"end_at" binding:"required"`
	} `json:"plans" binding:"required"`

	TokenInfo
}

type InspectList struct {
	PageInfo
}

type InspectPlanList struct {
	InspectionID int `form:"inspection_id"`
	PageInfo
}

type ExecuteInspectPlan struct {
	PlanID     int      `json:"plan_id" binding:"required"`
	Comment    string   `json:"comment"`
	Suggestion string   `json:"suggestion"`
	Images     []string `json:"images"`
	TokenInfo
}

type InspectionUpload struct {
	File *multipart.FileHeader `form:"file" binding:"required"`
}

type InspectionDeleteFile struct {
	Path string `json:"path" binding:"required"`
}
