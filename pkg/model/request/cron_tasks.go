package request

import "tw_platform/pkg/model"

type CreateCronTask struct {
	Name        string                   ` json:"name"`
	Enabled     string                   ` json:"enabled"` // ON OFF
	Description *string                  ` json:"description"`
	Condition   *model.CronTaskCondition `json:"condition,omitempty" `
	Action      *model.CronTaskAction    `json:"action,omitempty"`
}
type DeleteCronTask struct {
	ID int `json:"id" form:"id" binding:"required"`
}
type UpdateCronTask struct {
	FindByID
	CreateCronTask
}
type CronTaskAll struct {
	model.CronTask
	Condition *model.CronTaskCondition `json:"condition,omitempty" gorm:"column:condition;serializer:json"`
	Action    *model.CronTaskAction    `json:"action,omitempty" gorm:"column:action;serializer:json"`
}
