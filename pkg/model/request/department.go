package request

import "tw_platform/pkg/model"

type CreateDepartment struct {
	Name     string `json:"name" binding:"required"`
	ParentID int    `json:"parent_id" binding:"required"`
}

type DepartmentTreeByUserID struct {
	TokenInfo
	BusinessTypeID int ` form:"business_type_id"`
}

type UpdateDepartment struct {
	ID     int                    `json:"id" binding:"required"`
	Name   string                 `json:"name"`
	Status model.DepartmentStatus `json:"status"`
}

type DeleteDepartment struct {
	FindByID
}

type DepartmentListByFilter struct {
	ID       int `form:"id"`
	ParentID int `form:"parent_id"`
}
