package request

import "tw_platform/pkg/model"

type CreateGateway struct {
	Name    string            `json:"name" binding:"required"`
	IP      string            `json:"ip" binding:"required"`
	Version string            `json:"version"`
	Type    model.GatewayType `json:"type" binding:"required"`
	Model   string            `json:"model" binding:"required"`

	Ports []CreateGatewayPort `json:"ports" binding:"required"`
}

type CreateGatewayPort struct {
	Port string                `json:"port" binding:"required"`
	Name string                `json:"name" binding:"required"`
	Type model.GatewayPortType `json:"type" binding:"required"`
}

type IsAccept struct {
	IP   string `form:"ip" binding:"required"`
	Port string `form:"port" binding:"required"`
}

type UpdateGateway struct {
	ID    int    `json:"id" binding:"required"`
	Name  string `json:"name"`
	IP    string `json:"ip"`
	Model string `json:"model"`
}

type RegisterPceMPorts struct {
	Port string                `json:"port"`
	Name string                `json:"name"`
	Type model.GatewayPortType `json:"type"`
}
type RegisterPceM struct {
	FlashID string              `json:"flash_id"`
	Version string              `json:"version"`
	IP      string              `json:"ip"`
	Model   string              `json:"model"`
	Ports   []RegisterPceMPorts `json:"ports"`
}

type RegisterDeviceUnit struct {
	ID    int    `json:"id"`
	Name  string `json:"name"`
	Flag  string `json:"flag"`
	Level string `json:"level"`
}
type RegisterDevice struct {
	ID         int                  `json:"id"`
	Name       string               `json:"name"`
	DeviceType string               `json:"device_type"`
	Unit       []RegisterDeviceUnit `json:"unit"`
}

type Register struct {
	FID         string           `json:"fid"`
	Version     string           `json:"version"`
	IP          string           `json:"ip"`
	Model       string           `json:"model"`
	GWName      string           `json:"gw_name"`
	DeviceCount int              `json:"device_count"`
	DeviceInfo  []RegisterDevice `json:"device_info"`
}

type GatewayList struct {
	PageInfo
	Type  model.GatewayType `form:"type"`
	Model string            `form:"model"`
}

type IsAcceptMulti struct {
	IP    string   `json:"ip" binding:"required"`
	Ports []string `json:"ports" binding:"required"`
}

type UpdateGatewayPort struct {
	ID   int                   `json:"id" binding:"required"`
	Name string                `json:"name"`
	Port string                `json:"port"`
	Type model.GatewayPortType `json:"type"`
}

type AddGatewayPort struct {
	ID   int                   `json:"id" binding:"required"`
	Name string                `json:"name" binding:"required"`
	Port string                `json:"port" binding:"required"`
	Type model.GatewayPortType `json:"type" binding:"required"`
}

type DeleteGatewayPort struct {
	FindByID
}
type GatewayPortList struct {
	FindByID
}

type DeleteGateway struct {
	FindByID
}

type GatewayOPCNodeList struct {
	FindByID
}
type GetOPCNodeVariable struct {
	IP   string `json:"ip" binding:"required"`
	Port string `json:"port" binding:"required"`
	NS   int    `json:"ns" binding:"required"`
	ID   int    `json:"id" binding:"required"`
}

type ScanGateway struct {
	Name string `form:"name"`
}
type GatewayAlarmBinding struct {
	BindingID  int   `json:"binding_id" binding:"required"`
	GatewayIDs []int `json:"gateway_ids" binding:"required"`
}

type GetGatewayAlarmBinding struct {
	BindingID int `form:"binding_id" binding:"required"`
}

type ReplaceGateway struct {
	FID    string `json:"fid"`
	NewFID string `json:"-"`
}

type ReplaceGateway1 struct {
	ID    int `json:"id"`
	NewID int `json:"new_id"`
}
