package request

import (
	"mime/multipart"
	"tw_platform/pkg/model"
)

type UpdateSystem struct {
	Name      string `json:"name"`
	Copyright string `json:"copyright"`
}

type UpdateWebPage struct {
	File *multipart.FileHeader `form:"file" binding:"required"`
	Path string                `form:"path"`
}

type UpgradeLogo struct {
	File *multipart.FileHeader `form:"file" binding:"required"`
}

type SetStandby struct {
	Status model.StandbyStatus `json:"status" binding:"required"`
	Type   model.StandbyType   `json:"type" binding:"required"`
	IP     string              `json:"ip" binding:"required"`
	Port   string              `json:"port" binding:"required"`
}

type SystemStatistic struct {
	AlarmFilterCommon
	TokenInfo
	AlarmID        int `json:"alarm_id" form:"alarm_id"`
	BusinessTypeID int `json:"business_type_id" form:"business_type_id"`
}

type SystemStatisticApp struct {
	TokenInfo
}
