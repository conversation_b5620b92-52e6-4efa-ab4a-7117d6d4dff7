package model

import "time"

type LightScheduleStatus uint8

const (
	LightScheduleStatusActive LightScheduleStatus = 1
	LightScheduleStatusClosed LightScheduleStatus = 2
)

type LightScheduleType uint8

const (
	LightScheduleTypeOn LightScheduleType = iota + 1
	LightScheduleTypeOff
	LightScheduleTypeAdjustable
)

type LightSchedule struct {
	ID         int                 `json:"id" gorm:"column:id;primaryKey"`
	AreaID     int                 `json:"area_id,omitempty" gorm:"column:area_id;not null;type:int;index"`
	Name       string              `json:"name" gorm:"column:name;not null;type:varchar(64);default:''"`
	Cron       string              `json:"cron,omitempty" gorm:"column:cron;not null;type:varchar(64)"`
	CronID     int                 `json:"-" gorm:"column:cron_id;not null;type:int"`
	Status     LightScheduleStatus `json:"status,omitempty" gorm:"column:status;not null;type:int;default:0"`
	Type       LightScheduleType   `json:"type,omitempty" gorm:"column:type;not null;type:int;default:0"`
	BeginAt    *time.Time          `json:"begin_at,omitempty" gorm:"column:begin_at;type:timestamptz"`
	EndAt      *time.Time          `json:"end_at,omitempty" gorm:"column:end_at;type:timestamptz"`
	LastExecAt *time.Time          `json:"last_exec_at,omitempty" gorm:"column:last_exec_at;type:timestamptz"`
	CreatedAt  *time.Time          `json:"created_at,omitempty" gorm:"column:created_at;autoCreateTime;not null;type:timestamptz"`
	UpdatedAt  *time.Time          `json:"updated_at,omitempty" gorm:"column:updated_at;autoUpdateTime;not null;type:timestamptz"`
}

func (LightSchedule) TableName() string {
	return "light_schedules"
}

type LightScheduleDetail struct {
	ID         int        `json:"id" gorm:"column:id;primaryKey"`
	ScheduleID int        `json:"schedule_id" gorm:"column:schedule_id;not null;type:int;index"`
	DeviceID   int        `json:"device_id" gorm:"column:device_id;not null;type:int"`
	UnitID     int        `json:"unit_id" gorm:"column:unit_id;not null;type:int"`
	Value      string     `json:"value" gorm:"column:value;not null;type:varchar(64)"`
	ExecedAt   *time.Time `json:"execed_at" gorm:"column:execed_at;type:timestamptz;index"`
}

func (LightScheduleDetail) TableName() string {
	return "light_schedule_details"
}

type LightScheduleLog struct {
	ID       int        `json:"id" gorm:"column:id;primaryKey"`
	DetailID int        `json:"detail_id" gorm:"column:detail_id;not null;type:int;index"`
	ExecedAt *time.Time `json:"execed_at" gorm:"column:execed_at;type:timestamptz"`
}

func (LightScheduleLog) TableName() string {
	return "light_schedule_logs"
}

type LightScheduleWithAction struct {
	LightSchedule
	Actions []LightScheduleDetail `json:"actions" gorm:"foreignKey:ScheduleID;references:ID"`
}
