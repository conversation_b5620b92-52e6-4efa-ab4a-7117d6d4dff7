package model

type DOBinding interface {
	Type() string
	Trigger() int
	DOID() int
}

type DeviceAlarmBinding struct {
	ID        int `json:"id" gorm:"column:id;primary_key;auto_increment:false"`
	BindingID int `json:"binding_id" gorm:"column:binding_id;primary_key;auto_increment:false"`
}

func (DeviceAlarmBinding) TableName() string {
	return "device_alarm_bindings"
}

func (dab DeviceAlarmBinding) Type() string {
	return "device"
}

func (dab DeviceAlarmBinding) Trigger() int {
	return dab.ID
}
func (dab DeviceAlarmBinding) DOID() int {
	return dab.BindingID
}
