package model

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

type AreaStatus uint8

const (
	AreaStatusNormal AreaStatus = iota + 1
	AreaStatusHidden
)

type Area struct {
	ID        int        `json:"id,omitempty" gorm:"column:id;primaryKey"`
	Name      string     `json:"name,omitempty" gorm:"column:name;type:varchar(64);not null"`
	ParentID  int        `json:"parent_id,omitempty" gorm:"column:parent_id;type:int;not null"`
	Longitude string     `json:"longitude,omitempty" gorm:"column:longitude;type:varchar(32)"`
	Latitude  string     `json:"latitude,omitempty" gorm:"column:latitude;type:varchar(32)"`
	Status    AreaStatus `gorm:"column:status;type:int2;not null" json:"status,omitempty"`
	CreatedAt *time.Time `gorm:"column:created_at;autoCreateTime;not null;type:timestamptz" json:"created_at,omitempty"`
	UpdatedAt *time.Time `gorm:"column:updated_at;autoUpdateTime;not null;type:timestamptz" json:"updated_at,omitempty"`
}

func (Area) TableName() string {
	return "areas"
}

type AreaRelation struct {
	ParentID int   `json:"parent_id" gorm:"column:parent_id;type:int;not null;primaryKey"`
	ChildID  int   `json:"child_id" gorm:"column:child_id;type:int;not null;primaryKey;index"`
	Depth    uint8 `json:"depth" gorm:"column:depth;type:int;not null"`
}

func (AreaRelation) TableName() string {
	return "area_relations"
}

type AreaWithDevices struct {
	Area
	Devices []Device `json:"devices" gorm:"foreignKey:AreaID;references:ID"`
}

type AreaWithDeviceConfigs struct {
	Area
	Devices []DeviceWithConfig `json:"devices" gorm:"foreignKey:AreaID;references:ID"`
}

type AreaWithTypes struct {
	Area
	Types []AreaTypeCountWithName `json:"types" gorm:"foreignKey:AreaID;references:ID"`
}

type AreaWithAlarmCounts struct {
	Area
	Types []AreaAlarmCountWithName `json:"types" gorm:"foreignKey:AreaID;references:ID"`
}

type AreaSceneType uint8

const (
	SceneType2D AreaSceneType = iota + 1
	SceneType3D
	SceneTypeScreen     //大屏
	SceneTypeInspection //巡检路径
	SceneTypeParam      //组态
	SceneTypeMap        //地图
)

type AreaSceneExType uint8

const (
	AreaSceneExTypeNormal AreaSceneExType = iota + 1
	AreaSceneExTypeBigCard
)

type AreaScene struct {
	ID             int             `json:"id,omitempty" gorm:"column:id;primaryKey"`
	AreaID         int             `json:"area_id" gorm:"column:area_id;not null;type:int;index:idx_area_business_type"`
	BusinessTypeID int             `json:"-" gorm:"column:business_type_id;not null;type:int;index:idx_area_business_type"`
	Type           AreaSceneType   `json:"type,omitempty" gorm:"column:type;type:int;not null"`
	ImgPath        *string         `json:"img_path,omitempty" gorm:"column:img_path;type:varchar(255)"`
	Data           string          `json:"data,omitempty" gorm:"column:data;type:text"`
	ExType         AreaSceneExType `json:"ex_type,omitempty" gorm:"column:ex_type;type:int;not null;default:1"`
	CreatedAt      *time.Time      `json:"created_at,omitempty" gorm:"column:created_at;autoCreatetime;not null;type:timestamptz"`
	UpdatedAt      *time.Time      `json:"updated_at,omitempty" gorm:"column:updated_at;autoUpdatetime;not null;type:timestamptz"`
}

func (AreaScene) TableName() string {
	return "area_scenes"
}

func (as *AreaScene) AfterFind(tx *gorm.DB) error {
	if as.ImgPath == nil {
		return nil
	}
	path := fmt.Sprint(StaticScene, *as.ImgPath)
	as.ImgPath = &path
	return nil
}

type AreaTypeCount struct {
	AreaID         int `json:"-" gorm:"column:area_id;primaryKey"`
	BusinessTypeID int `json:"business_type_id" gorm:"column:business_type_id;primaryKey"`
	Count          int `json:"count" gorm:"column:count;type:int;not null"`
}

func (AreaTypeCount) TableName() string {
	return "area_type_counts"
}

type AreaTypeCountWithName struct {
	AreaTypeCount
	Name    string `json:"name" gorm:"column:name"`
	Alarmed bool   `json:"alarmed" gorm:"column:alarmed"`
}

type AreaAlarmCountWithName struct {
	AreaTypeCount
	Name        string `json:"name" gorm:"column:name"`
	Count       int    `json:"count,omitempty" gorm:"column:count;type:int;not null"`
	AlarmCounts int    `json:"alarm_counts" gorm:"column:alarm_counts"`
}

type AreaDefaultSceneType struct {
	AreaID         int   `json:"area_id" gorm:"column:area_id;primaryKey"`
	BusinessTypeID int   `json:"business_type_id" gorm:"column:business_type_id;primaryKey"`
	SceneType      uint8 `json:"scene_type" gorm:"column:scene_type;type:int;not null"`
}

func (AreaDefaultSceneType) TableName() string {
	return "area_default_scene_types"
}

type AreaWithDefaultSceneType struct {
	Area
	DefaultSceneType []AreaDefaultSceneType `json:"default_scene_types" gorm:"foreignkey:AreaID;references:ID"`
}

type AreaWithAlarmed struct {
	Area
	Alarms []Alarm `json:"-" gorm:"foreignkey:AreaID;references:ID"`
}
