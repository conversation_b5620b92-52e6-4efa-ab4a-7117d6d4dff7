package model

import "time"

type UserStatus uint8

const (
	UserStatusNormal UserStatus = iota + 1
	UserStatusSuspended
	QywxInit
	QywxCompleted
)

type User struct {
	ID        int        `json:"id" gorm:"column:id;primaryKey"`
	Name      string     `json:"name,omitempty" gorm:"column:name;type:varchar(64);not null"`
	Nickname  string     `json:"nickname,omitempty" gorm:"column:nickname;type:varchar(64);not null"`
	Phone     string     `json:"phone,omitempty" gorm:"column:phone;type:varchar(32);not null"`
	Email     string     `json:"email,omitempty" gorm:"column:email;type:varchar(64);not null"`
	Password  string     `json:"-" gorm:"column:password;type:varchar(64);not null"`
	RoleID    int        `json:"role_id,omitempty" gorm:"column:role_id;type:int;not null"`
	Status    UserStatus `json:"status,omitempty" gorm:"column:status;type:int;not null"`
	WechatID  string     `json:"wechat_id,omitempty" gorm:"column:wechat_id;type:varchar(64);not null;index;default:''"`
	CreatedAt *time.Time `json:"created_at,omitempty" gorm:"column:created_at;aotuCreatetime;not null;type:timestamptz"`
	UpdatedAt *time.Time `json:"updated_at,omitempty" gorm:"column:updated_at;autoUpdatetime;not null;type:timestamptz"`
}

func (User) TableName() string {
	return "users"
}

type UserArea struct {
	UserID int `json:"user_id" gorm:"column:user_id;primaryKey"`
	AreaID int `json:"area_id" gorm:"column:area_id;primaryKey"`
}

func (UserArea) TableName() string {
	return "user_areas"
}

type UserDepartment struct {
	UserID       int `json:"user_id" gorm:"column:user_id;primaryKey"`
	DepartmentID int `json:"department_id" gorm:"column:department_id;primaryKey"`
}

func (UserDepartment) TableName() string {
	return "user_departments"
}

type UserBusinessType struct {
	UserID         int `json:"user_id" gorm:"column:user_id;primaryKey"`
	BusinessTypeID int `json:"business_type_id" gorm:"column:business_type_id;primaryKey"`
}

func (UserBusinessType) TableName() string {
	return "user_business_types"
}

type UserWithRole struct {
	User
	RoleName string `json:"role_name,omitempty" gorm:"column:role_name"`
}
