package model

import (
	"time"
)

type Driver struct {
	ID           int           `json:"id" gorm:"column:id;primaryKey"`
	Agree        string        `json:"agree" gorm:"column:agree;not null;type:varchar(16)"`
	Use          string        `json:"use" gorm:"column:use;not null;type:varchar(16)"`
	ManuFacturer string        `json:"manu_facturer" gorm:"column:manu_facturer;type:varchar(64);not null"`
	DevType      string        `json:"dev_type" gorm:"column:dev_type;type:varchar(64);not null"`
	Model        string        `json:"model" gorm:"column:model;type:varchar(64);not null"`
	Name         string        `json:"name" gorm:"column:name;type:varchar(64);not null"`
	Content      DriverContent `json:"content,omitempty" gorm:"column:content;serializer:json;not null;type:jsonb"`
	CreatedAt    time.Time     `json:"created_at" gorm:"column:created_at;aotuCreatetime;not null;type:timestamptz"`
	UpdatedAt    time.Time     `json:"updated_at" gorm:"column:updated_at;autoUpdatetime;not null;type:timestamptz"`
}

func (Driver) TableName() string {
	return "drivers"
}

type DriverContent struct {
	Config     string     `json:"config"`
	DriverInfo DriverInfo `json:"driver" `
}

type DriverInfo struct {
	Driver string `json:"driver"`
	Name   string `json:"name"`
}

type DriverWithScript struct {
	Driver
	Script string `json:"script" gorm:"column:script"`
}
