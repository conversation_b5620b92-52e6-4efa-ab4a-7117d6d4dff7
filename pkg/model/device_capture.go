package model

type DeviceCaptureStatus uint8

const (
	DeviceCaptureStatusOn DeviceCaptureStatus = iota + 1
	DeviceCaptureStatusOff
)

type GetDeviceCaptureStatusResp struct {
	Status DeviceCaptureStatus `json:"status"`
}

type SetDeviceCaptureStatusReq struct {
	Status DeviceCaptureStatus `json:"status"`
}

type SetDeviceCaptureStatusResp struct {
	Code string `json:"code"`
	Msg  string `json:"msg"`
}
