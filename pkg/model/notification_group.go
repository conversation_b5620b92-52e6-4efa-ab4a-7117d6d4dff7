package model

import "time"

const (
	GprsLocalType = iota
	GprsCloudType
)

type NotificationConfig struct {
	Member    []int    `json:"member,omitempty"`
	Email     []string `json:"email,omitempty"`
	Sms       []string `json:"sms,omitempty"`
	Call      []string `json:"call,omitempty"`
	Broadcast string   `json:"broadcast,omitempty"`
	DingDIng  string   `json:"dingding,omitempty"`
	Wechat    string   `json:"wechat,omitempty"`
	MyHook    string   `json:"myhook,omitempty"`
	Week      []int    `json:"week"` //每周几
}

// 通知组（不仅仅用于告警通知） 可以按成员通知，也可以直接按方式通知 可关联成员（成员可以选择通知方式)
type NotificationGroup struct {
	ID               int                `gorm:"column:id;primaryKey" json:"id"`
	Name             string             `gorm:"column:name;type:varchar(64);not null" json:"name"`                                          // 名称
	NotificationType string             `gorm:"column:notification_type;type:varchar(64);not null" json:"notification_type"`                // 通知类型MEMBER-成员通知 EMAIL-邮箱通知 SMS-短信通知 CALL-电话通知
	Status           string             `gorm:"column:status;type:varchar(64);not null" json:"status"`                                      // 通知状态ON-启用 OFF-停用
	Config           NotificationConfig `json:"notification_config,omitempty;type:jsonb" gorm:"column:notification_config;serializer:json"` // 通知配置
	Description      *string            `gorm:"column:description;type:varchar(255)" json:"description"`                                    // 描述
	CreatedAt        *time.Time         `gorm:"column:created_at;autoCreateTime;not null;type:timestamptz" json:"created_at"`               // 创建时间
	UpdatedAt        *time.Time         `gorm:"column:updated_at;autoUpdateTime;not null;type:timestamptz" json:"updated_at"`               // 更新时间                                                                         // 备注
}

func (NotificationGroup) TableName() string {
	return "notification_groups"
}

type NotificationUsers struct {
	NotificationGroupId int `gorm:"column:notification_id;primaryKey" json:"-"`
	UserId              int `gorm:"column:user_id;primaryKey" json:"user_id"`
	Times               int `gorm:"column:times;type:int" json:"times"`
}

func (NotificationUsers) TableName() string {
	return "notification_users"
}

type Gprs struct {
	Type         int    `json:"type"`
	Gprsenable   bool   `json:"gprs_enable" `
	GprsIp       string `json:"gprs_ip"  `
	Type_2       int    `json:"type_2"`
	Gprsenable_2 bool   `json:"gprs_enable_2" `
	GprsIp_2     string `json:"gprs_ip_2"  `
}
type EmailClient struct {
	IsOpen             bool   `json:"is_open" `
	ServerHost         string `json:"server_host" binding:"required"`
	ServerPort         int    `json:"server_port" binding:"required"`
	FromPasswd         string `json:"from_passwd" binding:"required"`
	FromEmail          string `json:"from_email" binding:"required"`
	InsecureSkipVerify bool   `json:"insecure_skipverify" `
}

type NoticeSystemConfig struct {
	ID    int         `gorm:"column:id;primaryKey" json:"id"`
	Gprs  Gprs        `json:"gprs,omitempty" gorm:"column:gprs;serializer:json;type:jsonb"`
	Email EmailClient `json:"email,omitempty" gorm:"column:email;serializer:json;type:jsonb"`
}

func (NoticeSystemConfig) TableName() string {
	return "notice_system_config"
}
