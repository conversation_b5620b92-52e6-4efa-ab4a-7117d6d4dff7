package model

type DoorInfo struct {
	Type         string `json:"type"`
	Ip           string `json:"ip"`
	ManName      string `json:"manName" gorm:"column:man_name"`
	ManPwd       string `json:"manPwd"  gorm:"column:man_pwd"`
	ManuFacturer string `json:"manuFacturer" gorm:"column:manu_facturer"`
	Port         int    `json:"port"`
	Name         string `json:"name"`
	Ability      string `json:"ability"`
	SubCount     int    `json:"subCount"`
}

type Door struct {
	Id int64
	DoorInfo
}

type HkAddFaceParam struct {
	ImageType string
	ImageName string
}
