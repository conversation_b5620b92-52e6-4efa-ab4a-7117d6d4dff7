package model

import (
	"errors"
	"time"

	jsoniter "github.com/json-iterator/go"
	"gorm.io/datatypes"
)

type DeviceActivity uint8

const (
	DeviceActivityEnable DeviceActivity = iota + 1
	DeviceActivityDisable
)

type DeviceType uint8

const (
	DeviceTypePceM       DeviceType = iota + 1
	_                               //合并协议设备与非协议设备
	DeviceTypePceNetwork            //todo 取消
	DeviceTypeNetwork
	DeviceTypeCustom
	DeviceTypeCamera
	DeviceTypeNvr
	DeviceTypeDoor
	DeviceTypeOpc
	DeviceTypeAsset
	DeviceTypeReport
)

type DeviceStatus uint8

const (
	DeviceStatusNormal DeviceStatus = iota + 1
	DeviceStatusAlarmed
	DeviceStatusOffline
	DeviceStatusGatewayOffline
)

type DeviceDiffEvent uint8

const (
	DeviceDiffAdd DeviceDiffEvent = iota + 1
	DeviceDiffDel
)

type DeviceUnitExtraType uint8

const (
	DeviceUnitExtraTypeControlLight DeviceUnitExtraType = iota + 1
)

type Device struct {
	ID             int            `json:"id,omitempty" gorm:"column:id;primaryKey"`
	Name           string         `json:"name,omitempty" gorm:"column:name;type:varchar(64);not null"`
	Type           string         `json:"type,omitempty" gorm:"column:type;type:varchar(64);not null"`
	Activity       DeviceActivity `json:"activity,omitempty" gorm:"column:activity;type:int;not null"`
	GatewayID      *int           `json:"gateway_id,omitempty" gorm:"column:gateway_id;index;type:int"`
	PortID         *int           `json:"-" gorm:"column:port_id;index;type:int"`
	InternalID     *int           `json:"internal_id,omitempty" gorm:"column:internal_id;type:int"`
	Status         DeviceStatus   `json:"status,omitempty" gorm:"column:status;type:int;not null"`
	AreaID         int            `json:"area_id,omitempty" gorm:"column:area_id;index;not null;type:int"`
	BusinessTypeID int            `json:"business_type_id,omitempty" gorm:"column:business_type_id;not null;type:int"`
	CreatedAt      *time.Time     `json:"created_at,omitempty" gorm:"column:created_at;aotuCreatetime;not null;type:timestamptz"`
	UpdatedAt      *time.Time     `json:"updated_at,omitempty" gorm:"column:updated_at;autoUpdatetime;not null;type:timestamptz"`
}

func (Device) TableName() string {
	return "devices"
}

type Config struct {
	ID       int          `json:"id" gorm:"column:id;primaryKey;autoIncrement:false"`
	DriverID *int         `json:"driver_id,omitempty" gorm:"column:driver_id;type:int;index"`
	Type     DeviceType   `json:"type" gorm:"column:type;type:int;not null"`
	Config   DeviceConfig `json:"config,omitempty" gorm:"column:config;serializer:json;type:jsonb"`
}

func (Config) TableName() string {
	return "device_configs"
}

type DeviceConfig struct {
	CommonDevice *CommonDevice `json:"common_device,omitempty"`
	VideoDevice  *VideoDevice  `json:"video_device,omitempty"`
	DoorDevice   *DoorInfo     `json:"door_device,omitempty"`
	CustomDevice *CustomDevice `json:"custom_device,omitempty"`
	OpcDevice    *OpcDevice    `json:"opc_device,omitempty"`
	AssetDevice  *AssetDevice  `json:"asset_device,omitempty"`
	ReportDevice *ReportDevice `json:"report_device,omitempty"`
}

func (d Config) GetConfigUint(uintID int) (DeviceConfigUnit, error) {
	var unit DeviceConfigUnit
	switch d.Type {
	case DeviceTypeNetwork, DeviceTypePceM:
		if d.Config.CommonDevice == nil {
			return unit, errors.New("config not found")
		}
		if len(d.Config.CommonDevice.Unit)-1 < uintID {
			return unit, errors.New("device config unit not found")
		}
		unit = d.Config.CommonDevice.Unit[uintID]
	case DeviceTypeCustom:
		if d.Config.CustomDevice == nil {
			return unit, errors.New("config not found")
		}
		if len(d.Config.CustomDevice.Unit)-1 < uintID {
			return unit, errors.New("device config unit not found")
		}
		unit = d.Config.CustomDevice.Unit[uintID].DeviceConfigUnit
	}
	return unit, nil
}
func (d Config) GetConfigPath() (string, error) {
	switch d.Type {
	case DeviceTypeNetwork, DeviceTypePceM:
		return "common_device", nil
	case DeviceTypeCustom:
		return "custom_device", nil
	case DeviceTypeReport:
		return "report_device", nil
	}
	return "", errors.New("device config type not found")
}

type DeviceWithConfig struct {
	Device
	Config Config `json:"config,omitempty" gorm:"foreignKey:ID;references:ID"`
}

type DeviceWithDriver struct {
	Device
	Config DeviceWithScript `json:"config,omitempty" gorm:"foreignKey:ID;references:ID"`
}

type DeviceWithScript struct {
	Config
	Script string `json:"script" gorm:"column:script"`
}

type DeviceConfigUnit struct {
	Activity     bool                `json:"activity"`
	AlarmLevel   string              `json:"alarmLevel"`
	Alarmenable  bool                `json:"alarmenable"`
	Alarmstatus  DeviceStatus        `json:"alarmstatus"`
	FilterCnt    int                 `json:"filterCnt"`
	Flag         string              `json:"flag"`
	High         int                 `json:"high"`
	HighName     string              `json:"highname,omitempty"`
	HighHyse     int                 `json:"highHyse"`
	HighHigh     int                 `json:"highhigh"`
	HighHighName string              `json:"highhighname,omitempty"`
	ID           int                 `json:"id"`
	Low          int                 `json:"low"`
	LowLow       int                 `json:"lowlow"`
	LowLowName   string              `json:"lowlowname,omitempty"`
	LowName      string              `json:"lowname,omitempty"`
	LowHyse      int                 `json:"lowHyse"`
	Name         string              `json:"name,omitempty"`
	Priority     int                 `json:"priority"`
	RegCmd       int                 `json:"regCmd,omitempty"`
	RegNo        int                 `json:"regNo,omitempty"`
	RegNum       int                 `json:"regNum,omitempty"`
	RegRw        string              `json:"regRw,omitempty"`
	Saveenable   bool                `json:"saveenable"`
	Savetime     int                 `json:"savetime"`
	Status       string              `json:"status"`
	SubValue     datatypes.JSON      `json:"subValue,omitempty"`
	Value        string              `json:"value"`
	ValueType    string              `json:"valueType,omitempty"`
	EffectTime   []string            `json:"effecttime,omitempty"`
	Type         string              `json:"type,omitempty"`
	Action       int                 `json:"action"`
	Relpoly      *DeviceUnitRelpoly  `json:"relpoly,omitempty"`
	ExtraType    DeviceUnitExtraType `json:"extra_type,omitempty"`
	ExtraData    string              `json:"extra_data,omitempty"`
	ExtraName    string              `json:"extra_name,omitempty"`
}

func (dcu *DeviceConfigUnit) UnmarshalJSON(data []byte) error {
	type NewDeviceConfigUnit DeviceConfigUnit
	var (
		temp = struct {
			*NewDeviceConfigUnit
			LowHyse  float64 `json:"lowHyse,omitempty"`
			HighHyse float64 `json:"highHyse,omitempty"`
		}{
			NewDeviceConfigUnit: (*NewDeviceConfigUnit)(dcu),
		}
	)
	err := jsoniter.Unmarshal(data, &temp)
	if err != nil {
		return err
	}

	dcu.LowHyse = int(temp.LowHyse)
	dcu.HighHyse = int(temp.HighHyse)

	return nil
}

type DeviceUnitRelpoly struct {
	OnceTime int64  `json:"oncetime,omitempty"`
	Type     string `json:"type,omitempty"`
}

func (dfu DeviceConfigUnit) MarshalBinary() ([]byte, error) {
	return jsoniter.Marshal(dfu)
}

func (dfu *DeviceConfigUnit) UnmarshalBinary(data []byte) error {
	return jsoniter.Unmarshal(data, dfu)
}

type CommonDevice struct {
	Addr  int    `json:"addr,omitempty"`
	Agree string `json:"agree,omitempty"`
	Com   struct {
		BaudRate  int    `json:"baudRate,omitempty"`
		CheckType string `json:"checkType,omitempty"`
		DataBit   int    `json:"dataBit,omitempty"`
		StopBit   int    `json:"stopBit,omitempty"`
	} `json:"com,omitempty"`
	Devtype      string `json:"devtype,omitempty"`
	Driver       string `json:"driver,omitempty"`
	Group        int    `json:"group,omitempty"`
	Manufacturer string `json:"manufacturer,omitempty"`
	Modbusid     int    `json:"modbusid,omitempty"`
	Model        string `json:"model,omitempty"`
	Name         string `json:"name,omitempty"`
	Net          struct {
		IP       string `json:"ip,omitempty"`
		Port     int    `json:"port,omitempty"`
		SockType string `json:"sockType,omitempty"`
	} `json:"net,omitempty"`
	Reserve      interface{}        `json:"reserve,omitempty"`
	Status       string             `json:"status,omitempty"`
	Step         int                `json:"step,omitempty"`
	Systemalarm  bool               `json:"systemalarm,omitempty"`
	TimeInterval int                `json:"timeInterval,omitempty"`
	TimeOut      int                `json:"timeOut,omitempty"`
	Unit         []DeviceConfigUnit `json:"unit,omitempty"`
	Use          string             `json:"use,omitempty"`
}
type VideoDevice struct {
	Brand     string `json:"brand,omitempty"`
	ChannelID int    `json:"channel_id"`
	IP        string `json:"ip,omitempty"`
	Name      string `json:"name,omitempty"`
	Password  string `json:"password,omitempty"`
	Stream    string `json:"stream,omitempty"`
}
type CustomDeviceUnits []struct {
	DeviceConfigUnit
	DataSourceType    int               `json:"data_source"`                   //数据来源 0无来源 1直接从现有测点提取 2取平均值 3数值之间随机生成 4pue 100脚本生成
	DataSourceUnit    *CustomSourceUnit `json:"data_source_unit,omitempty"`    //从现有测点直接获取
	DataSourceAverage []*CustomUnit     `json:"data_source_average,omitempty"` //从若干个设备测点取平均值
	DataSourcePue     *CustomPue        `json:"data_source_pue,omitempty"`
	DataSourceRange   *CustomRange      `json:"data_source_range,omitempty"`
	DataSourceScript  string            `json:"data_source_script,omitempty"` //脚本
}

func (cdu CustomDeviceUnits) Len() int {
	return len(cdu)
}

func (cdu CustomDeviceUnits) Less(i, j int) bool {
	return cdu[i].ID < cdu[j].ID
}

func (cdu CustomDeviceUnits) Swap(i, j int) {
	cdu[i], cdu[j] = cdu[j], cdu[i]
}

type CustomDevice struct {
	Activity     bool              `json:"activity,omitempty"`
	Devtype      string            `json:"devtype,omitempty"`
	Manufacturer string            `json:"manufacturer,omitempty"`
	Model        string            `json:"model,omitempty"`
	TimeInterval int               `json:"timeInterval,omitempty"`
	Remark       string            `json:"remark,omitempty"` // 备注
	Unit         CustomDeviceUnits `json:"unit,omitempty"`
}
type OpcDevice struct {
	Activity     bool   `json:"activity,omitempty"`
	Devtype      string `json:"devtype,omitempty"`
	Manufacturer string `json:"manufacturer,omitempty"`
	Model        string `json:"model,omitempty"`
	TimeInterval int    `json:"timeInterval,omitempty"`
	Remark       string `json:"remark,omitempty"` // 备注
	Unit         []struct {
		DeviceConfigUnit
		NodeID        string `json:"nodeId"`
		NodeWriteMask bool   `json:"nodeWriteMask"`
		NodeDataType  string `json:"nodeDataType"`
	} `json:"unit,omitempty"`
}
type DeviceDiff struct {
	DeviceIDs []int           `json:"device_ids"`
	Event     DeviceDiffEvent `json:"event"`
}
type DeviceWithAreaName struct {
	Device
	AreaName string `json:"area_name"`
}

type DeviceWithArea struct {
	DeviceWithConfig
	Area Area `json:"area" gorm:"foreignKey:AreaID;references:ID"`
}

func GetConfigPath(t DeviceType) (string, error) {
	var (
		path string
		err  error
	)
	switch t {
	case DeviceTypePceM:
		path = "common_device"
	case DeviceTypeNetwork:
		path = "common_device"
	case DeviceTypeCamera:
		path = "video_device"
	case DeviceTypeNvr:
		path = "video_device"
	case DeviceTypeDoor:
		path = "door_device"
	case DeviceTypeOpc:
		path = "opc_device"
	case DeviceTypeCustom:
		path = "custom_device"
	default:
		err = errors.New("设备类型错误")
	}

	return path, err
}

type AssetDevice struct {
	IP   string `json:"ip"`
	Port string `json:"port"`
}

type ReportDeviceUnit struct {
	ID          int          `json:"id"`
	Name        string       `json:"name"`
	Flag        string       `json:"flag"`
	AlarmStatus DeviceStatus `json:"alarmstatus"`
	Level       string       `json:"level"`
}

type ReportDevice struct {
	Unit []ReportDeviceUnit `json:"unit"`
}

type DeviceWithGatewayInfo struct {
	Device
	GatewayInfo Gateway `json:"gateway_info" gorm:"foreignKey:GatewayID;references:ID"`
}
