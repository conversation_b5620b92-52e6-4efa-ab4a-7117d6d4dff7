package model

import (
	"time"

	"gorm.io/datatypes"
)

type LogTableType uint8

const (
	LogTableTypeOperation LogTableType = iota + 1
	LogTableTypeAlarm
)

type OperationLog struct {
	ID           int            `json:"id" gorm:"column:id;primaryKey"`
	UserID       int            `json:"-" gorm:"column:user_id;not null;type:int;index"`
	Param        datatypes.JSON `json:"param" gorm:"column:param;type:jsonb"`
	PermissionID int            `json:"-" gorm:"column:permission_id;not null;type:int;index"`
	CreatedAt    *time.Time     `json:"created_at" gorm:"column:created_at;autoCreatetime;not null;type:timestamptz"`
}

func (OperationLog) TableName() string {
	return "operation_logs"
}

type AlarmLog struct {
	ID int `json:"id" gorm:"column:id;primaryKey"`

	DeviceID  *int `json:"device_id,omitempty" gorm:"column:device_id;type:int"`
	UnitID    *int `json:"unit_id,omitempty" gorm:"column:unit_id;type:int"`
	GatewayID *int `json:"gateway_id,omitempty" gorm:"column:gateway_id;type:int"`

	Event   string `json:"event" gorm:"column:event;type:varchar(64)"`
	Message string `json:"message" gorm:"column:message;type:varchar(255)"`

	TX        string    `json:"tx" gorm:"column:tx;type:varchar(255)"`
	RX        string    `json:"rx" gorm:"column:rx;type:varchar(255)"`
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at;autoCreateTime;not null;type:timestamptz"`
}

func (AlarmLog) TableName() string {
	return "alarm_logs"
}

type TableUseage struct {
	Usage int64 `json:"usage" gorm:"column:usage"`
}
