package model

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

type InspectCycle uint8

const (
	InspectCycleDaily InspectCycle = iota + 1
	InspectCycleWeekly
	InspectCycleMonthly
)

type InspectPlanStatus uint8

const (
	InspectPlanStatusUnfinished InspectPlanStatus = iota + 1
	InspectPlanStatusFinished
)

type Inspect struct {
	ID            int          `json:"id" gorm:"column:id;primary_key"`
	AreaID        int          `json:"-" gorm:"column:area_id;not null;type:int;index"`
	Title         string       `json:"title,omitempty" gorm:"column:title;not null;type:varchar(128)"`
	Content       string       `json:"content,omitempty" gorm:"column:content;not null;type:varchar(512)"`
	Detail        string       `json:"detail,omitempty" gorm:"column:detail;not null;type:varchar(512)"`
	Comment       string       `json:"comment,omitempty" gorm:"column:comment;not null;type:varchar(512)"`
	Cycle         InspectCycle `json:"cycle,omitempty" gorm:"column:cycle;not null"`
	CreatedUserID int          `json:"-" gorm:"column:created_user_id;not null;type:int"`
	CheckUserID   int          `json:"-" gorm:"column:check_user_id;not null;type:int"`
	CreatedAt     *time.Time   `json:"created_at,omitempty" gorm:"column:created_at;autoCreateTime;not null;type:timestamptz"`
	UpdatedAt     *time.Time   `json:"updated_at,omitempty" gorm:"column:updated_at;autoUpdateTime;not null;type:timestamptz"`
}

func (Inspect) TableName() string {
	return "inspects"
}

type InspectPlan struct {
	ID             int               `json:"id" gorm:"column:id;primary_key"`
	InspectID      int               `json:"-" gorm:"column:inspect_id;not null;type:int;index"`
	ExecutedUserID *int              `json:"executed_user_id,omitempty" gorm:"column:executed_user_id;type:int"`
	Date           string            `json:"date" gorm:"column:date;not null;type:date;index"`
	Status         InspectPlanStatus `json:"status" gorm:"column:status;not null;type:int"`
	StartAt        string            `json:"start_at" gorm:"column:start_at;not null;type:time"`
	EndAt          string            `json:"end_at" gorm:"column:end_at;not null;type:time"`
	Comment        string            `json:"comment" gorm:"column:comment;not null;type:varchar(512)"`
	Suggestion     string            `json:"suggestion,omitempty" gorm:"column:suggestion;not null;type:varchar(512)"`
	ExecutedAt     *time.Time        `json:"executed_at,omitempty" gorm:"column:executed_at;type:timestamptz"`
}

func (InspectPlan) TableName() string {
	return "inspect_plans"
}

type InspectExecuteUser struct {
	InspectID int `json:"inspect_id" gorm:"column:inspect_id;not null;type:int;primary_key"`
	UserID    int `json:"user_id" gorm:"column:user_id;not null;type:int;primary_key"`
}

func (InspectExecuteUser) TableName() string {
	return "inspect_execute_users"
}

type InspectLeadingUser struct {
	InspectID int `json:"inspect_id" gorm:"column:inspect_id;not null;type:int;primary_key"`
	UserID    int `json:"user_id" gorm:"column:user_id;not null;type:int;primary_key"`
}

func (InspectLeadingUser) TableName() string {
	return "inspect_leading_users"
}

type InspectPlanImage struct {
	PlanID int    `json:"plan_id" gorm:"column:plan_id;not null;type:int;index"`
	Path   string `json:"path" gorm:"column:path;not null;type:varchar(255)"`
}

func (InspectPlanImage) TableName() string {
	return "inspect_plan_images"
}

func (ii *InspectPlanImage) AfterFind(tx *gorm.DB) error {
	ii.Path = fmt.Sprintf("%s%s", StaticInspection, ii.Path)
	return nil
}
