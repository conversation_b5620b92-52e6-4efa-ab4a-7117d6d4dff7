package model

// 静态资源路径，同步添加到StaticPaths
const (
	Static             = "static/"
	StaticScene        = Static + "scenes/"
	StaticSystem       = Static + "system/"
	StaticLinkage      = Static + "linkage_capture/"
	StaticVideoCapture = Static + "video_capture/"
	StaticContract     = Static + "contract/"
	StaticExportAlarm  = Static + "export_alarms/"
	StaticStandby      = Static + "standby/"
	StaticInspection   = Static + "inspection/"
)

type StaticPath []string

var Paths = StaticPath{
	Static,
	StaticScene,
	StaticSystem,
	StaticLinkage,
	StaticVideoCapture,
	StaticContract,
	StaticExportAlarm,
	StaticStandby,
	StaticInspection,
}

type SystemInfo struct {
	ID        int    `json:"id" gorm:"column:id;primaryKey"`
	Name      string `json:"name" gorm:"column:name;type:varchar(64)"`
	Copyright string `json:"copyright" gorm:"column:copyright;type:varchar(64)"`
}

func (SystemInfo) TableName() string {
	return "system_info"
}

type StandbyType = uint8

const (
	StandbyTypeMaster StandbyType = iota + 1
	StandbyTypeSlave
)

type StandbyStatus = uint8

const (
	StandbyStatusEnabled StandbyStatus = iota + 1
	StandbyStatusDisabled
)

type Standby struct {
	ID     int           `json:"id" gorm:"column:id;primaryKey"`
	Status StandbyStatus `json:"status" gorm:"column:status;type:int"`
	Type   StandbyType   `json:"type" gorm:"column:type;type:int"`
	IP     string        `json:"ip" gorm:"column:ip;type:varchar(64)"`
	Port   string        `json:"port" gorm:"column:port;type:varchar(64)"`
}

func (Standby) TableName() string {
	return "standbys"
}
