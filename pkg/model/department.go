package model

import "time"

type DepartmentStatus uint8

const (
	DepartmentStatusEnable DepartmentStatus = iota + 1
	DepartmentStatusDisable
)

type Department struct {
	ID        int              `json:"id,omitempty" gorm:"column:id;primaryKey"`
	Name      string           `json:"name,omitempty" gorm:"column:name;type:varchar(64);not null"`
	Status    DepartmentStatus `json:"status,omitempty" gorm:"column:status;type:int2;not null"`
	ParentID  int              `json:"parent_id,omitempty" gorm:"column:parent_id;type:int;not null"`
	CreatedAt *time.Time       `json:"created_at,omitempty" gorm:"column:created_at;autoCreatetime;not null;type:timestamptz"`
	UpdatedAt *time.Time       `json:"updated_at,omitempty" gorm:"column:updated_at;autoUpdatetime;not null;type:timestamptz"`
}

func (Department) TableName() string {
	return "departments"
}

type DepartmentDevice struct {
	DepartmentID int `json:"department_id" gorm:"column:department_id;primaryKey"`
	DeviceID     int `json:"device_id" gorm:"column:device_id;primaryKey"`
}

func (DepartmentDevice) TableName() string {
	return "department_devices"
}

type DepartmentRelation struct {
	ParentID int   `json:"parent_id" gorm:"column:parent_id;primaryKey"`
	ChildID  int   `json:"child_id" gorm:"column:child_id;primaryKey;index"`
	Depth    uint8 `json:"depth" gorm:"column:depth;type:int;not null"`
}

func (DepartmentRelation) TableName() string {
	return "department_relations"
}

type DepartmentTypeCount struct {
	DepartmentID   int `json:"-" gorm:"column:department_id;primaryKey"`
	BusinessTypeID int `json:"business_type_id" gorm:"column:business_type_id;primaryKey"`
	Count          int `json:"count" gorm:"column:count;type:int;not null"`
}

func (DepartmentTypeCount) TableName() string {
	return "department_type_counts"
}

type DepartmentTypeCountWithName struct {
	DepartmentTypeCount
	Name    string `json:"name" gorm:"column:name"`
	Alarmed bool   `json:"alarmed" gorm:"column:alarmed"`
}

type DepartmentWithTypeCount struct {
	Department
	Types []DepartmentTypeCountWithName `json:"types" gorm:"foreignKey:DepartmentID;references:ID"`
}
