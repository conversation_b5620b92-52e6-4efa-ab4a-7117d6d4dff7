package model

import "time"

type EventTask struct {
	ID          int        `gorm:"column:id;primaryKey" json:"id"`
	Name        string     `gorm:"column:name;type:varchar(64);not null" json:"name"`
	Enabled     string     `gorm:"column:enabled;not null;type:varchar(8);" json:"enabled"` // ON OFF
	Description *string    `gorm:"column:description;type:varchar(255)" json:"description"`
	CreatedAt   *time.Time `gorm:"column:created_at;autoCreateTime;not null;type:timestamptz" json:"created_at"`
	UpdatedAt   *time.Time `gorm:"column:updated_at;autoUpdateTime;not null;type:timestamptz" json:"updated_at"`
}

func (EventTask) TableName() string {
	return "event_task"
}

type LinkInput struct {
	Name         string    `json:"name" `
	Type         string    `json:"type"` //device  设备  time  时间
	TimeSlot     *TimeSlot `json:"time_slot"`
	SubSystemid  int       `json:"subsystem_id"`
	DeviceTypeid int       `json:"device_type_id"`
	Devid        int       `json:"device_id" `
	Argid        int       `json:"unit_id"`
	Option       string    `json:"option" `
	CompareValue int       `json:"compare_value"`
}
type EventTaskCondition struct {
	HasActioned  bool        `json:"has_actioned"`
	InputSignals []LinkInput `json:"input_signal"`
	Options      []string    `json:"options"`
}
type EventTaskAction struct {
	LinkId int `json:"link_id"`
}
type EventTaskConfig struct {
	ID        int                 `gorm:"column:id;primaryKey" json:"id"`
	Condition *EventTaskCondition `json:"condition,omitempty" gorm:"column:condition;serializer:json;type:jsonb"`
	Action    *EventTaskAction    `json:"action,omitempty" gorm:"column:action;serializer:json;type:jsonb"`
}

func (EventTaskConfig) TableName() string {
	return "event_task_configs"
}

type EventTaskAll struct {
	EventTask
	Condition *EventTaskCondition `json:"condition,omitempty" gorm:"column:condition;serializer:json"`
	Action    *EventTaskAction    `json:"action,omitempty" gorm:"column:action;serializer:json"`
}
