// internal/pkg/opcua/device/types.go
package device

// DeviceInfo 表示设备的基本信息
type DeviceInfo struct {
	ID          string `json:"id"`          // 设备唯一标识
	Name        string `json:"name"`        // 设备名称
	Type        string `json:"type"`        // 设备类型
	Description string `json:"description"` // 设备描述
	Status      string `json:"status"`      // 设备状态
	NodeID      string `json:"nodeId"`      // OPC UA节点ID
}

// ScanResult 表示设备扫描的结果
type ScanResult struct {
	ServerURI string       `json:"serverUri"` // 服务器URI
	Devices   []DeviceInfo `json:"devices"`   // 扫描到的设备列表
	Timestamp int64        `json:"timestamp"` // 扫描时间戳
}

// Scanner 定义设备扫描器接口
type Scanner interface {
	// ScanDevices 扫描并返回OPC UA服务器中的设备列表
	ScanDevices(serverURL, username, password string) (*ScanResult, error)
}
