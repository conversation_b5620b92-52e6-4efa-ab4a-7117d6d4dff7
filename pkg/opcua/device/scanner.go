// internal/pkg/opcua/device/scanner.go
package device

import (
	"context"
	"fmt"
	"time"

	"github.com/gopcua/opcua"
	"github.com/gopcua/opcua/id"
	"github.com/gopcua/opcua/ua"
	"github.com/sirupsen/logrus"
)

// DeviceScanner OPC UA设备扫描器
type DeviceScanner struct {
	ctx    context.Context
	cancel context.CancelFunc
	logger *logrus.Logger
}

// NewDeviceScanner 创建设备扫描器实例
func NewDeviceScanner() *DeviceScanner {
	ctx, cancel := context.WithCancel(context.Background())
	logger := logrus.StandardLogger()

	logger.Info("创建OPC UA设备扫描器实例")
	return &DeviceScanner{
		ctx:    ctx,
		cancel: cancel,
		logger: logger,
	}
}

// ScanDevices 扫描并返回OPC UA服务器中的设备列表
func (s *DeviceScanner) ScanDevices(serverURL, username, password string) (*ScanResult, error) {
	s.logger.WithFields(logrus.Fields{
		"serverURL": serverURL,
		"username":  username,
	}).Info("开始扫描OPC UA服务器设备列表")

	// 连接到服务器
	client, err := s.connect(serverURL, username, password)
	if err != nil {
		s.logger.WithError(err).Error("连接OPC UA服务器失败")
		return nil, fmt.Errorf("连接服务器失败: %v", err)
	}
	defer client.Close(context.Background())

	s.logger.Info("成功连接到OPC UA服务器")

	// 从Objects文件夹开始扫描
	objectsFolder := ua.NewNumericNodeID(0, 85) // Objects文件夹的标准NodeID
	s.logger.WithField("rootNode", objectsFolder.String()).Debug("从Objects文件夹开始扫描")

	devices, err := s.browseDevices(client, objectsFolder)
	if err != nil {
		s.logger.WithError(err).Error("浏览设备失败")
		return nil, fmt.Errorf("浏览设备失败: %v", err)
	}

	result := &ScanResult{
		ServerURI: serverURL,
		Devices:   devices,
		Timestamp: time.Now().Unix(),
	}

	s.logger.WithField("deviceCount", len(devices)).Info("设备扫描完成")
	return result, nil
}

// browseDevices 递归浏览节点(跳过Server及其子树)
func (s *DeviceScanner) browseDevices(client *opcua.Client, nodeID *ua.NodeID) ([]DeviceInfo, error) {
	var devices []DeviceInfo
	s.logger.WithField("nodeID", nodeID.String()).Debug("浏览节点")

	// 创建浏览请求
	req := &ua.BrowseRequest{
		NodesToBrowse: []*ua.BrowseDescription{
			{
				NodeID:          nodeID,
				BrowseDirection: ua.BrowseDirectionForward,
				ReferenceTypeID: ua.NewNumericNodeID(0, id.HierarchicalReferences),
				IncludeSubtypes: true,
				NodeClassMask:   uint32(ua.NodeClassObject), // 只浏览Object类型
				ResultMask:      uint32(ua.BrowseResultMaskAll),
			},
		},
	}

	// 执行浏览请求
	resp, err := client.Browse(context.Background(), req)
	if err != nil {
		s.logger.WithError(err).Error("浏览节点失败")
		return nil, fmt.Errorf("浏览节点失败: %v", err)
	}

	if len(resp.Results) > 0 && len(resp.Results[0].References) > 0 {
		for _, ref := range resp.Results[0].References {
			// 跳过空引用
			if ref.NodeID == nil || ref.NodeID.NodeID == nil {
				continue
			}

			// 如果是Server节点,跳过整个分支
			if ref.BrowseName.Name == "Server" {
				s.logger.Debug("跳过Server节点及其子树")
				continue
			}

			// 这是个Object,加入设备列表
			device := DeviceInfo{
				ID:          ref.NodeID.String(),
				Name:        ref.DisplayName.Text,
				Type:        "Device",
				NodeID:      ref.NodeID.String(),
				Status:      "online",
				Description: s.getNodeDescription(client, ref.NodeID.NodeID),
			}

			s.logger.WithFields(logrus.Fields{
				"deviceID":   device.ID,
				"deviceName": device.Name,
			}).Info("找到设备对象")
			devices = append(devices, device)

			// 继续递归处理非Server的子节点
			childDevices, err := s.browseDevices(client, ref.NodeID.NodeID)
			if err != nil {
				s.logger.WithError(err).Warn("浏览子节点失败")
				continue
			}
			devices = append(devices, childDevices...)
		}
	}

	return devices, nil
}

// connect 建立与OPC UA服务器的连接
func (s *DeviceScanner) connect(serverURL string, username, password string) (*opcua.Client, error) {
	s.logger.WithField("serverURL", serverURL).Debug("正在连接服务器")

	opts := []opcua.Option{
		opcua.SecurityMode(ua.MessageSecurityModeNone),
	}

	if username != "" && password != "" {
		opts = append(opts, opcua.AuthUsername(username, password))
	}

	client, err := opcua.NewClient(serverURL, opts...)
	if err != nil {
		return nil, fmt.Errorf("创建客户端失败: %v", err)
	}

	if err := client.Connect(context.Background()); err != nil {
		return nil, fmt.Errorf("连接失败: %v", err)
	}

	return client, nil
}

// getNodeDescription 获取节点描述
func (s *DeviceScanner) getNodeDescription(client *opcua.Client, nodeID *ua.NodeID) string {
	req := &ua.ReadRequest{
		NodesToRead: []*ua.ReadValueID{
			{
				NodeID:      nodeID,
				AttributeID: ua.AttributeIDDescription,
			},
		},
	}

	resp, err := client.Read(context.Background(), req)
	if err != nil {
		s.logger.WithError(err).Debug("读取节点描述失败")
		return ""
	}

	if len(resp.Results) > 0 && resp.Results[0].Status == ua.StatusOK {
		if v, ok := resp.Results[0].Value.Value().(ua.LocalizedText); ok {
			return v.Text
		}
	}

	return ""
}

// Stop 停止扫描器
func (s *DeviceScanner) Stop() {
	s.logger.Info("停止设备扫描器")
	if s.cancel != nil {
		s.cancel()
	}
}
