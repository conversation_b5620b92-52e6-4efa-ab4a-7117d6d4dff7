// internal/pkg/scanner/scanner.go
package scanner

import (
	"context"
	"fmt"
	"github.com/sirupsen/logrus"
	"go.uber.org/zap"
	"sync"
	"time"

	"github.com/gopcua/opcua"
	"github.com/gopcua/opcua/id"
	"github.com/gopcua/opcua/ua"
)

// NodeScanner OPC UA节点扫描器，用于扫描和获取OPC UA服务器中的节点信息
type NodeScanner struct {
	mu       sync.Mutex    // 互斥锁，用于保护并发访问
	client   *opcua.Client // OPC UA客户端实例
	ctx      context.Context
	cancel   context.CancelFunc
	scanning bool // 标记当前是否正在执行扫描操作
	l        *zap.Logger
}

// NewNodeScanner 创建一个新的节点扫描器实例
func NewNodeScanner(l *zap.Logger) *NodeScanner {
	ctx, cancel := context.WithCancel(context.Background())
	return &NodeScanner{
		ctx:    ctx,
		cancel: cancel,
		l:      l.Named("opc_ua_scanner"),
	}
}

// Scan 执行完整的服务器节点扫描
// serverURL: OPC UA服务器地址
// username: 用户名（可选）
// password: 密码（可选）
// Scan 从 Objects 节点开始，跳过名为 "Server" 的子节点，只扫描变量类型的节点（Variable NodeClass）
func (s *NodeScanner) Scan(serverURL string, username, password string, ns int, id int, nodeClassMask uint32) (*ScanResult, error) {
	s.l.Debug("开始扫描设备变量节点...")

	s.mu.Lock()
	if s.scanning {
		s.mu.Unlock()
		return nil, fmt.Errorf("scanning already in progress")
	}
	s.scanning = true
	s.mu.Unlock()

	defer func() {
		s.mu.Lock()
		s.scanning = false
		s.mu.Unlock()
	}()

	// 连接到服务器
	client, err := s.connect(serverURL, username, password)
	if err != nil {
		return nil, fmt.Errorf("连接失败: %v", err)
	}
	defer client.Close(context.Background())

	// 从 Objects 节点开始浏览 (i=85)
	objectsNode := ua.NewNumericNodeID(uint16(ns), uint32(id))
	nodes, err := s.browseNode(client, objectsNode, nodeClassMask)
	if err != nil {
		return nil, fmt.Errorf("节点浏览失败: %v", err)
	}

	s.l.Debug("扫描完成，共发现 个变量节点", zap.Int("count", len(nodes)))

	return &ScanResult{
		ServerURI: serverURL,
		Nodes:     nodes,
		Timestamp: time.Now().Unix(),
	}, nil
}

// ScanNode 扫描指定节点及其子节点
// serverURL: OPC UA服务器地址
// nodeID: 要扫描的节点ID
// username: 用户名（可选）
// password: 密码（可选）
func (s *NodeScanner) ScanNode(serverURL string, nodeID string, username, password string, nodeClassMask uint32) (*ScanResult, error) {
	//logrus.Infof("开始扫描指定节点: %s 在服务器: %s", nodeID, serverURL)

	client, err := s.connect(serverURL, username, password)
	if err != nil {
		s.l.Error("连接服务器失败: ", zap.Error(err))
		return nil, fmt.Errorf("failed to connect: %v", err)
	}
	defer func() {
		if err := client.Close(context.Background()); err != nil {
			logrus.Errorf("关闭客户端连接失败: %v", err)
		}
	}()

	nid, err := ua.ParseNodeID(nodeID)
	if err != nil {
		s.l.Error("解析节点ID失败: ", zap.Error(err))
		return nil, fmt.Errorf("invalid node ID: %v", err)
	}

	nodes, err := s.browseNode(client, nid, nodeClassMask)
	if err != nil {
		s.l.Error("浏览节点失败: ", zap.Error(err))
		return nil, fmt.Errorf("failed to browse node: %v", err)
	}

	s.l.Debug("节点扫描完成，共发现 %d 个相关节点", zap.Int("count", len(nodes)))
	return &ScanResult{
		ServerURI: serverURL,
		Nodes:     nodes,
		Timestamp: time.Now().Unix(),
	}, nil
}

// Stop 停止当前正在进行的扫描操作
func (s *NodeScanner) Stop() error {
	s.l.Debug("正在停止扫描操作")
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.scanning {
		s.l.Debug("当前没有正在进行的扫描操作")
		return nil
	}

	s.cancel()
	if s.client != nil {
		if err := s.client.Close(context.Background()); err != nil {
			s.l.Debug("关闭客户端连接失败: ", zap.Error(err))
			return err
		}
	}

	s.l.Debug("扫描操作已停止")
	return nil
}

// connect 建立与OPC UA服务器的连接
func (s *NodeScanner) connect(serverURL string, username, password string) (*opcua.Client, error) {
	s.l.Debug("正在连接到服务器: ", zap.String("url", serverURL))

	opts := []opcua.Option{
		opcua.SecurityMode(ua.MessageSecurityModeNone),
	}

	if username != "" && password != "" {
		s.l.Debug("使用用户名密码认证")
		opts = append(opts, opcua.AuthUsername(username, password))
	}

	client, err := opcua.NewClient(serverURL, opts...)
	if err != nil {
		return nil, fmt.Errorf("failed to create client: %v", err)
	}

	if err := client.Connect(context.Background()); err != nil {
		return nil, fmt.Errorf("failed to connect: %v", err)
	}

	s.l.Debug("服务器连接成功")
	s.client = client
	return client, nil
}

// browseNode 递归浏览节点及其子节点
func (s *NodeScanner) browseNode(client *opcua.Client, nodeID *ua.NodeID, nodeClassMask uint32) ([]NodeInfo, error) {
	var nodes []NodeInfo

	// 浏览子节点
	req := &ua.BrowseRequest{
		NodesToBrowse: []*ua.BrowseDescription{
			{
				NodeID:          nodeID,
				BrowseDirection: ua.BrowseDirectionForward,
				ReferenceTypeID: ua.NewNumericNodeID(0, id.HierarchicalReferences),
				IncludeSubtypes: true,
				NodeClassMask:   nodeClassMask, // NodeClassAll 全部节点 NodeClassVariable // 只扫描变量节点
				ResultMask:      uint32(ua.BrowseResultMaskAll),
			},
		},
	}
	s.l.Debug("req: ", zap.Any("req", req))
	s.l.Debug("nodeClassMask: ", zap.Any("nodeClassMask", nodeClassMask))

	resp, err := client.Browse(context.Background(), req)
	s.l.Debug("resp: ", zap.Any("resp", resp))

	if err != nil {
		return nodes, fmt.Errorf("failed to browse: %v", err)
	}

	for _, res := range resp.Results {
		for _, ref := range res.References {
			if ref.NodeID == nil {
				continue
			}
			s.l.Debug("NodeID: ", zap.Any("result", ref.NodeID))
			// 获取节点信息
			childNodeInfo, err := s.getNodeInfo(client, ref.NodeID.NodeID)
			s.l.Debug("childNodeInfo: ", zap.Any("childNodeInfo", childNodeInfo))

			if err != nil {
				s.l.Error("获取节点信息失败: ", zap.Error(err))
				continue
			}

			// 跳过 Server 节点
			if childNodeInfo.BrowseName == "Server" {
				s.l.Error("跳过 Server 节点")
				continue
			}

			// 如果是变量节点，添加到结果中
			if childNodeInfo.NodeClass == "NodeClassVariable" {
				s.l.Debug("", zap.String("发现变量节点:", childNodeInfo.DisplayName))
				//nodes = append(nodes, childNodeInfo)
			}

			// 如果不是变量节点，递归遍历其子节点
			childNodes, err := s.browseNode(client, ref.NodeID.NodeID, nodeClassMask)
			if err != nil {
				s.l.Error("浏览子节点失败: ", zap.Error(err))
				continue
			}
			childNodeInfo.Children = childNodes
			nodes = append(nodes, childNodeInfo)
		}
	}

	return nodes, nil
}

// getNodeInfo 获取单个节点的详细信息
func (s *NodeScanner) getNodeInfo(client *opcua.Client, nodeID *ua.NodeID) (NodeInfo, error) {
	node := NodeInfo{
		NodeID: nodeID.String(),
	}

	// 读取基本属性
	attrIDs := []ua.AttributeID{
		ua.AttributeIDBrowseName,
		ua.AttributeIDDisplayName,
		ua.AttributeIDNodeClass,
		ua.AttributeIDDescription,
		ua.AttributeIDDataType,
	}

	for _, attr := range attrIDs {
		req := &ua.ReadRequest{
			NodesToRead: []*ua.ReadValueID{
				{
					NodeID:      nodeID,
					AttributeID: attr,
				},
			},
		}

		resp, err := client.Read(context.Background(), req)
		if err != nil {
			continue
		}

		if len(resp.Results) > 0 && resp.Results[0].Status == ua.StatusOK {
			s.parseNodeAttribute(&node, attr, resp.Results[0], client)
		}
	}

	// 如果是变量节点，读取其当前值
	if node.NodeClass == "NodeClassVariable" {
		req := &ua.ReadRequest{
			NodesToRead: []*ua.ReadValueID{
				{
					NodeID:      nodeID,
					AttributeID: ua.AttributeIDValue,
				},
			},
		}

		resp, err := client.Read(context.Background(), req)
		if err == nil && len(resp.Results) > 0 && resp.Results[0].Status == ua.StatusOK {
			if resp.Results[0].Value != nil {
				node.Value = fmt.Sprintf("%v", resp.Results[0].Value.Value())
			}
		}
	}

	return node, nil
}

// internal/pkg/scanner/scanner.go

// parseNodeAttribute 解析节点属性
// parseNodeAttribute 解析节点属性
func (s *NodeScanner) parseNodeAttribute(node *NodeInfo, attr ua.AttributeID, value *ua.DataValue, client *opcua.Client) {
	if value == nil || value.Value == nil {
		return
	}

	switch attr {
	case ua.AttributeIDBrowseName:
		if v, ok := value.Value.Value().(*ua.QualifiedName); ok {
			node.BrowseName = v.Name
		}

	case ua.AttributeIDDisplayName:
		if v, ok := value.Value.Value().(*ua.LocalizedText); ok {
			node.DisplayName = v.Text
		}

	case ua.AttributeIDNodeClass:
		if v, ok := value.Value.Value().(int32); ok {
			nodeClass := ua.NodeClass(v)
			node.NodeClass = nodeClass.String()
		}

	case ua.AttributeIDDescription:
		if v, ok := value.Value.Value().(*ua.LocalizedText); ok {
			node.Description = v.Text
		}

	case ua.AttributeIDDataType:
		if v, ok := value.Value.Value().(*ua.NodeID); ok {
			s.retrieveDataTypeName(node, v, client)
		}
	}
}

// retrieveDataTypeName 获取数据类型名称
func (s *NodeScanner) retrieveDataTypeName(node *NodeInfo, dataTypeID *ua.NodeID, client *opcua.Client) {
	req := &ua.ReadRequest{
		NodesToRead: []*ua.ReadValueID{
			{
				NodeID:      dataTypeID,
				AttributeID: ua.AttributeIDDisplayName,
			},
		},
	}

	resp, err := client.Read(context.Background(), req)
	if err != nil {
		s.l.Error("读取数据类型名称失败: ", zap.Error(err))
		return
	}

	if len(resp.Results) > 0 && resp.Results[0].Status == ua.StatusOK {
		if dt, ok := resp.Results[0].Value.Value().(ua.LocalizedText); ok {
			node.DataType = dt.Text
		}
	}
}
