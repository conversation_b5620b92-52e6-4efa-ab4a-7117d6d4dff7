package scanner

// NodeInfo represents the information of an OPC UA node
type NodeInfo struct {
	NodeID      string     `json:"nodeId"`
	BrowseName  string     `json:"browseName"`
	DisplayName string     `json:"displayName"`
	NodeClass   string     `json:"nodeClass"`
	DataType    string     `json:"dataType,omitempty"`
	Description string     `json:"description,omitempty"`
	IsArray     bool       `json:"isArray,omitempty"`
	Value       string     `json:"value,omitempty"` // 添加了Value字段
	Children    []NodeInfo `json:"children,omitempty"`
}

// ScanResult represents the result of a node scanning operation
type ScanResult struct {
	ServerURI string     `json:"serverUri"`
	Nodes     []NodeInfo `json:"nodes"`
	Timestamp int64      `json:"timestamp"`
}

// Scanner defines the interface for node scanning operations
type Scanner interface {
	// <PERSON>an performs a complete scan of the OPC UA server
	Scan(serverURL string, username, password string) (*ScanResult, error)

	// ScanNode scans a specific node and its children
	ScanNode(serverURL string, nodeID string, username, password string) (*ScanResult, error)

	// Stop stops any ongoing scanning operation
	Stop() error
}
