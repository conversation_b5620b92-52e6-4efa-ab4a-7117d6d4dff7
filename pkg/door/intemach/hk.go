package intemach

import (
	"bytes"
	"crypto/rand"
	b64 "encoding/base64"
	"encoding/json"
	"encoding/xml"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"mime/multipart"
	"net/http"
	"net/textproto"
	"strconv"
	"strings"
	"sync"
	"time"
	"tw_platform/pkg/door/digest"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
)

var (
	ErrGetTrans   = errors.New("Transport is nil")
	ErrHttpCom    = errors.New("http trans fail")
	ERRHttpNot200 = errors.New("http not 200")
	//ErrAlgNotImplemented = errors.New("Alg not implemented")
	ErrJson = errors.New("json is not ok")
)

const (
	jsonType               = "application/json"
	deviceInfoUrl          = "/ISAPI/System/deviceInfo"
	userInfoUrl            = "/ISAPI/AccessControl/UserInfo/Search?format=json"
	getCardUrl             = "/ISAPI/AccessControl/CardInfo/Search?format=json"
	getFingerUrl           = "/ISAPI/AccessControl/FingerPrintUpload?format=json"
	addUserUrl             = "/ISAPI/AccessControl/UserInfo/Record?format=json"
	delUserUrl             = "/ISAPI/AccessControl/UserInfo/Delete?format=json"
	changePwdUrl           = "/ISAPI/AccessControl/UserInfo/Modify?format=json"
	delFingerUrl           = "/ISAPI/AccessControl/FingerPrint/Delete?format=json"
	getDelFingerProcessUrl = "/ISAPI/AccessControl/FingerPrint/DeleteProcess?format=json"
	controlDoorUrl         = "/ISAPI/AccessControl/RemoteControl/door/1"
	getRecordUrl           = "/ISAPI/AccessControl/AcsEvent?format=json"
	getDoorStatusUrl       = "/ISAPI/AccessControl/AcsWorkStatus?format=json"
	addCardUrl             = "/ISAPI/AccessControl/CardInfo/Record?format=json"
	delCardUrl             = "/ISAPI/AccessControl/CardInfo/Delete?format=json"
	addFingerUrl           = "/ISAPI/AccessControl/FingerPrintDownload?format=json"
	addFingerProcessUrl    = "/ISAPI/AccessControl/FingerPrintProgress?format=json"
	addFaceUrl             = "/ISAPI/Intelligent/FDLib/FDSetUp?format=json"
	getFaceUrl             = "/ISAPI/Intelligent/FDLib/FDSearch?format=json"
	delFaceUrl             = "/ISAPI/Intelligent/FDLib/FDSetUp?format=json"
	getUserCntUrl          = "/ISAPI/AccessControl/UserInfo/Count?format=json"
)

var major = map[int]string{
	1: "报警类型",
	2: "异常类型",
	3: "操作类型",
	5: "事件",
}
var minorString = map[int]map[int]string{
	1: {
		0x400: "防区短路报警",
		0x401: "防区断路报警",
		0x042: "防区异常报警",
		0x403: "防区报警恢复",
		0x404: "防区防拆报警",
		0x405: "防区防拆恢复",
		0x406: "读卡器防拆报警",
		0x407: "读卡器防拆恢复",
		0x408: "事件输入报警",
		0x409: "事件输入恢复",
		0x40a: "胁迫报警",
		0x40b: "离线事件满90%报警",
		0x40c: "卡号认证失败超次报警",
		0x40d: "SD卡存储满报警",
		0x40e: "联动抓拍事件报警",
		0x40f: "门控安全模块防拆报警",
		0x410: "门控安全模块防拆恢复",
		0x411: "POS开启",
		0x412: "POS结束",
		0x413: "人脸图像画质低",
		0x414: "指纹图像画质低",
		0x415: "消防输入短路报警",
		0x416: "消防输入断路报警",
		0x417: "消防输入恢复",
		0x418: "消防按钮触发",
		0x419: "消防按钮恢复",
		0x41a: "维护按钮触发",
		0x41b: "维护按钮恢复",
		0x41c: "维护按钮恢复",
		0x41d: "紧急按钮恢复",
		0x41e: "分控制器防拆报警",
		0x41f: "分控制器防拆报警恢复",
		0x422: "通道控制器防拆报警",
		0x423: "通道控制器防拆报警恢复",
		0x424: "通道控制器消防输入报警",
		0x425: "通道控制器消防输入报警恢复",
		0x442: "合法事件满90%报警",
	},
	2: {
		0x27:  "网络断开",
		0x3a:  "RS485连接状态异常",
		0x3b:  "RS485链接状态异常恢复",
		0x400: "设备上电启动",
		0x401: "设备掉电关闭",
		0x402: "看门够复位",
		0x403: "蓄电池电压低",
		0x404: "蓄电池电压恢复正常",
		0x405: "交流电断电",
		0x406: "交流电恢复",
		0x407: "网络恢复",
		0x408: "FLASH读写异常",
		0x409: "读卡器掉线",
		0x40a: "读卡器掉线恢复",
		0x40b: "指示灯关闭",
		0x40c: "指示灯恢复",
		0x40d: "通道控制器掉线",
		0x40e: "通道控制器恢复",
		0x40f: "门控安全模块掉线",
		0x410: "门控安全模块掉线恢复",
		0x413: "就地控制器网络断开",
		0x414: "就地控制器网络恢复",
		0x415: "主控RS485环路节点断开",
		0x416: "主控RS485环路节点恢复",
		0x417: "就地控制器掉线",
		0x418: "就地控制器掉线恢复",
		0x419: "就地下行RS485环路恢复",
		0x41a: "就地下行RS485环路恢复",
		0x41b: "分控制器在线",
		0x41c: "分控制器离线",
		0x41d: "身份证阅读器未连接",
		0x41e: "身份控制器连接恢复",
		0x41f: "指纹模组未连接",
		0x420: "指纹模组连接恢复",
		0x421: "摄像头未连接",
		0x422: "摄像头连接恢复",
		0x423: "COM口未连接",
		0x424: "COM口连接恢复",
		0x425: "设备未授权",
		0x426: "人证设备在线",
		0x427: "人证设备离线",
		0x411: "电池电压低",
		0x412: "电池电压恢复正常",
		0x428: "本地登录锁定",
		0x429: "本地登录解锁",
		0x42a: "与反潜回服务器通信断开",
		0x42b: "与反潜回服务器通信恢复",
		0x42c: "电机或传感器异常",
		0x42d: "CAN 总线异常",
		0x42e: "CAN 总线恢复",
		0x42f: "闸机腔体温度超限",
		0x430: "红外对射异常",
		0x431: "红外对射恢复",
		0x432: "灯板通信异常",
		0x433: "灯板通信恢复",
		0x434: "红外转接板通信异常",
		0x435: "红外转接板通信恢复",
	},
	3: {
		0x50:  "本地登陆",
		0x51:  "本地注销登陆",
		0x5a:  "本地升级",
		0x70:  "远程登录",
		0x71:  "远程注销登录",
		0x79:  "远程布防",
		0x7a:  "远程撤防",
		0x7b:  "远程重启",
		0x7e:  "远程升级",
		0x86:  "远程导出配置文件",
		0x87:  "远程导入配置文件",
		0xd6:  "远程手动开启报警输出",
		0xd7:  "远程手动关闭报警输出",
		0x400: "远程开门",
		0x401: "远程关门",
		0x402: "远程常开",
		0x403: "远程常关",
		0x404: "远程手动校时",
		0x405: "NTP自动校时",
		0x406: "远程清空卡号",
		0x407: "远程恢复默认参数",
		0x408: "防区布防",
		0x409: "防区撤防",
		0x40a: "本地恢复默认参数",
		0x40b: "远程抓拍",
		0x40c: "修改网络中心参数配置",
		0x40d: "修改 GPRS 中心参数配置",
		0x40e: "修改中心组参数配置",
		0x40f: "解除码输入",
		0x410: "自动重新编号",
		0x411: "自动补充编号",
		0x412: "导入普通配置文件",
		0x413: "导出普通配置文件",
		0x414: "导入卡权限参数",
		0x415: "导出卡权限参数",
		0x416: "本地 U 盘升级",
		0x417: "访客呼梯",
		0x418: "住户呼梯",
		0x419: "远程实时布防",
		0x41a: "远程实时撤防",
		0x41b: "遥控器未对码操作失败",
		0x41c: "遥控器关门",
		0x41d: "遥控器开门",
		0x41e: "遥控器常开门",
	},
	5: {
		0x01: "合法卡认证通过",
		0x02: "刷卡加密码认证通过",
		0x03: "刷卡加密码认证失败",
		0x04: "数卡加密码认证超时",
		0x05: "刷卡加密码超次",
		0x06: "未分配权限",
		0x07: "无效时段",
		0x08: "卡号过期",
		0x09: "无此卡号",
		0x0a: "反潜回认证失败",
		0x0b: "互锁门未关闭",
		0x0c: "卡不属于多重认证群组",
		0x0d: "卡不在多重认证时间段内",
		0x0e: "多重认证模式超级权限认证失败",
		0x0f: "多重认证模式远程认证失败",
		0x10: "多重认证成功",
		0x11: "首卡开门开始",
		0x12: "首卡开门结束",
		0x13: "常开状态开始",
		0x14: "常开状态结束",
		0x15: "门锁打开",
		0x16: "门锁关闭",
		0x17: "开门按钮打开",
		0x18: "开门按钮放开",
		0x19: "正常开门(门磁)",
		0x1a: "正常关门(门磁)",
		0x1b: "门异常打开(门磁)",
		0x1d: "报警输出打开",
		0x1e: "报警输出关闭",
		0x1f: "常关状态开始",
		0x20: "常关状态结束",
		0x21: "多重多重认证需要远程开门",
		0x22: "多重认证超级密码认证成功事件",
		0x23: "多重认证重复认证事件",
		0x24: "多重认证超时",
		0x25: "门铃响",
		0x26: "指纹比对通过",
		0x27: "指纹比对失败",
		0x28: "刷卡加指纹认证通过",
		0x29: "刷卡加指纹认证失败",
		0x2a: "刷卡加指纹认证超时",
		0x2b: "刷卡加指纹加密码认证通过",
		0x2c: "刷卡加指纹加密码认证失败",
		0x2d: "刷卡加指纹加密码认证超时",
		0x2e: "指纹加密码认证通过",
		0x2f: "指纹加密码认证失败",
		0x30: "指纹加密码认证超时",
		0x31: "指纹不存在",
		0x32: "刷卡平台认证",
		0x33: "呼叫中心事件",
		0x34: "消防继电器导通触发门常开",
		0x35: "消防继电器恢复门恢复正常",
		0x36: "人脸加指纹认证通过",
		0x37: "人脸加指纹认证失败",
		0x38: "人脸加指纹认证超时",
		0x39: "人脸加密码认证通过",
		0x3a: "人脸加密码认证失败",
		0x3b: "人脸加密码认证超时",
		0x3c: "人脸加刷卡认证通过",
		0x3d: "人脸加刷卡认证失败",
		0x3e: "人脸加刷卡认证超时",
		0x3f: "人脸加密码加指纹认证通过",
		0x40: "人脸加密码加指纹认证失败",
		0x41: "人脸加密码加指纹认证超时",
		0x42: "人脸加刷卡加指纹认证通过",
		0x43: "人脸加刷卡加指纹认证失败",
		0x44: "人脸加刷卡加指纹认证超时",
		0x45: "工号加指纹认证通过",
		0x46: "工号加指纹认证失败",
		0x47: "工号加指纹认证超时",
		0x48: "工号加指纹加密码认证通过",
		0x49: "工号加指纹加密码认证失败",
		0x4a: "工号加指纹加密码认证超时",
		0x4b: "人脸认证通过",
		0x4c: "人脸认证失败",
		0x4d: "工号加人脸认证通过",
		0x4e: "工号加人脸认证失败",
		0x4f: "工号加人脸认证超时",
		0x50: "人脸识别失败",
		0x51: "首卡授权开始",
		0x52: "首卡授权结束",
		0x53: "门锁输入短路报警",
		0x54: "门锁输入断路报警",
		0x55: "门锁输入异常报警",
		0x56: "门磁输入短路报警",
		0x57: "门磁输入断路报警",
		0x58: "门磁输入异常报警",
		0x59: "开门按钮输入短路报警",
		0x5a: "开门按钮输入断路报警",
		0x5b: "开门按钮输入异常报警",
		0x5c: "门锁异常打开",
		0x5d: "门锁打开超时",
		0x5e: "首卡未授权开门失败",
		0x5f: "呼梯继电器断开",
		0x60: "呼梯继电器闭合",
		0x61: "自动按键继电器断开",
		0x62: "自动按键继电器闭合",
		0x63: "按键梯控继电器断开",
		0x64: "按键梯控继电器闭合",
		0x65: "工号加密码认证通过",
		0x66: "工号加密码认证失败",
		0x67: "工号加密码认证超时",
		0x68: "真人检测失败",
		0x69: "人证比对通过",
		0x70: "人证比对失败",
		0x71: "黑名单事件",
		0x72: "合法短信",
		0x73: "非法短信",
		0x74: "MAC 侦测",
		0x75: "门状态常闭或休眠状态认证失败",
		0x76: "认证计划休眠模式认证失败",
		0x77: "卡加密校验失败",
		0x78: "反潜回服务器应答失败",
		0x85: "尾随通行",
		0x86: "反向闯入",
		0x87: "外力冲撞",
		0x88: "翻越",
		0x89: "通行超时",
		0x8a: "误闯报警",
		0x8b: "闸机自由通行时未认证通过",
		0x8c: "摆臂被阻挡",
		0x8d: "摆臂阻挡消除",
		0x8e: "设备升级本地人脸建模失败",
		0x8f: "逗留事件",
		0x97: "密码不匹配",
		0x98: "工号不存在",
		0x99: "组合认证通过",
		0x9a: "组合认证超时",
		0x9b: "认证方式不匹配",
		0xb5: "密码认证通过",
	},
}

type webAddUserCmd struct {
	Doorid         int    `json:"doorId"`
	EmployeeNo     string `json:"employeeNo"`
	Name           string `json:"name"`
	Password       string `json:"password"`
	LocalUIRight   bool   `json:"localUIRight"`
	UserVerifyMode string `json:"userVerifyMode"`
}
type _userNo struct {
	EmployeeNo string `json:"employeeNo"`
}
type _cardSearchCmd struct {
	SearchID             string    `json:"searchID"`
	SearchResultPosition int       `json:"searchResultPosition"`
	MaxResults           int       `json:"maxResults"`
	EmployeeNoList       []_userNo `json:"EmployeeNoList"`
}
type cardSearchCmd struct {
	_cardSearchCmd `json:"CardInfoSearchCond"`
}

type _cardSearchRet struct {
	SearchID           string         `json:"searchID"`
	ResponseStatusStrg string         `json:"responseStatusStrg"`
	NumOfMatches       int            `json:"numOfMatches"`
	TotalMatches       int            `json:"totalMatches"`
	CardInfo           []_getCardInfo `json:"CardInfo"`
}
type cardSearchRet struct {
	_cardSearchRet `json:"CardInfoSearch"`
}

type addFaceParam struct {
	FaceLibType string `json:"faceLibType"`
	FDID        string `json:"FDID"`
	FPID        string `json:"FPID"`
}

type delFace struct {
	FaceLibType string `json:"faceLibType"`
	FDID        string
	FPID        string
	DeleteFP    bool `json:"deleteFP"`
}

type _faceInfo struct {
	FPID    string `json:"FPID"`
	FaceUrl string `json:"faceURL"`
}
type faceInfo struct {
	StatusCode         int         `json:"statusCode"`
	ResponseStatusStrg string      `json:"responseStatusStrg"`
	NumOfMatches       int         `json:"numOfMatches"`
	TotalMatches       int         `json:"totalMatches"`
	MatchList          []_faceInfo `json:"MatchList"`
}
type getFaceParam struct {
	SearchResultPosition int    `json:"searchResultPosition"`
	MaxResults           int    `json:"maxResults"`
	FaceLibType          string `json:"faceLibType"`
	FDID                 string `json:"FDID"`
	FPID                 string `json:"FPID"`
}
type _delFingerProcess struct {
	Status string `json:"status"`
}
type delFingerProcess struct {
	_delFingerProcess `json:"FingerPrintDeleteProcess"`
}
type _addFingerProcess struct {
	TotalStatus int `json:"totalStatus"`
}
type addFingerProcess struct {
	_addFingerProcess `json:"FingerPrintStatus"`
}

// 添加指纹
type _addfingerErrResp struct {
	RequestURL    string `json:"requestURL"`
	StatusCode    int    `json:"statusCode"`
	StatusString  string `json:"statusString"`
	SubStatusCode string `json:"subStatusCode"`
	ErrorCode     int    `json:"errorCode"`
	ErrorMsg      string `json:"errorMsg"`
}
type _getFingerCmd struct {
	SearchID   string `json:"searchID"`
	EmployeeNo string `json:"employeeNo"`
}
type getFingerCmd struct {
	_getFingerCmd `json:"FingerPrintCond"`
}
type _employeeNoDetail struct {
	EmployeeNo       string `json:"employeeNo"`
	EnableCardReader []int  `json:"enableCardReader"`
	FingerPrintID    []int  `json:"fingerPrintID"`
}
type _fingerPrintDelete struct {
	Mode              string `json:"mode"`
	_employeeNoDetail `json:"EmployeeNoDetail"`
}
type fingerPrintDelete struct {
	_fingerPrintDelete `json:"FingerPrintDelete"`
}
type _getFingerInfo struct {
	SearchID        string        `json:"searchID"`
	Status          string        `json:"status"`
	FingerPrintList []_fingerList `json:"FingerPrintList"`
}
type getFingerInfo struct {
	_getFingerInfo `json:"FingerPrintInfo"`
}

// 获取门磁状态
type _getDoorStatus struct {
	MagneticStatus []int `json:"magneticStatus"`
}
type DoorStatus struct {
	_getDoorStatus `json:"AcsWorkStatus"`
}
type RemoteControlDoor struct {
	Version string `xml:"version,attr"`
	Xmlns   string `xml:"xmlns,attr"`
	Cmd     string `xml:"cmd"`
}
type UserInfoSearchCond struct {
	SearchID             string `json:"searchID"`
	SearchResultPosition int    `json:"searchResultPosition"`
	MaxResults           int    `json:"maxResults"`
}

type UserInfoCmd struct {
	UserInfoSearchCond `json:"UserInfoSearchCond"`
}
type Hk struct {
	AdminName string
	AdminPwd  string
	Host      string
	Ability   string
}
type UserInfoSearch struct {
	SearchID           string     `json:"searchID"`
	ResponseStatusStrg string     `json:"responseStatusStrg"`
	NumOfMatches       int        `json:"numOfMatches"`
	TotalMatches       int        `json:"totalMatches"`
	UserInfo           []UserInfo `json:"UserInfo"`
}
type UserInfoValid struct {
	Enable    bool   `json:"enable"`
	TimeType  string `json:"timeType"`
	BeginTime string `json:"beginTime"`
	EndTime   string `json:"endTime"`
}

type UserInfo struct {
	EmployeeNo     string `json:"employeeNo"`
	Name           string `json:"name"`
	Password       string `json:"password"`
	LocalUIRight   bool   `json:"localUIRight"`
	UserVerifyMode string `json:"userVerifyMode"`
	UserInfoValid  `json:"Valid"`
}

type userSearchRet struct {
	UserInfoSearch `json:"UserInfoSearch"`
}

type _addUser struct {
	EmployeeNo     string `json:"employeeNo"`
	Name           string `json:"name"`
	UserType       string `json:"userType"`
	UserInfoValid  `json:"Valid"`
	Password       string       `json:"password"`
	DoorRight      string       `json:"doorRight"`
	RightPlan      []_rightPlan `json:"RightPlan"`
	UserVerifyMode string       `json:"userVerifyMode"`
	LocalUIRight   bool         `json:"localUIRight"`
}
type _rightPlan struct {
	DoorNo         int    `json:"doorNo"`
	PlanTemplateNo string `json:"planTemplateNo"`
}

type addUser struct {
	_addUser `json:"UserInfo"`
}
type _delemInfo struct {
	EmployeeNo string `json:"employeeNo"`
}
type _employeeNoList struct {
	EmployeeNoList []_delemInfo `json:"EmployeeNoList"`
}
type userInfoDelCond struct {
	_employeeNoList `json:"UserInfoDelCond"`
}
type _changePwd struct {
	EmployeeNo     string `json:"employeeNo"`
	Password       string `json:"password"`
	Name           string `json:"name"`
	UserVerifyMode string `json:"userVerifyMode"`
	LocalUIRight   bool   `json:"localUIRight"`
}
type changePwd struct {
	_changePwd `json:"UserInfo"`
}

type _addFinger struct {
	EmployeeNo       string `json:"employeeNo"`
	EnableCardReader []int  `json:"enableCardReader"`
	FingerPrintId    int    `json:"fingerPrintID"`
	FingerType       string `json:"fingerType"`
	FingerData       string `json:"fingerData"`
}
type addFinger struct {
	_addFinger `json:"FingerPrintCfg"`
}

var HTTP_FAIL = errors.New("HTTP FAIL")

var eventCache sync.Map

type eventRecordParam struct {
	lastSearchId string
	revcnt       int
}

func httpPut(url string, data []byte, adminName string, adminPwd string) error {
	t := digest.NewTransport(adminName, adminPwd)
	c, err := t.Client()
	if err != nil {
		return err
	}
	req, err := http.NewRequest("PUT", url, bytes.NewReader(data))
	if err != nil {
		fmt.Println("1111")
		return err
	}
	req.Header.Add("Content-Type", jsonType)

	resp, err := c.Do(req)
	if err != nil {
		fmt.Println("22222")
		return err
	}
	defer resp.Body.Close()
	//s, _ := ioutil.ReadAll(resp.Body)
	//fmt.Println(string(s))
	if resp.StatusCode != http.StatusOK {
		fmt.Println("code:", resp.StatusCode)
		return HTTP_FAIL
	}

	return nil
}
func (h *Hk) GetDoorStatus() (int, error) {
	gs := DoorStatus{}
	t := digest.NewTransport(h.AdminName, h.AdminPwd)
	c, err := t.Client()
	if err != nil {
		return -1, err
	}

	resp, err := c.Get(h.Host + getDoorStatusUrl)
	if err != nil {
		return -1, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return -1, errors.New("HTTP FAIL")
	}
	s, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return -1, err
	}
	err = json.Unmarshal(s, &gs)
	if err != nil {
		return -1, err
	}
	return gs.MagneticStatus[0], nil
}

// 控制门
func (h *Hk) CtlDoorStatus(way string) error {
	rc := RemoteControlDoor{}
	rc.Version = "2.0"
	rc.Xmlns = "http://www.isapi.org/ver20/XMLSchema"
	rc.Cmd = way
	jc, err := xml.Marshal(rc)
	if err != nil {
		return err
	}
	return httpPut(h.Host+controlDoorUrl, jc, h.AdminName, h.AdminPwd)
}
func getUniId() string {
	b := make([]byte, 10)
	io.ReadFull(rand.Reader, b)
	rstr := fmt.Sprintf("%x", b)[:20]
	return rstr
}

// 获取人员列表
func (h *Hk) GetUserList() ([]ToWebUserInfo, error) {
	toWebUser := []ToWebUserInfo{}
	startPos := 0
	uid := getUniId()
	for {
		t := digest.NewTransport(h.AdminName, h.AdminPwd)
		c, err := t.Client()
		if err != nil {
			return toWebUser, err
		}
		cmd := UserInfoCmd{UserInfoSearchCond{SearchID: uid, SearchResultPosition: startPos, MaxResults: 30}}
		jc, err := json.Marshal(cmd)
		if err != nil {
			fmt.Println("json format err")
			return toWebUser, err
		}

		resp, err := c.Post(h.Host+userInfoUrl, jsonType, bytes.NewReader(jc))
		if err != nil {
			return toWebUser, err
		}
		defer resp.Body.Close()
		if resp.StatusCode == http.StatusNotFound {
			return []ToWebUserInfo{}, nil
		}
		if resp.StatusCode != http.StatusOK {
			return []ToWebUserInfo{}, HTTP_FAIL
		}
		s, _ := ioutil.ReadAll(resp.Body)
		//fmt.Println("user info: ", string(s))
		var uInfo userSearchRet
		json.Unmarshal(s, &uInfo)
		if uInfo.ResponseStatusStrg == "NO MATCH" {
			return toWebUser, nil
		}
		if len(uInfo.UserInfo) > 0 {
			for _, v := range uInfo.UserInfo {
				toWebUser = append(toWebUser, ToWebUserInfo{
					EmployeeNo:     v.EmployeeNo,
					Name:           v.Name,
					Password:       v.Password,
					LocalUIRight:   v.LocalUIRight,
					UserVerifyMode: v.UserVerifyMode,
				})
			}
		}
		if uInfo.ResponseStatusStrg == "OK" {
			return toWebUser, nil
		}
		if uInfo.ResponseStatusStrg == "MORE" {
			startPos += uInfo.NumOfMatches
		}
	}
	return toWebUser, nil
}

// 添加人员
func (h *Hk) AddOneUser(emStr, name, pwd, UserVerifyMode string, localUi bool) error {
	auinfo := addUser{}
	auinfo.EmployeeNo = emStr
	auinfo.Name = name
	auinfo.UserType = "normal"
	auinfo.Enable = true
	auinfo.BeginTime = "2000-01-01T00:00:00"
	auinfo.EndTime = "2037-12-31T23:59:59"
	auinfo.TimeType = "local"
	auinfo.DoorRight = "1"
	auinfo.Password = pwd
	auinfo.LocalUIRight = localUi
	auinfo.UserVerifyMode = UserVerifyMode
	auinfo.RightPlan = append(auinfo.RightPlan, _rightPlan{DoorNo: 1, PlanTemplateNo: "1"})

	t := digest.NewTransport(h.AdminName, h.AdminPwd)
	c, err := t.Client()
	if err != nil {
		return err
	}
	jc, err := json.Marshal(auinfo)
	if err != nil {
		return err
	}
	resp, err := c.Post(h.Host+addUserUrl, jsonType, bytes.NewReader(jc))
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	rets, _ := ioutil.ReadAll(resp.Body)
	println(string(rets))
	if resp.StatusCode != http.StatusOK {
		return HTTP_FAIL
	}
	return nil
}

// 删除人员
func (h *Hk) DelOneUser(emStr []string) error {
	var err error
	for {
		//删除卡片
		if strings.Contains(h.Ability, "C") {
			if err = h.DelUserCardByEmNo(emStr); err != nil {
				return err
			}
		}
		//删除指纹
		if strings.Contains(h.Ability, "P") {
			ret := h.delFingerEmAll(emStr)
			if !ret {
				fmt.Println("zzz")
				//tc.Reply = 203
				return errors.New("删除错误")
			}
		}
		//删除人脸
		if strings.Contains(h.Ability, "F") {
			for _, emNo := range emStr {
				if errf := h.DelUserFace(emNo); errf != nil {
					fmt.Println("err delete user face")
					return errors.New("删除错误")
				}
			}
		}
		//删除人员信息
		delUInfo := userInfoDelCond{}
		for _, emNo := range emStr {
			delUInfo.EmployeeNoList = append(delUInfo.EmployeeNoList, _delemInfo{
				EmployeeNo: emNo})
		}
		jc, err := json.Marshal(delUInfo)
		if err != nil {
			return err
		}
		return httpPut(h.Host+delUserUrl, jc, h.AdminName, h.AdminPwd)
	}
	return nil
}

// 修改人员信息
func (h *Hk) ChangeOneUser(emStr, name, pwd, UserVerifyMode string, localUi bool) error {
	chPwd := changePwd{}
	chPwd.EmployeeNo = emStr
	chPwd.Password = pwd
	chPwd.Name = name
	chPwd.UserVerifyMode = UserVerifyMode
	chPwd.LocalUIRight = localUi

	jc, err := json.Marshal(chPwd)
	if err != nil {
		return err
	}
	return httpPut(h.Host+changePwdUrl, jc, h.AdminName, h.AdminPwd)
}

// 获取指纹
func (h *Hk) GetUserFp(emStr string) ([]_fingerList, error) {
	unid := getUniId()
	fCmd := getFingerCmd{_getFingerCmd{SearchID: unid, EmployeeNo: emStr}}
	rFinList := []_fingerList{}
	for {
		jc, err := json.Marshal(fCmd)
		if err != nil {
			return []_fingerList{}, err
		}
		t := digest.NewTransport(h.AdminName, h.AdminPwd)
		c, err := t.Client()
		if err != nil {
			return []_fingerList{}, err
		}
		resp, err := c.Post(h.Host+getFingerUrl, jsonType, bytes.NewReader(jc))
		if err != nil {
			return []_fingerList{}, err
		}
		defer resp.Body.Close()
		if resp.StatusCode == http.StatusNotFound {
			return []_fingerList{}, nil
		}
		if resp.StatusCode != http.StatusOK {
			return []_fingerList{}, HTTP_FAIL
		}
		s, _ := ioutil.ReadAll(resp.Body)
		if err != nil {
			return []_fingerList{}, err
		}
		fingerInfo := getFingerInfo{}
		err = json.Unmarshal(s, &fingerInfo)
		if err != nil {
			return []_fingerList{}, err
		}
		if fingerInfo._getFingerInfo.Status == "NoFP" {
			return rFinList, nil
		}
		if fingerInfo._getFingerInfo.Status == "OK" {
			for i := 0; i < len(fingerInfo.FingerPrintList); i++ {
				rFinList = append(rFinList, fingerInfo.FingerPrintList[i])
			}
		}
	}
	return rFinList, nil
}

// 增加指纹
func (h *Hk) AddUserFp(emStr, fpData, ftype string, fid int) error {
	af := addFinger{}
	af._addFinger = _addFinger{
		EmployeeNo:       emStr,
		EnableCardReader: []int{1},
		FingerPrintId:    fid,
		FingerType:       ftype,
		FingerData:       fpData,
	}

	t := digest.NewTransport(h.AdminName, h.AdminPwd)
	c, err := t.Client()
	if err != nil {
		return err
	}
	jc, err := json.Marshal(af)
	//fmt.Println(string(jc))
	if err != nil {
		fmt.Println("err")
		return err
	}
	resp, err := c.Post(h.Host+addFingerUrl, jsonType, bytes.NewReader(jc))
	if err != nil {
		fmt.Println("err")
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		return nil
	}

	//检查是否重复,允许重复则返回重复则认为成功
	s, _ := ioutil.ReadAll(resp.Body)
	//		fmt.Printf("%s\n", string(s))
	eret := &_addfingerErrResp{}
	err = json.Unmarshal(s, eret)
	if err != nil {
		return err
	}
	if eret.SubStatusCode == "alreadyExistFP" {
		return nil
	} else {
		return errors.New(eret.SubStatusCode)
	}
	return nil
}

type _cardReaderDetail struct {
	CardReaderNo int    `json:"cardReaderNo"`
	ClearAllCard bool   `json:"clearAllCard"`
	EmployeeNo   string `json:"employeeNo"`
}
type _fingerPrintDeleteByEm struct {
	Mode              string `json:"mode"`
	_cardReaderDetail `json:"CardReaderDetail"`
}
type fingerPrintDeleteByEm struct {
	_fingerPrintDeleteByEm `json:"FingerPrintDelete"`
}

func (h *Hk) delFingerEmAll(emList []string) bool {
	for _, emNo := range emList {
		fpe := fingerPrintDeleteByEm{}
		fpe.Mode = "byCardReader"
		fpe.CardReaderNo = 1
		fpe.ClearAllCard = false
		fpe.EmployeeNo = emNo
		jc, err := json.Marshal(fpe)
		if err != nil {
			return false
		}
		if err = httpPut(h.Host+delFingerUrl, jc, h.AdminName, h.AdminPwd); err != nil {
			return false
		}
		for {
			str, _ := h.GetDelFpProgress()
			if str == "" {
				return false
			} else if str == "success" {
				break
			} else if str == "failed" {
				return false
			}
		}
	}
	return true
}

// 删除指纹
func (h *Hk) DelUserFp(emStr string, fid []int) error {
	fdel := fingerPrintDelete{}
	fdel.Mode = "byEmployeeNo"
	fdel.EmployeeNo = emStr
	fdel.FingerPrintID = fid
	fdel.EnableCardReader = []int{1}
	jc, err := json.Marshal(fdel)
	if err != nil {
		return err
	}
	return httpPut(h.Host+delFingerUrl, jc, h.AdminName, h.AdminPwd)
}

// 获取增加指纹进度
func (h *Hk) GetAddFpProgress() (int, error) {
	t := digest.NewTransport(h.AdminName, h.AdminPwd)
	c, err := t.Client()
	if err != nil {
		return -1, err
	}
	resp, err := c.Get(h.Host + addFingerProcessUrl)
	if err != nil {
		return -1, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return -1, HTTP_FAIL
	}

	s, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return -1, err
	}
	ap := addFingerProcess{}
	err = json.Unmarshal(s, &ap)
	if err != nil {
		return -1, err
	}
	return ap.TotalStatus, nil
}

// 获取删除指纹进度
func (h *Hk) GetDelFpProgress() (string, error) {
	t := digest.NewTransport(h.AdminName, h.AdminPwd)
	c, err := t.Client()
	if err != nil {
		return "", err
	}
	resp, err := c.Get(h.Host + getDelFingerProcessUrl)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", HTTP_FAIL
	}

	s, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", HTTP_FAIL
	}
	dp := delFingerProcess{}
	err = json.Unmarshal(s, &dp)
	if err != nil {
		return "", HTTP_FAIL
	}
	return dp.Status, nil
}
func getFaceInfo(host string, emNo string, name string, pwd string) (int, faceInfo) {
	gf := getFaceParam{}
	gf.SearchResultPosition = 0
	gf.MaxResults = 20
	gf.FaceLibType = "blackFD"
	gf.FDID = "1"
	gf.FPID = emNo
	jc, _ := json.Marshal(gf)

	t := digest.NewTransport(name, pwd)
	c, err := t.Client()
	if err != nil {
		return 400, faceInfo{}
	}
	resp, err := c.Post(host+getFaceUrl, jsonType, bytes.NewReader(jc))
	if err != nil {
		return 400, faceInfo{}
	}
	defer resp.Body.Close()
	if resp.StatusCode != 200 {
		return 400, faceInfo{}
	}
	rd, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return 400, faceInfo{}
	}
	finfo := faceInfo{}
	err = json.Unmarshal(rd, &finfo)
	if err != nil {
		return 400, faceInfo{}
	}
	return 200, finfo
}

// 获取人脸
func (h *Hk) GetUserFace(emStr string) (*toWebFaceInfo, error) {
	toweb := &toWebFaceInfo{}
	for {
		if !strings.Contains(h.Ability, "F") {
			return nil, IAVLID_ERR
		}

		code, finfo := getFaceInfo(h.Host, emStr, h.AdminName, h.AdminPwd)
		if code != 200 {
			return nil, DEVICE_ERR
		}
		if finfo.StatusCode != 1 {
			return nil, DEVICE_ERR

		}
		if finfo.NumOfMatches == 0 {
			toweb.Face = ""
			break
		}

		//获取图片的实际数据，转为base64
		t := digest.NewTransport(h.AdminName, h.AdminPwd)
		tc, err := t.Client()
		if err != nil {
			return nil, IAVLID_ERR
		}
		resp2, err := tc.Get(finfo.MatchList[0].FaceUrl)
		if err != nil {
			return nil, IAVLID_ERR

		}
		//	fmt.Printf("%v\n", resp2.Header)
		defer resp2.Body.Close()
		if resp2.StatusCode != 200 {
			toweb.Face = ""
			break
		}
		jpg, err := ioutil.ReadAll(resp2.Body)
		if err != nil {
			return nil, DEVICE_ERR
		}
		toweb.Face = b64.StdEncoding.EncodeToString(jpg)
		break
	}
	return toweb, nil
}

// 删除人脸
func (h *Hk) DelUserFace(emStr string) error {
	deFace := delFace{}
	deFace.FaceLibType = "blackFD"
	deFace.FDID = "1"
	deFace.FPID = emStr
	deFace.DeleteFP = true
	jc, _ := json.Marshal(deFace)
	t := digest.NewTransport(h.AdminName, h.AdminPwd)
	c, err := t.Client()
	if err != nil {
		return err
	}
	bodyBuffer := &bytes.Buffer{}
	bodyWriter := multipart.NewWriter(bodyBuffer)

	bodyWriter.WriteField("", string(jc))
	bodyWriter.Close()
	req, err := http.NewRequest("PUT", h.Host+delFaceUrl, bytes.NewReader(jc))
	if err != nil {
		return err
	}

	resp, err := c.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	ioutil.ReadAll(resp.Body)
	if resp.StatusCode == 200 {
		return nil
	} else {
		return DEVICE_ERR
	}
	return nil
}

//type HkAddFaceParam struct {
//	ImageType string
//	ImageName string
//}

func (h *Hk) GetManuFactName() string {
	return "海康"
}

// 增加人脸
func (h *Hk) addFaceInternal(
	imageType string, imageName string, facedata []byte, emno string) (int, error) {
	bodyBuffer := &bytes.Buffer{}
	bodyWriter := multipart.NewWriter(bodyBuffer)

	//表单第一项
	j := addFaceParam{}
	j.FaceLibType = "blackFD"
	j.FDID = "1"
	j.FPID = emno
	jj, err := json.Marshal(j)
	bodyWriter.WriteField("FaceDataRecord", string(jj))

	//表单第二项
	ftype := imageType
	fname := imageName

	mh := make(textproto.MIMEHeader)
	mh.Set("Content-Disposition",
		fmt.Sprintf(`form-data; name="%s"; filename="%s"`,
			"img",
			fname))
	mh.Set("Content-Type", ftype)
	cwf, err := bodyWriter.CreatePart(mh)

	if err != nil {
		return 0, errors.New("内部构造错误")
	}

	_, err = io.Copy(cwf, bytes.NewReader(facedata))
	if err != nil {
		return 0, errors.New("读取人脸数据失败")
	}

	contentType := bodyWriter.FormDataContentType()
	bodyWriter.Close()

	t := digest.NewTransport(h.AdminName, h.AdminPwd)
	c, err := t.Client()
	if err != nil {
		return 0, errors.New("创建http客户端失败")
	}
	req, err := http.NewRequest("PUT", h.Host+addFaceUrl, bodyBuffer)
	if err != nil {
		return 0, errors.New("创建http客户端失败")
	}
	req.Header.Add("Content-Type", contentType)

	resp, err := c.Do(req)
	if err != nil {
		return 0, errors.New("http传输数据失败")
	}
	defer resp.Body.Close()
	//	rebyte, _ := io.ReadAll(resp.Body)
	//	fmt.Printf("%s\n", string(rebyte))
	code := resp.StatusCode
	return code, err
}

func (h *Hk) AddUserFace(emStr string, facedata []byte, param interface{}) error {
	p := param.(model.HkAddFaceParam)
	_, err := h.addFaceInternal(p.ImageType, p.ImageName, facedata, emStr)
	return err
}

// 获取指定人员卡号
func (h *Hk) GetUserCard(emStr string) ([]ToWebGetCardInfo, error) {
	startPos := 0
	cardList := []ToWebGetCardInfo{}
	unid := getUniId()

	for {
		cardCmd := cardSearchCmd{}
		cardCmd.SearchID = unid
		cardCmd.SearchResultPosition = startPos
		cardCmd.MaxResults = 30
		cardCmd.EmployeeNoList = append(cardCmd.EmployeeNoList, _userNo{EmployeeNo: emStr})
		jc, err := json.Marshal(cardCmd)
		if err != nil {
			fmt.Println("err1", err)
			return nil, errors.New("err set json cmd")
		}
		t := digest.NewTransport(h.AdminName, h.AdminPwd)
		c, err := t.Client()
		if err != nil {
			fmt.Println("err2", err)
			return nil, errors.New("err get http")
		}
		resp, err := c.Post(h.Host+getCardUrl, jsonType, bytes.NewReader(jc))
		if err != nil {
			fmt.Println("err3", err)
			return nil, errors.New("err get http ret")
		}
		defer resp.Body.Close()
		if resp.StatusCode != http.StatusOK {
			fmt.Println("err4")
			return nil, errors.New("err http ret")
		}
		s, _ := ioutil.ReadAll(resp.Body)
		//fmt.Println(string(s))
		if err != nil {
			fmt.Println("err5")
			return nil, errors.New("err body")
		}
		cardRet := cardSearchRet{}
		err = json.Unmarshal(s, &cardRet)
		if err != nil {
			fmt.Println("err6")
			return nil, errors.New("err json unmar")
		}
		if cardRet.ResponseStatusStrg == "NO MATCH" {
			return nil, nil
		}
		//fmt.Println(len(cardRet.CardInfo))
		if len(cardRet.CardInfo) > 0 {
			for _, v := range cardRet.CardInfo {
				cardList = append(cardList, ToWebGetCardInfo{
					CardNo:   v.CardNo,
					CardType: v.CardType,
				})
			}
		}
		//fmt.Println(string(s))
		//fmt.Printf("card,%v\n", cardList)
		if cardRet.ResponseStatusStrg == "OK" {
			return cardList, nil
		}
		if cardRet.ResponseStatusStrg == "MORE" {
			startPos += cardRet.NumOfMatches
		}
	}

	return cardList, nil
}

type _addCard struct {
	EmployeeNo string `json:"employeeNo"`
	CardNo     string `json:"cardNo"`
	CardType   string `json:"cardType"`
}
type addCardInfo struct {
	_addCard `json:"CardInfo"`
}

// 添加卡号
func (h *Hk) AddUserCard(emStr, cardNoStr, cardType string) error {
	ac := addCardInfo{}
	ac.EmployeeNo = emStr
	ac.CardNo = cardNoStr
	ac.CardType = cardType

	t := digest.NewTransport(h.AdminName, h.AdminPwd)
	c, err := t.Client()
	if err != nil {
		return err
	}
	jc, err := json.Marshal(ac)
	if err != nil {
		fmt.Println("err")
		return err
	}
	resp, err := c.Post(h.Host+addCardUrl, jsonType, bytes.NewReader(jc))
	if err != nil {
		fmt.Println("err")
		return err
	}
	defer resp.Body.Close()
	if resp.StatusCode == http.StatusOK {
		return nil
	}
	s, _ := ioutil.ReadAll(resp.Body)
	eret := &_addfingerErrResp{}
	err = json.Unmarshal(s, eret)
	if err != nil {
		return err
	}
	if eret.SubStatusCode == "cardNoAlreadyExist" {
		return nil
	} else {
		return errors.New(eret.SubStatusCode)
	}

	return nil
}

type _delCardByNo struct {
	CardNo string `json:"cardNo"`
}

// 删除卡号
type _delCardCond struct {
	EmployeeNoList []_delCardByEm `json:"EmployeeNoList"`
	CardNoList     []_delCardByNo `json:"CardNoList"`
}
type _delCardCondByNo struct {
	CardNoList []_delCardByNo `json:"CardNoList"`
}
type delCardCond struct {
	_delCardCond `json:"CardInfoDelCond"`
}
type delCardCondByNo struct {
	_delCardCondByNo `json:"CardInfoDelCond"`
}

func (h *Hk) DelUserCard(delList []DelCardByNoParam) error {
	dc := delCardCondByNo{}
	for i := range delList {
		dc.CardNoList = append(dc.CardNoList, _delCardByNo{CardNo: delList[i].CardNo})
	}

	jc, err := json.Marshal(dc)
	if err != nil {
		fmt.Println("err")
		return err
	}

	return httpPut(h.Host+delCardUrl, jc, h.AdminName, h.AdminPwd)
}

func (h *Hk) DelUserCardByEmNo(emNos []string) error {
	dc := delCardCond{}
	dc.CardNoList = []_delCardByNo{}
	for _, v := range emNos {
		dc.EmployeeNoList = append(dc.EmployeeNoList, _delCardByEm{EmployeeNo: v})
	}
	//dc.EmployeeNoList = append(dc.EmployeeNoList, _delCardByEm{EmployeeNo: emNo})
	jc, err := json.Marshal(dc)
	if err != nil {
		fmt.Println("err")
		return err
	}
	return httpPut(h.Host+delCardUrl, jc, h.AdminName, h.AdminPwd)
}

func (h *Hk) GetEventRecord(id int, pos int, startTime string, endTime string) (*ToWebRecord, error) {
	er := eventRecord{}
	usedParam := eventRecordParam{}
	if pos == 0 {
		//DoorCfg[id].lastSearchId = getUniId()
		//DoorCfg[id].revCnt = 0
		param := eventRecordParam{
			lastSearchId: getUniId(),
			revcnt:       0,
		}
		eventCache.Store(id, param)
		usedParam.lastSearchId = param.lastSearchId
		usedParam.revcnt = 0
	} else {
		val, ok := eventCache.Load(id)
		if !ok {
			return nil, errors.New("获取缓存信息失败")
		}
		usedParam = val.(eventRecordParam)
	}

	er.SearchID = usedParam.lastSearchId
	er.SearchResultPosition = pos
	er.MaxResults = 30
	er.Major = 0
	er.Minor = 0
	er.StartTime = startTime
	er.EndTime = endTime

	t := digest.NewTransport(h.AdminName, h.AdminPwd)
	c, err := t.Client()
	if err != nil {
		return nil, ErrGetTrans
	}
	jc, err := json.Marshal(er)
	if err != nil {
		fmt.Println("err")
		return nil, ErrJson
	}
	//fmt.Println(string(jc))
	c.Timeout = time.Second * 10
	resp, err := c.Post(h.Host+getRecordUrl, jsonType, bytes.NewReader(jc))
	if err != nil {
		fmt.Println("err", err)
		return nil, ErrHttpCom
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		s, _ := ioutil.ReadAll(resp.Body)
		log.Println("err read door record:", string(s))
		log.Println("start:", startTime, "end:", endTime, "id:", id, "pos:", pos)
		return nil, ERRHttpNot200
	}
	s, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Println("read body error")
		log.Print(string(s))
		return nil, ErrJson
	}
	ae := ascEvent{}
	err = json.Unmarshal(s, &ae)
	if err != nil {
		return nil, ErrJson
	}
	//fmt.Println(string(s))
	tw := ToWebRecord{}
	tw.ResponseStatusStrg = ae.AcsEvent.ResponseStatusStrg
	tw.TotalMatches = ae.AcsEvent.TotalMatches
	tw.NumOfMatches = ae.AcsEvent.NumOfMatches
	for i := 0; i < len(ae.AcsEvent.InfoList); i++ {
		//fmt.Printf("major: %d, minor: %02x\n", ae.AcsEvent.InfoList[i].Major, ae.AcsEvent.InfoList[i].Minor)
		info, ok := minorString[ae.AcsEvent.InfoList[i].Major][ae.AcsEvent.InfoList[i].Minor]
		if !ok {
			continue
		}
		tw.ToWebInfoList = append(tw.ToWebInfoList, toWebInfoList{
			Time:             ae.AcsEvent.InfoList[i].Time,
			CardNo:           ae.AcsEvent.InfoList[i].CardNO,
			EmployeeNoString: ae.AcsEvent.InfoList[i].EmployeeNoString,
			Info:             info,
		})
	}

	usedParam.revcnt += len(ae.AcsEvent.InfoList)
	if usedParam.revcnt == tw.TotalMatches {
		if tw.ResponseStatusStrg != "OK" {
			tw.ResponseStatusStrg = "OK"
		}
	}
	eventCache.Store(id, usedParam)
	//fmt.Printf("%v\n", tw)
	return &tw, nil
}
func (h *Hk) StartSync(sy request.StartSyncReq, doorConf []model.Door) {

	g_syncRet.Percentage = 0
	g_syncRet.Info = "开始同步信息"
	g_syncSucc = true
	var err error

	ip := h.Host
	manname := h.AdminName
	manpwd := h.AdminPwd
	srcAbility := h.Ability
	//出错恢复
	if err := recover(); err != nil {
		fmt.Println("test 发生错误", err)
	}

	wu, err := h.GetUserList()
	if err != nil {
		g_syncSucc = false
		g_syncErr.Code = 10003
		g_syncErr.Msg = "获取一体机的人员信息失败"
		g_syncErr.Desc = ""
		return
	}
	time.Sleep(500 * time.Millisecond)
	totalstep := 0
	desnum := len(sy.Destination)
	sourcestep := 0
	donestep := 0
	for _, v := range sy.Source.EmployeeNoList {
		//建立人员信息
		sourcestep += 1
		//下发卡号步骤
		sourcestep += len(v.CardList)
		//下发指纹步骤
		sourcestep += len(v.FingerPrintIDList)
		//下发人脸步骤
		if v.Face {
			sourcestep += 1
		}
	}
	totalstep = desnum * sourcestep

	for _, v := range wu {
		for _, e := range sy.Source.EmployeeNoList {
			if e.EmployeeNo == v.EmployeeNo {
				//获取待下发的人员用户名\密码\卡号\指纹人脸信息
				username := v.Name
				userpwd := v.Password
				emno := v.EmployeeNo
				openDoorWay := v.UserVerifyMode
				//待下发获取卡号
				downCardinfo := e.CardList
				//待下发指纹数据
				allFinger := []_fingerList{}
				downFinger := []_addFinger{}
				if strings.Contains(h.Ability, "P") && len(e.FingerPrintIDList) > 0 {
					allFinger, err = h.GetUserFp(e.EmployeeNo)
					if err != nil {
						g_syncSucc = false
						g_syncErr.Code = 10003
						g_syncErr.Msg = "获取一体机的指纹信息失败"
						fmt.Printf("%s\n", g_syncErr.Msg)
						g_syncErr.Desc = err.Error()
						return
					}
					time.Sleep(500 * time.Millisecond)
					for _, v := range allFinger {
						for _, hf := range e.FingerPrintIDList {
							if v.FingerPrintID == hf {
								a := _addFinger{}
								a.FingerData = v.FingerData
								//待实际下发的时候再进行替换
								a.EmployeeNo = "1"
								a.EnableCardReader = []int{1}
								a.FingerType = v.FingerType
								a.FingerPrintId = v.FingerPrintID
								downFinger = append(downFinger, a)
							}
						}
					}
					if len(downFinger) == 0 {
						g_syncSucc = false
						g_syncErr.Code = 10010
						g_syncErr.Msg = "无效的指纹ID"
						g_syncErr.Desc = ""
						fmt.Printf("%s\n", g_syncErr.Msg)
						return
					}
				}

				//待下发人脸数据
				var faceData []byte
				facetype := ""
				for {
					if strings.Contains(h.Ability, "F") && e.Face {
						gfret, finfo := getFaceInfo(ip, emno, manname, manpwd)
						if gfret != 200 {
							g_syncSucc = false
							g_syncErr.Code = 10004
							g_syncErr.Msg = "获取一体机的人脸失败"
							fmt.Printf("%s\n", g_syncErr.Msg)
							g_syncErr.Desc = ""
							return
						}
						if finfo.StatusCode != 1 {
							g_syncSucc = false
							g_syncErr.Code = 10004
							g_syncErr.Msg = "获取一体机的人脸失败"
							fmt.Printf("%s\n", g_syncErr.Msg)
							g_syncErr.Desc = ""
							return
						}

						if finfo.NumOfMatches == 0 {
							break
						}

						t := digest.NewTransport(manname, manpwd)
						c, err := t.Client()
						if err != nil {
							g_syncSucc = false
							g_syncErr.Code = 10004
							g_syncErr.Msg = "获取一体机的人脸失败"
							fmt.Printf("%s\n", g_syncErr.Msg)
							g_syncErr.Desc = err.Error()
							return
						}
						resp2, err := c.Get(finfo.MatchList[0].FaceUrl)
						if err != nil {
							g_syncSucc = false
							g_syncErr.Code = 10004
							g_syncErr.Msg = "获取一体机的人脸失败"
							fmt.Printf("%s\n", g_syncErr.Msg)
							g_syncErr.Desc = err.Error()
							return
						}
						defer resp2.Body.Close()
						if resp2.StatusCode != 200 {
							g_syncSucc = false
							g_syncErr.Code = 10004
							g_syncErr.Msg = "获取一体机的人脸失败"
							fmt.Printf("%s\n", g_syncErr.Msg)
							g_syncErr.Desc = ""
							return
						}
						jpg, err := ioutil.ReadAll(resp2.Body)
						if err != nil {
							g_syncSucc = false
							g_syncErr.Code = 10004
							g_syncErr.Msg = "获取一体机的人脸失败"
							fmt.Printf("%s\n", g_syncErr.Msg)
							g_syncErr.Desc = err.Error()
							return
						}
						faceData = jpg
						facetype = resp2.Header["Content-Type"][0]
						break
					} else {
						break
					}
				}
				time.Sleep(500 * time.Millisecond)
				//信息获取完成,下发信息至指定的一体机
				for _, dst := range sy.Destination {
					dstDoorInfo := model.DoorInfo{}
					for dstIdx := range doorConf {
						if doorConf[dstIdx].Id == int64(dst) {
							dstDoorInfo = doorConf[dstIdx].DoorInfo
							break
						}
					}

					dstname := dstDoorInfo.Name

					dstHk := &Hk{
						AdminName: dstDoorInfo.ManName,
						AdminPwd:  dstDoorInfo.ManPwd,
						Host:      fmt.Sprintf("http://%s", dstDoorInfo.Ip),
						Ability:   dstDoorInfo.Ability,
					}

					//建立人员信息,存在同名的人员则原地更新,不存在则新建
					emstr := ""
					duinfo, err := dstHk.GetUserList()
					if err != nil {
						g_syncSucc = false
						g_syncErr.Code = 10005
						g_syncErr.Msg = fmt.Sprintf("获取%s现有人员数量失败", dstname)
						fmt.Printf("%s\n", g_syncErr.Msg)
						g_syncErr.Desc = ""
						return
					}

					maxeo := 0
					for _, v := range duinfo {
						if v.Name == username {
							emstr = v.EmployeeNo
							break
						} else {
							tempno, err := strconv.Atoi(v.EmployeeNo)
							if err != nil {
								g_syncSucc = false
								g_syncErr.Code = 10005
								g_syncErr.Msg = fmt.Sprintf("获取%s现有人员数量失败", dstname)
								fmt.Printf("%s\n", g_syncErr.Msg)
								g_syncErr.Desc = err.Error()
								return
							}
							if tempno >= maxeo {
								maxeo = tempno
							}
						}
					}
					if emstr == "" {
						//最大的工号+1
						emstr = strconv.Itoa(maxeo + 1)
						//兼容以前支持单独密码开门的门控器
						if openDoorWay == "cardOrfaceOrPw" {
							openDoorWay = "cardOrFace"
						}
						if dstHk.Ability != srcAbility {
							openDoorWay = ""
						}
						aret := dstHk.AddOneUser(emstr, username, userpwd, openDoorWay, v.LocalUIRight)
						if aret != nil {
							fmt.Printf("add user fail,user: %s\n", username)
							g_syncSucc = false
							g_syncErr.Code = 10005
							g_syncErr.Msg = fmt.Sprintf("添加%s至%s失败", username, dstname)
							fmt.Printf("%s\n", g_syncErr.Msg)
							g_syncErr.Desc = ""
							return
						}
					}

					donestep++
					time.Sleep(500 * time.Millisecond)
					g_syncRet.Percentage = 100 * donestep / totalstep
					g_syncRet.Info = fmt.Sprintf("添加%s至%s成功", username, dstname)
					//下发卡号
					if strings.Contains(dstHk.Ability, "C") {
						for _, v := range downCardinfo {
							aret := dstHk.AddUserCard(emstr, v.CardNo, v.CardType)
							if aret != nil {
								g_syncSucc = false
								g_syncErr.Code = 10005
								g_syncErr.Msg = fmt.Sprintf("添加用户:%s的卡片%s至%s失败", username, v, dstname)
								fmt.Printf("%s\n", g_syncErr.Msg)
								g_syncErr.Desc = ""
								return
							}
							donestep++
							time.Sleep(500 * time.Millisecond)
							g_syncRet.Percentage = 100 * donestep / totalstep
							g_syncRet.Info = fmt.Sprintf("添加用户:%s的卡片%s至%s成功", username, v.CardNo, dstname)
						}
					}
					//下发指纹
					if strings.Contains(dstHk.Ability, "P") {
						for k, _ := range downFinger {
							downFinger[k].EmployeeNo = emstr
							aret := dstHk.AddUserFp(emstr, downFinger[k].FingerData, downFinger[k].FingerType, downFinger[k].FingerPrintId)
							if aret != nil {
								g_syncSucc = false
								g_syncErr.Code = 10005
								g_syncErr.Msg = fmt.Sprintf("添加%s的指纹:%d至%s失败", username, downFinger[k].FingerPrintId, dstname)
								fmt.Printf("%s\n", g_syncErr.Msg)
								g_syncErr.Desc = ""
								return
							}
							//获取下发进度
							{
								maxrpt := 10
								for ; maxrpt >= 0; maxrpt-- {
									fmt.Printf("finger cycle: %d\n", maxrpt)
									addFpRet, err := dstHk.GetAddFpProgress()
									if err != nil {
										g_syncSucc = false
										g_syncErr.Code = 10005
										g_syncErr.Msg = fmt.Sprintf("添加%s的指纹:%d至%s失败", username, downFinger[k].FingerPrintId, dstname)
										fmt.Printf("%s\n", g_syncErr.Msg)
										g_syncErr.Desc = ""
										return
									}
									fmt.Printf("add finger proress: %d\n", addFpRet)
									if addFpRet == 1 {
										break
									}
									time.Sleep(200 * time.Millisecond)
								}
								if maxrpt <= 0 {
									g_syncSucc = false
									g_syncErr.Code = 10005
									g_syncErr.Msg = fmt.Sprintf("添加%s的指纹:%d至%s下发进度失败", username, downFinger[k].FingerPrintId, dstname)
									fmt.Printf("%s\n", g_syncErr.Msg)
									g_syncErr.Desc = ""
									return
								}
							}
							donestep++
							time.Sleep(500 * time.Millisecond)
							g_syncRet.Percentage = 100 * donestep / totalstep
							g_syncRet.Info = fmt.Sprintf("添加用户:%s的指纹%d至%s成功", username, downFinger[k].FingerPrintId, dstname)
						}
					} else {
						donestep += len(downFinger)
						g_syncRet.Percentage = 100 * donestep / totalstep
					}
					//下发人脸
					if strings.Contains(dstHk.Ability, "F") && len(faceData) > 0 {
						aret, err := dstHk.addFaceInternal(facetype, "face", faceData, emstr)
						if err != nil {
							g_syncSucc = false
							g_syncErr.Code = 10005
							g_syncErr.Msg = fmt.Sprintf("添加%s的人脸至%s失败", username, dstname)
							fmt.Printf("%s\n", g_syncErr.Msg)
							g_syncErr.Desc = err.Error()
							return
						}
						if aret != 200 {
							g_syncSucc = false
							g_syncErr.Code = 10005
							g_syncErr.Msg = fmt.Sprintf("添加%s的人脸至%s失败", username, dstname)
							fmt.Printf("%s\n", g_syncErr.Msg)
							g_syncErr.Desc = ""
							return
						}

					} else {
						if e.Face {
							donestep++
							g_syncRet.Percentage = 100 * donestep / totalstep
						}
					}
					time.Sleep(500 * time.Millisecond)
				}

			}
		}
	}
	fmt.Printf("add success")
	g_syncRet.Percentage = 100
	g_syncRet.Info = "成功"
}
