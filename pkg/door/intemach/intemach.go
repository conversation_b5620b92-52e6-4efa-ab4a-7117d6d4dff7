package intemach

import (
	"errors"
	"fmt"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
)

var (
	IAVLID_ERR = errors.New("参数不正确")
	DEVICE_ERR = errors.New("硬件执行错误")
)

// --------------------------------------------------------------
// 获取事件
type _eventCond struct {
	SearchID             string `json:"searchID"`
	SearchResultPosition int    `json:"searchResultPosition"`
	MaxResults           int    `json:"maxResults"`
	Major                int    `json:"major"`
	Minor                int    `json:"minor"`
	StartTime            string `json:"startTime"`
	EndTime              string `json:"endTime"`
}
type eventRecord struct {
	_eventCond `json:"AcsEventCond"`
}

type webDel struct {
	Doorid         int      `json:"doorId"`
	EmployeeNoList []string `json:"employeeNoList"`
}

type toWebDoorStatus struct {
	DoorStatus int `json:"doorStatus"`
}

type _fingerList struct {
	FingerPrintID int    `json:"fingerPrintID"`
	FingerType    string `json:"fingerType"`
	FingerData    string `json:"fingerData"`
}
type toWebFaceInfo struct {
	Face string `json:"face"`
}
type _getCardInfo struct {
	CardNo   string `json:"cardNo"`
	CardType string `json:"cardType"`
}

type DelCardByNoParam struct {
	CardNo string `json:"cardNo"`
}
type _delCardByEm struct {
	EmployeeNo string `json:"employeeNo"`
}

// 返回的事件结构体
type infoList struct {
	Major            int    `json:"major"`
	Minor            int    `json:"minor"`
	Time             string `json:"time"`
	CardNO           string `json:"cardNo"`
	EmployeeNoString string `json:"employeeNoString"`
}
type _acsEvent struct {
	SearchID           string     `json:"searchID"`
	ResponseStatusStrg string     `json:"responseStatusStrg"`
	NumOfMatches       int        `json:"numOfMatches"`
	TotalMatches       int        `json:"totalMatches"`
	InfoList           []infoList `json:"infoList"`
}
type ascEvent struct {
	AcsEvent _acsEvent `json:"AcsEvent"`
}
type toWebInfoList struct {
	Info             string `json:"info""`
	Time             string `json:"time"`
	CardNo           string `json:"cardNo"`
	EmployeeNoString string `json:"employeeNoString"`
}
type ToWebRecord struct {
	Reply              int             `json:"reply"`
	ResponseStatusStrg string          `json:"responseStatusStrg"`
	NumOfMatches       int             `json:"numOfMatches"`
	TotalMatches       int             `json:"totalMatches"`
	ToWebInfoList      []toWebInfoList `json:"infoList"`
}
type _doorInfo struct {
	Id           int    `json:"id"`
	Type         string `json:"type"`
	Ip           string `json:"ip"`
	ManName      string `json:"manName"`
	ManPwd       string `json:"manPwd"`
	ManuFacturer string `json:"manuFacturer"`
	Port         int    `json:"port"`
	Name         string `json:"name"`
	Ability      string `json:"ability"`
	SubCount     int    `json:"subCount"`
	lastSearchId string
	revCnt       int
}
type ErrSync struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Desc string `json:"desc"`
}

type ToWebUserInfo struct {
	EmployeeNo     string `json:"employeeNo"`
	Name           string `json:"name"`
	Password       string `json:"password"`
	LocalUIRight   bool   `json:"localUIRight"`
	UserVerifyMode string `json:"userVerifyMode"`
}

type ToWebGetCardInfo struct {
	CardNo   string `json:"cardNo"`
	CardType string `json:"cardType"`
}

var g_syncRet SyncRet
var g_syncSucc bool
var g_syncErr ErrSync

type InteMach interface {
	//获取门禁的能力
	//门状态
	GetManuFactName() string
	GetDoorStatus() (int, error)
	////控制门
	CtlDoorStatus(way string) error
	////获取人员列表
	GetUserList() ([]ToWebUserInfo, error)
	////添加人员
	AddOneUser(emStr, name, pwd, UserVerifyMode string, localUi bool) error
	////删除人员
	DelOneUser(emStr []string) error
	////修改人员信息
	ChangeOneUser(emStr, name, pwd, UserVerifyMode string, localUi bool) error
	////获取指纹
	GetUserFp(emStr string) ([]_fingerList, error)
	////增加指纹
	AddUserFp(emStr, fpData, ftype string, fid int) error
	////删除指纹
	DelUserFp(emStr string, fid []int) error
	////获取增加指纹进度
	GetAddFpProgress() (int, error)
	////获取删除指纹进度
	GetDelFpProgress() (string, error)
	////获取人脸
	GetUserFace(emStr string) (*toWebFaceInfo, error)
	////删除人脸
	DelUserFace(emStr string) error
	////增加人脸
	AddUserFace(emStr string, facedata []byte, params interface{}) error
	////获取指定人员卡号
	GetUserCard(emStr string) ([]ToWebGetCardInfo, error)
	////添加卡号
	AddUserCard(emStr, cardNoStr, cardType string) error
	////删除卡号
	DelUserCard(delList []DelCardByNoParam) error
	////获取历史记录
	GetEventRecord(id int, pos int, startTime string, endTime string) (*ToWebRecord, error)
	//开始同步
	StartSync(sy request.StartSyncReq, doorConf []model.Door)
}

func GetDoorImpl(dinfo *model.DoorInfo) (InteMach, error) {
	var im InteMach
	if dinfo.ManuFacturer == "海康" || dinfo.ManuFacturer == "" {
		im = &Hk{
			AdminName: dinfo.ManName,
			AdminPwd:  dinfo.ManPwd,
			Host:      fmt.Sprintf("http://%s", dinfo.Ip),
			Ability:   dinfo.Ability,
		}
	} else if dinfo.ManuFacturer == "中控" {
		if zk, err := ZkConnect(dinfo.Ip, dinfo.Port); err != nil {
			return nil, errors.New("初始化失败")
		} else {
			zk.Ability = dinfo.Ability
			im = zk
		}
	} else {
		return nil, errors.New("不支持的型号")
	}
	return im, nil
}

func GetSyanresult() (bool, SyncRet, ErrSync) {
	return g_syncSucc, g_syncRet, g_syncErr
}

//func InterSetDoor(id int, way string) {
//	idstr := fmt.Sprintf("%d", id)
//	im, ret := getDoorCfgInfo(idstr)
//	if !ret {
//		return
//	}
//	im.CtlDoorStatus(way)
//}

type _changUserReq struct {
	Doorid         int    `json:doorId`
	Emno           string `json:"employeeNo"`
	Name           string `json:"name"`
	Pwd            string `json:"password"`
	UserVerifyMode string `json:"userVerifyMode"`
	LocalUIRight   bool   `json:"localUIRight"`
}

type webDelFingerCmd struct {
	Doorid        int    `json:"doorId"`
	EmployeeNo    string `json:"employeeNo"`
	FingerPrintID []int  `json:"fingerPrintId"`
}

type toWebProgress struct {
	DelFingerProcess string `json:"delFingerProcess"`
}

//-----------------------------------------------------------------
//增加卡片

type webAddCard struct {
	Doorid int `json:"doorId"`
	_addCard
}

type webDelCard struct {
	Doorid      int            `json:"doorId"`
	DelCardByNo []_delCardByNo `json:"cardNo"`
}

// --添加指纹
type webAddFinger struct {
	Doorid        int    `json:"doorId"`
	EmployeeNo    string `json:"employeeNo"`
	FingerPrintId int    `json:"fingerPrintId"`
	FingerData    string `json:"fingerData"`
	FingerType    string `json:"fingerType"`
}

type webCmdFafceInfo struct {
	FaceLibType string `json:"faceLibType"`
	FDID        string `json:"FDID"`
	FPID        string `json:"FPID"`
}

type _dcardInfo struct {
	CardNo   string `json:"cardNo"`
	CardType string `json:"cardType"`
}
type _oneUser struct {
	EmployeeNo        string       `json:"employeeNo"`
	CardList          []_dcardInfo `json:"cardList"`
	FingerPrintIDList []int        `json:"fingerPrintIDList"`
	Face              bool         `json:"face"`
}
type _syncuserinfo struct {
	Id             int        `json:"id"`
	EmployeeNoList []_oneUser `json:"employeeNoList"`
}

type SyncReq struct {
	Source      _syncuserinfo `json:"source"`
	Destination []int         `json:"destination"`
}

// --返回下发进度
type SyncRet struct {
	Percentage int    `json:"percentage"`
	Info       string `json:"info"`
}
