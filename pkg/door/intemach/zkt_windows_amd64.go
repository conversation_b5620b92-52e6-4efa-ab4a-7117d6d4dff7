//go:build windows && amd64

// 中控一体机
package intemach

import (
	"encoding/base64"
	"errors"
	"fmt"
	"github.com/go-ole/go-ole"
	"github.com/go-ole/go-ole/oleutil"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
	"time"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
)

var zkt *ole.IDispatch = nil

type cachedParram struct {
	Ip   string
	Port int
}
type Zkt struct {
	Old     *ole.IDispatch
	Ability string
}

var ZKT_ERROR = errors.New("执行错误")
var openway = map[int]string{
	0:   "",
	128: "cardOrFpOrPw",
	129: "fp",
	132: "card",
	133: "fpOrPw",
	134: "fpOrCard",
	135: "cardOrPw",
	137: "fpAndPw",
	138: "fpAndCard",
	139: "cardAndPw",
	140: "fpAndCardAndPw",
	143: "face",
	144: "faceAndFp",
	145: "faceAndPw",
	146: "faceAndCard",
	147: "faceAndFpAndCard",
	148: "faceAndPwAndFp",
}

func ZkConnect(ip string, port int) (*Zkt, error) {
	ole.CoInitialize(0)
	var err error
	zktCom, err := oleutil.CreateObject("zkemkeeper.ZKEM.1")
	if err != nil {
		s := err.Error()
		println(s)
		return nil, err
	}
	zkt, _ := zktCom.QueryInterface(ole.IID_IDispatch)
	oleutil.PutProperty(zkt, "Visible", true)

	v, err := oleutil.CallMethod(zkt, "Connect_Net", ip, port)
	if err != nil {
		return nil, err
	}
	if !v.Value().(bool) {
		return nil, errors.New("err connect")
	}

	return &Zkt{
		Old: zkt,
	}, nil
}
func checkIsValid(v *ole.VARIANT, err error) int {
	if err != nil {
		return -1
	}
	ret := v.Value().(bool)
	if !ret {
		return -2
	}
	return 0
}
func (z *Zkt) disconnect() error {
	oleutil.CallMethod(z.Old, "Disconnect")
	z.Old.Release()
	return nil
}
func (z *Zkt) GetDoorStatus() (int, error) {
	defer z.disconnect()
	status := new(int)
	v, err := oleutil.CallMethod(z.Old, "GetDoorState", 1, status)
	if checkIsValid(v, err) != 0 {
		return -1, ZKT_ERROR
	}

	return *status, nil
}

// //控制门
func (z *Zkt) CtlDoorStatus(way string) error {
	defer z.disconnect()
	if way != "open" {
		return nil
	}
	v, err := oleutil.CallMethod(z.Old, "ACUnlock", 1, 10)
	if checkIsValid(v, err) != 0 {
		return ZKT_ERROR
	}

	return nil
}

// 获取人员列表
func (z *Zkt) getUserID() error {
	v, err := oleutil.CallMethod(z.Old, "ReadAllUserID", 1)
	if checkIsValid(v, err) != 0 {
		return ZKT_ERROR
	}
	return nil
}
func (z *Zkt) GetUserList() ([]ToWebUserInfo, error) {
	defer z.disconnect()
	return z.getUserList()
}

func (z *Zkt) getUserList() ([]ToWebUserInfo, error) {
	if err := z.getUserID(); err != nil {
		return nil, err
	}
	towebUser := []ToWebUserInfo{}

	//获取用户信息
	for {
		erollStr := new(string)
		name := new(string)
		pwd := new(string)
		privilege := new(int)
		enable := new(bool)
		one := ToWebUserInfo{}
		fmt.Println("for list: %v\n", towebUser)

		//获取除去开门方式的信息
		v, err := oleutil.CallMethod(z.Old, "SSR_GetAllUserInfo", 1, erollStr, name, pwd, privilege, enable)
		if err != nil {
			fmt.Printf("err: %s\n", err.Error())
			return nil, err
		}
		fmt.Println("for1: %v\n", towebUser)
		ret := v.Value().(bool)
		if !ret {
			println("get list break")
			break
		}
		fmt.Println("for2: %v\n", towebUser)
		one.EmployeeNo = *erollStr
		one.Name = *name
		one.Password = *pwd
		if *privilege != 0 {
			one.LocalUIRight = true
		} else {
			one.LocalUIRight = false
		}
		//    获取用户的开发方式
		vfymode := new(int)
		resv := new(uint8)
		erollNum, _ := strconv.Atoi(*erollStr)

		v, err = oleutil.CallMethod(z.Old, "GetUserInfoEx", 1, erollNum, vfymode, resv)
		if err != nil {
			fmt.Printf("err.Error(): %v\n", err.Error())
			return nil, err
		}
		if !v.Value().(bool) {
			fmt.Println("err value")
			return nil, ZKT_ERROR
		}
		fmt.Println("for3 %v\n", towebUser)
		if _, ok := openway[*vfymode]; ok {
			one.UserVerifyMode = openway[*vfymode]
		} else {
			one.UserVerifyMode = ""
		}
		fmt.Println("11t: %v\n", towebUser)
		towebUser = append(towebUser, one)
	}
	return towebUser, nil
}

//
////添加人员

func (z *Zkt) addOneUser(emStr, name, pwd, UserVerifyMode string, localUi bool) error {
	return z.ChangeOneUser(emStr, name, pwd, UserVerifyMode, localUi)
}
func (z *Zkt) AddOneUser(emStr, name, pwd, UserVerifyMode string, localUi bool) error {
	defer z.disconnect()
	return z.addOneUser(emStr, name, pwd, UserVerifyMode, localUi)
}

// 删除人员
func (z *Zkt) DelOneUser(emStr []string) error {
	defer z.disconnect()
	for _, emNo := range emStr {
		v, err := oleutil.CallMethod(z.Old, "SSR_DeleteEnrollData", 1, emNo, 12)
		if err != nil {
			return err
		}
		if !v.Value().(bool) {
			return ZKT_ERROR
		}
	}
	return nil
}

func (z *Zkt) setUserOpenMode(emNo string, openMode string) error {
	openNum := 0
	for k, v := range openway {
		if v == openMode {
			openNum = k
		}
	}
	enNum, _ := strconv.Atoi(emNo)
	resv := new(uint8)
	v, err := oleutil.CallMethod(z.Old, "SetUserInfoEx", 1, enNum, openNum, resv)
	if err != nil {
		return err
	}
	if !v.Value().(bool) {
		return ZKT_ERROR
	}
	return nil
}

////修改人员信息

func (z *Zkt) changeOneUser(emStr, name, pwd, UserVerifyMode string, localUi bool) error {
	privage := 0
	if localUi {
		privage = 3
	}
	v, err := oleutil.CallMethod(z.Old, "SSR_SetUserInfo", 1, emStr, name, pwd, privage, true)
	if err != nil {
		return err
	}
	if !v.Value().(bool) {
		return ZKT_ERROR
	}
	//设置用户本地开门权限
	return z.setUserOpenMode(emStr, UserVerifyMode)
}
func (z *Zkt) ChangeOneUser(emStr, name, pwd, UserVerifyMode string, localUi bool) error {
	defer z.disconnect()
	return z.changeOneUser(emStr, name, pwd, UserVerifyMode, localUi)
}

// //获取指纹
func (z *Zkt) getUserFp(emStr string) ([]_fingerList, error) {
	fpList := []_fingerList{}

	for i := 0; i < 10; i++ {
		fpStr := new(string)
		fpLen := new(int)
		v, err := oleutil.CallMethod(z.Old, "SSR_GetUserTmpStr", 1, emStr, i, fpStr, fpLen)
		if err != nil {
			return nil, err
		}
		if !v.Value().(bool) {
			continue
		}
		if *fpLen > 0 {
			one := _fingerList{}
			one.FingerData = *fpStr
			one.FingerPrintID = i + 1
			one.FingerType = "normalFP"
			fpList = append(fpList, one)
		}
	}
	return fpList, nil
}

func (z *Zkt) GetUserFp(emStr string) ([]_fingerList, error) {
	defer z.disconnect()
	return z.getUserFp(emStr)
}

// //增加指纹
func (z *Zkt) addUserFp(emStr, fpData, ftype string, fid int) error {
	addId := fid - 1
	v, err := oleutil.CallMethod(z.Old, "SSR_SetUserTmpStr", 1, emStr, addId, fpData)
	if err != nil {
		return err
	}
	if !v.Value().(bool) {
		return ZKT_ERROR
	}
	return nil
}

func (z *Zkt) AddUserFp(emStr, fpData, ftype string, fid int) error {
	defer z.disconnect()
	return z.addUserFp(emStr, fpData, ftype, fid)
}

// 删除指纹
func (z *Zkt) DelUserFp(emStr string, fid []int) error {
	for _, id := range fid {
		zkfid := id - 1

		v, err := oleutil.CallMethod(z.Old, "SSR_DelUserTmp", 1, emStr, zkfid)
		if err != nil {
			return err
		}
		if !v.Value().(bool) {
			return ZKT_ERROR
		}
	}
	return nil
}

// 获取增加指纹进度
func (z *Zkt) GetAddFpProgress() (int, error) {
	defer z.disconnect()
	return 1, nil
}

// 获取删除指纹进度
func (z *Zkt) GetDelFpProgress() (string, error) {
	defer z.disconnect()
	return "success", nil
}

// 获取人脸
func (z *Zkt) getFaceBinData(emStr string) ([]byte, error) {
	faceStr := [1024 * 1024]uint8{}
	faceLen := new(int)
	imgPath := fmt.Sprintf("%s.jpg", emStr)
	v, err := oleutil.CallMethod(z.Old, "GetUserFacePhotoByName", 1, imgPath, &faceStr[0], faceLen)
	if err != nil {
		fmt.Printf("err.Error(): %v\n", err.Error())
		return nil, err
	}
	if !v.Value().(bool) {
		return faceStr[0:1], nil
	}
	return faceStr[:*faceLen+1], nil
}

func (z *Zkt) GetUserFace(emStr string) (*toWebFaceInfo, error) {
	defer z.disconnect()
	toWeb := &toWebFaceInfo{}
	faceData, err := z.getFaceBinData(emStr)
	if err != nil {
		s := err.Error()
		println(s)
		return nil, errors.New(err.Error())
	}
	fstr := ""
	if len(faceData) > 100 {
		fstr = base64.StdEncoding.EncodeToString(faceData)

	}
	toWeb.Face = fstr

	return toWeb, nil
}

// 删除人脸
func (z *Zkt) DelUserFace(emStr string) error {
	defer z.disconnect()
	return errors.New("设备不支持")
}

func (z *Zkt) GetManuFactName() string {
	defer z.disconnect()
	return "中控"
}

// //增加人脸
func (z *Zkt) addUserFace(emStr string, facedata []byte, param interface{}) error {
	//需要web的二进制保存为实际文件
	dir, _ := os.Getwd()
	imgPath := fmt.Sprintf("%s\\verify_biophoto_9_%s.jpg", dir, emStr)
	err := ioutil.WriteFile(imgPath, facedata, 0644)
	if err != nil {
		return err
	}
	//调用接口发送
	v, err := oleutil.CallMethod(z.Old, "SendUserFacePhoto", 1, imgPath)
	if err != nil {
		fmt.Printf("err.Error(): %v\n", err.Error())
		return err
	}
	if !v.Value().(bool) {
		return ZKT_ERROR
	}
	return nil
}

func (z *Zkt) AddUserFace(emStr string, facedata []byte, param interface{}) error {
	//需要web的二进制保存为实际文件
	defer z.disconnect()
	return z.addUserFace(emStr, facedata, param)
}

// //获取指定人员卡号
func (z *Zkt) GetUserCard(emStr string) ([]ToWebGetCardInfo, error) {
	defer z.disconnect()
	if err := z.getUserID(); err != nil {
		return nil, err
	}

	//获取除去开门方式的信息
	for {
		erollStr := new(string)
		name := new(string)
		pwd := new(string)
		privilege := new(int)
		enable := new(bool)
		cardNo := new(string)
		cList := []ToWebGetCardInfo{}
		v, err := oleutil.CallMethod(z.Old, "SSR_GetAllUserInfo", 1, erollStr, name, pwd, privilege, enable)
		if err != nil {
			fmt.Printf("err: %s\n", err.Error())
			return nil, err
		}
		ret := v.Value().(bool)
		if !ret {
			break
		}
		//获取卡号
		if *erollStr == emStr {
			v, err = oleutil.CallMethod(z.Old, "GetStrCardNumber", cardNo)
			if checkIsValid(v, err) != 0 {
				return nil, errors.New("1")
			}

			if *cardNo != "0" {
				one := ToWebGetCardInfo{}
				one.CardType = "normalCard"
				one.CardNo = *cardNo
				cList = append(cList, one)
			}
			return cList, nil
		}
	}
	return nil, errors.New("not found")
}

// 添加卡号
func (z *Zkt) addUserCard(emStr, cardNoStr, cardType string) error {
	ulist, err := z.getUserList()
	selUser := &ToWebUserInfo{}

	for i := range ulist {
		if ulist[i].EmployeeNo == emStr {
			selUser.Name = ulist[i].Name
			selUser.Password = ulist[i].Password
			selUser.EmployeeNo = ulist[i].EmployeeNo
			selUser.LocalUIRight = ulist[i].LocalUIRight
			selUser.UserVerifyMode = ulist[i].UserVerifyMode
		}
	}
	//设置卡属性
	v, err := oleutil.CallMethod(z.Old, "SetStrCardNumber", cardNoStr)
	if checkIsValid(v, err) != 0 {
		return ZKT_ERROR
	}
	//设置人员属性
	if err = z.changeOneUser(selUser.EmployeeNo, selUser.Name, selUser.Password, selUser.UserVerifyMode, selUser.LocalUIRight); err != nil {
		return err
	}

	return nil
}
func (z *Zkt) AddUserCard(emStr, cardNoStr, cardType string) error {
	defer z.disconnect()
	return z.addUserCard(emStr, cardNoStr, cardType)
}

// //删除卡号
func (z *Zkt) DelUserCard(delList []DelCardByNoParam) error {
	return ZKT_ERROR
}
func (z *Zkt) GetEventRecord(id int, pos int, startTime string, endTime string) (*ToWebRecord, error) {
	toweb := &ToWebRecord{}
	err := z.getEventByTime(startTime, endTime)
	if err != nil {
		println(err)
	}

	for {
		//Info             string `json:"info""`
		//	Time             string `json:"time"`
		//	CardNo           string `json:"cardNo"`
		//	EmployeeNoString string `json:"employeeNoString"`
		oneline := toWebInfoList{}
		dwEnrollNumber := new(string)
		dwVerifyMode := new(int)
		dwInOutMode := new(int)
		dwYear := new(int)
		dwMonth := new(int)
		dwDay := new(int)
		dwHour := new(int)
		dwMinute := new(int)
		dwSecond := new(int)
		dwWorkcode := new(int)

		v, err := oleutil.CallMethod(z.Old, "SSR_GetGeneralLogData",
			1,
			dwEnrollNumber, dwVerifyMode, dwInOutMode, dwYear, dwMonth, dwDay, dwHour, dwMinute, dwSecond, dwWorkcode,
		)
		if err != nil {
			return nil, err
		}
		if !v.Value().(bool) {
			return toweb, nil
		}
		if *dwEnrollNumber == "" {
			continue
		}
		oneline.EmployeeNoString = *dwEnrollNumber
		oneline.Time = strconv.Itoa(*dwYear) + "-" + strconv.Itoa(*dwMonth) + "-" + strconv.Itoa(*dwDay) + " " + strconv.Itoa(*dwHour) + ":" + strconv.Itoa(*dwMonth) + ":" + strconv.Itoa(*dwSecond)
		switch *dwVerifyMode {
		case 0:
			oneline.Info = fmt.Sprintf("密码开门或者指纹或刷开")
		case 1:
			oneline.Info = fmt.Sprintf("指纹开门")
		case 2:
			oneline.Info = "PIN开门或刷卡开门"
		case 3:
			oneline.Info = "密码开门"
		case 4:
			oneline.Info = "刷卡开门"
		case 5:
			oneline.Info = "指纹或密码开门"
		case 6:
			oneline.Info = "指纹或刷卡开门"
		case 7:
			oneline.Info = "密码或刷卡开门"
		case 8:
			oneline.Info = "pin和指纹双校验开门"
		case 9:
			oneline.Info = "指纹和密码双校验开门"
		case 10:
			oneline.Info = "指纹和密码双校验开门"
		case 11:
			oneline.Info = "密码和刷卡双校验开门"
		case 12:
			oneline.Info = "指纹密码刷卡同时校验开门"
		case 13:
			oneline.Info = "pin指纹密码同时校验开门"
		case 14:
			oneline.Info = "指纹和刷卡同时或者单pin开门"
		case 15:
			oneline.Info = "人脸开门"
		}
		toweb.ToWebInfoList = append(toweb.ToWebInfoList, oneline)
		toweb.NumOfMatches = len(toweb.ToWebInfoList)
		toweb.TotalMatches = len(toweb.ToWebInfoList)
		toweb.ResponseStatusStrg = "OK"
	}
	return toweb, nil
}

func (z *Zkt) getEventByTime(startTime, endTime string) error {
	stime := strings.Split(startTime, "+")[0]
	etime := strings.Split(endTime, "+")[0]
	zkStartTime := strings.ReplaceAll(stime, "T", " ")
	zkEtartTime := strings.ReplaceAll(etime, "T", " ")

	_, err := oleutil.CallMethod(z.Old, "ReadTimeGLogData", 1, zkStartTime, zkEtartTime)
	if err != nil {
		return err
	}
	return nil
}

var zksyncRet SyncRet

func (z *Zkt) StartSync(sy request.StartSyncReq, doorConf []model.Door) {
	g_syncRet.Percentage = 0
	g_syncRet.Info = "开始同步信息"
	g_syncSucc = true
	var err error
	defer z.disconnect()
	if err := recover(); err != nil {
		fmt.Println("test 发生错误", err)
	}

	//获取人员列表
	wu, err := z.getUserList()
	if err != nil {
		g_syncSucc = false
		g_syncErr.Code = 10003
		g_syncErr.Msg = "获取一体机的人员信息失败"
		g_syncErr.Desc = ""
		return
	}
	time.Sleep(500 * time.Millisecond)
	totalstep := 0
	desnum := len(sy.Destination)
	sourcestep := 0
	donestep := 0
	for _, v := range sy.Source.EmployeeNoList {
		//建立人员信息
		sourcestep += 1
		//下发卡号步骤
		sourcestep += len(v.CardList)
		//下发指纹步骤
		sourcestep += len(v.FingerPrintIDList)
		//下发人脸步骤
		if v.Face {
			sourcestep += 1
		}
	}
	totalstep = desnum * sourcestep

	for _, v := range wu {
		for _, e := range sy.Source.EmployeeNoList {
			if e.EmployeeNo == v.EmployeeNo {
				username := v.Name
				userpwd := v.Password
				emno := v.EmployeeNo
				openDoorWay := v.UserVerifyMode
				downCardinfo := e.CardList
				//待下发指纹数据
				allFinger := []_fingerList{}
				downFinger := []_addFinger{}

				//ablity :=
				srcAbility := z.Ability
				//待下发的指纹数据
				if strings.Contains(srcAbility, "P") && len(e.FingerPrintIDList) > 0 {
					allFinger, err = z.getUserFp(e.EmployeeNo)
					if err != nil {
						g_syncSucc = false
						g_syncErr.Code = 10003
						g_syncErr.Msg = "获取一体机的指纹信息失败"
						fmt.Printf("%s\n", g_syncErr.Msg)
						g_syncErr.Desc = err.Error()
						return
					}
					time.Sleep(500 * time.Millisecond)
					for _, v := range allFinger {
						for _, hf := range e.FingerPrintIDList {
							if v.FingerPrintID == hf {
								a := _addFinger{}
								a.FingerData = v.FingerData
								//待实际下发的时候再进行替换
								a.EmployeeNo = "1"
								a.EnableCardReader = []int{1}
								a.FingerType = v.FingerType
								a.FingerPrintId = v.FingerPrintID
								downFinger = append(downFinger, a)
							}
						}
					}
					if len(downFinger) == 0 {
						g_syncSucc = false
						g_syncErr.Code = 10010
						g_syncErr.Msg = "无效的指纹ID"
						g_syncErr.Desc = ""
						fmt.Printf("%s\n", g_syncErr.Msg)
						return
					}
				}
				//待下发人脸数据
				var faceData []byte
				for {
					if strings.Contains(srcAbility, "F") && e.Face {
						finfo, err := z.getFaceBinData(emno)
						if err != nil {
							g_syncSucc = false
							g_syncErr.Code = 10004
							g_syncErr.Msg = "获取一体机的人脸失败"
							fmt.Printf("%s\n", g_syncErr.Msg)
							g_syncErr.Desc = ""
							return
						}
						if len(faceData) < 100 {
							break
						}
						faceData = finfo
						break
					} else {
						break
					}
				}

				time.Sleep(500 * time.Millisecond)
				//信息获取完成,下发信息至指定的一体机
				for _, dst := range sy.Destination {

					dstDoorInfo := model.DoorInfo{}
					for dstIdx := range doorConf {
						if doorConf[dstIdx].Id == int64(dst) {
							dstDoorInfo = doorConf[dstIdx].DoorInfo
							break
						}
					}

					dstname := dstDoorInfo.Name
					dstip := dstDoorInfo.Ip
					dstport := dstDoorInfo.Port
					//dstabi := DoorCfg[dst].Ability

					dstZk, err := ZkConnect(dstip, dstport)
					dstZk.Ability = dstDoorInfo.Ability
					if err != nil {
						g_syncSucc = false
						g_syncErr.Code = 10004
						g_syncErr.Msg = fmt.Sprintf("连接一体机:%s失败", dstname)
						fmt.Printf("%s\n", g_syncErr.Msg)
						g_syncErr.Desc = ""
						dstZk.disconnect()
						return
					}

					//建立人员信息,存在同名的人员则原地更新,不存在则新建
					emstr := ""
					duinfo, err := dstZk.getUserList()
					if err != nil {
						g_syncSucc = false
						g_syncErr.Code = 10005
						g_syncErr.Msg = fmt.Sprintf("获取%s现有人员数量失败", dstname)
						fmt.Printf("%s\n", g_syncErr.Msg)
						g_syncErr.Desc = ""
						dstZk.disconnect()
						return
					}
					dstZk.disconnect()

					maxeo := 0
					for _, v := range duinfo {
						if v.Name == username {
							emstr = v.EmployeeNo
							break
						} else {
							tempno, err := strconv.Atoi(v.EmployeeNo)
							if err != nil {
								g_syncSucc = false
								g_syncErr.Code = 10005
								g_syncErr.Msg = fmt.Sprintf("获取%s现有人员数量失败", dstname)
								fmt.Printf("%s\n", g_syncErr.Msg)
								g_syncErr.Desc = err.Error()
								//	dstZk.disconnect()
								return
							}
							if tempno >= maxeo {
								maxeo = tempno
							}
						}
					}
					if emstr == "" {
						//最大的工号+1
						emstr = strconv.Itoa(maxeo + 1)
						dstZk, err = ZkConnect(dstip, dstport)
						dstZk.Ability = dstDoorInfo.Ability
						aret := dstZk.addOneUser(emstr, username, userpwd, openDoorWay, v.LocalUIRight)
						if aret != nil {
							fmt.Printf("add user fail,user: %s\n", username)
							g_syncSucc = false
							g_syncErr.Code = 10005
							g_syncErr.Msg = fmt.Sprintf("添加%s至%s失败", username, dstname)
							fmt.Printf("%s\n", g_syncErr.Msg)
							g_syncErr.Desc = ""
							dstZk.disconnect()
							return
						}
					}
					dstZk.disconnect()
					donestep++
					time.Sleep(500 * time.Millisecond)
					g_syncRet.Percentage = 100 * donestep / totalstep
					g_syncRet.Info = fmt.Sprintf("添加%s至%s成功", username, dstname)
					//下发卡号
					if strings.Contains(dstZk.Ability, "C") {
						for _, v := range downCardinfo {
							dstZk, err = ZkConnect(dstip, dstport)
							dstZk.Ability = dstDoorInfo.Ability
							aret := dstZk.addUserCard(emstr, v.CardNo, v.CardType)
							if aret != nil {
								g_syncSucc = false
								g_syncErr.Code = 10005
								g_syncErr.Msg = fmt.Sprintf("添加用户:%s的卡片%s至%s失败", username, v, dstname)
								fmt.Printf("%s\n", g_syncErr.Msg)
								g_syncErr.Desc = ""
								dstZk.disconnect()
								return
							}
							dstZk.disconnect()
							donestep++
							time.Sleep(500 * time.Millisecond)
							g_syncRet.Percentage = 100 * donestep / totalstep
							g_syncRet.Info = fmt.Sprintf("添加用户:%s的卡片%s至%s成功", username, v.CardNo, dstname)
						}
					}
					//下发指纹
					if strings.Contains(dstZk.Ability, "P") {
						for k, _ := range downFinger {
							downFinger[k].EmployeeNo = emstr
							dstZk, err = ZkConnect(dstip, dstport)
							dstZk.Ability = dstDoorInfo.Ability
							aret := dstZk.addUserFp(emstr, downFinger[k].FingerData, downFinger[k].FingerType, downFinger[k].FingerPrintId)
							if aret != nil {
								g_syncSucc = false
								g_syncErr.Code = 10005
								g_syncErr.Msg = fmt.Sprintf("添加%s的指纹:%d至%s失败", username, downFinger[k].FingerPrintId, dstname)
								fmt.Printf("%s\n", g_syncErr.Msg)
								g_syncErr.Desc = ""
								dstZk.disconnect()
								return
							}
							dstZk.disconnect()
							donestep++
							time.Sleep(500 * time.Millisecond)
							g_syncRet.Percentage = 100 * donestep / totalstep
							g_syncRet.Info = fmt.Sprintf("添加用户:%s的指纹%d至%s成功", username, downFinger[k].FingerPrintId, dstname)
						}
					} else {
						donestep += len(downFinger)
						g_syncRet.Percentage = 100 * donestep / totalstep
					}
					//下发人脸
					if strings.Contains(dstZk.Ability, "F") && len(faceData) > 0 {
						var param interface{}
						dstZk, err = ZkConnect(dstip, dstport)
						dstZk.Ability = dstDoorInfo.Ability
						err = dstZk.addUserFace(emstr, faceData, param)
						if err != nil {
							g_syncSucc = false
							g_syncErr.Code = 10005
							g_syncErr.Msg = fmt.Sprintf("添加%s的人脸至%s失败", username, dstname)
							fmt.Printf("%s\n", g_syncErr.Msg)
							g_syncErr.Desc = err.Error()
							dstZk.disconnect()
							return
						}
						dstZk.disconnect()
					} else {
						if e.Face {
							donestep++
							g_syncRet.Percentage = 100 * donestep / totalstep
						}
					}
					time.Sleep(500 * time.Millisecond)
				}
			}
		}
	}
	fmt.Printf("add success")
	g_syncRet.Percentage = 100
	g_syncRet.Info = "成功"
}
