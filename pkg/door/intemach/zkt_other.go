//go:build !windows

package intemach

import (
	"errors"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"
)

type Zkt struct {
	Ability string
}

var notsupport = errors.New("平台不支持")

func ZkConnect(ip string, port int) (*Zkt, error) {
	return nil, notsupport
}
func (z *Zkt) GetManuFactName() string {
	return ""
}
func (z *Zkt) GetDoorStatus() (int, error) { return -1, notsupport }

// //控制门
func (z *Zkt) CtlDoorStatus(way string) error { return notsupport }

// //获取人员列表
func (z *Zkt) GetUserList() ([]ToWebUserInfo, error) { return nil, notsupport }

// //添加人员
func (z *Zkt) AddOneUser(emStr, name, pwd, UserVerifyMode string, localUi bool) error {
	return notsupport
}

// //删除人员
func (z *Zkt) DelOneUser(emStr []string) error { return notsupport }

// //修改人员信息
func (z *Zkt) ChangeOneUser(emStr, name, pwd, UserVerifyMode string, localUi bool) error {
	return notsupport
}

// //获取指纹
func (z *Zkt) GetUserFp(emStr string) ([]_fingerList, error) { return nil, notsupport }

// //增加指纹
func (z *Zkt) AddUserFp(emStr, fpData, ftype string, fid int) error { return notsupport }

// //删除指纹
func (z *Zkt) DelUserFp(emStr string, fid []int) error { return notsupport }

// //获取增加指纹进度
func (z *Zkt) GetAddFpProgress() (int, error) { return -1, notsupport }

// //获取删除指纹进度
func (z *Zkt) GetDelFpProgress() (string, error) { return "", notsupport }

// //获取人脸
func (z *Zkt) GetUserFace(emStr string) (*toWebFaceInfo, error) {
	return nil, notsupport
}

// //删除人脸
func (z *Zkt) DelUserFace(emStr string) error { return notsupport }

// //增加人脸
func (z *Zkt) AddUserFace(emStr string, facedata []byte, params interface{}) error { return notsupport }

// //获取指定人员卡号
func (z *Zkt) GetUserCard(emStr string) ([]ToWebGetCardInfo, error) { return nil, notsupport }

// //添加卡号
func (z *Zkt) AddUserCard(emStr, cardNoStr, cardType string) error { return notsupport }

// //删除卡号
func (z *Zkt) DelUserCard(delList []DelCardByNoParam) error { return notsupport }

// //获取历史记录
func (z *Zkt) GetEventRecord(id int, pos int, startTime string, endTime string) (*ToWebRecord, error) {
	return nil, notsupport
}

// 开始同步
func (z *Zkt) StartSync(sy request.StartSyncReq, doorConf []model.Door) {}
