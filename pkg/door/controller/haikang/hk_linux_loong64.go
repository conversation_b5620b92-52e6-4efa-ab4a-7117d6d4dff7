//go:build linux && loong64

package controller

import (
	"sync"
	. "tw_platform/pkg/door/controller/comm"
)

type hkParam struct {
	Ip      string
	Pwd     string
	DoorNum int
}
type cachedHkinfo map[int32]hkParam

var g_hkCached cachedHkinfo = cachedHkinfo{}
var cacheHkLock sync.Mutex

func Connect(ip, name, pwd string, doorNum int) *hk {
	return nil
}
func DisConnect(uid int32) {
}

type hk struct {
	uid     int32
	DoorNum int
}

// //门禁控制器远程开门
func (h *hk) CtrDoor(doorid int, way string) error {
	return nil
}

// 门禁控制器获取历史记录
func (h *hk) GetDoorcontrollerRecord(stime, etime int64) (*HisRecord, error) {
	return nil, nil
}

//type _CardList struct {
//	Cardlist []Cardlist `json:"cardlist"`
//}
//type Cardlist struct {
//	Cardno string `json:"cardNo"`
//	Door   []int  `json:"door"`
//}

// 门禁控制器获取卡列表
func (h *hk) GetCardList() (*DoorList, error) {
	return nil, nil
}

// //门禁控制器添加卡
func (h *hk) AddCard(cardNo string, doorids []int) error {
	return nil
}

// //门禁控制器删除卡
func (h *hk) DelCard(cardNo string) error {
	return nil
}

// //获取门的状态
func (h *hk) GetDoorStatus() (*DoorStatus, error) {
	return nil, nil
}

func InitDoorCfg() {

}
func init() {
}
