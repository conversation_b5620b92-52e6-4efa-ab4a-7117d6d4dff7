//go:build !amd64 && !loong64

package controller

import (
	"fmt"
	"sync"
	"time"
	. "tw_platform/pkg/door/controller/comm"
	"tw_platform/pkg/door/controller/haikang/netsdk"
	"unsafe"
)

type hkParam struct {
	Ip      string
	Pwd     string
	DoorNum int
}
type cachedHkinfo map[int32]hkParam

var g_hkCached cachedHkinfo = cachedHkinfo{}
var cacheHkLock sync.Mutex

func Connect(ip, name, pwd string, doorNum int) *hk {
	cacheHkLock.Lock()
	//查找缓存
	var delid int32 = -123456789
	for uid, param := range g_hkCached {
		if param.Ip == ip && param.Pwd == pwd && param.DoorNum == doorNum {
			cacheHkLock.Unlock()
			return &hk{
				uid: uid,
			}
		} else if param.Ip == ip {
			delid = uid
			break
		}
	}
	if delid != -123456789 {
		delete(g_hkCached, delid)
		DisConnect(delid)
	}

	userID, err := netsdk.NetLoginV40(ip, name, pwd)
	if err != nil {
		fmt.Println(err)
		cacheHkLock.Unlock()
		return nil
	}
	g_hkCached[userID] = hkParam{
		Ip:      ip,
		Pwd:     pwd,
		DoorNum: doorNum,
	}
	cacheHkLock.Unlock()
	return &hk{
		uid: userID,
	}
}
func DisConnect(uid int32) {
	netsdk.NetLogout(uid)
}

type hk struct {
	uid     int32
	DoorNum int
}

// //门禁控制器远程开门
func (h *hk) CtrDoor(doorid int, way string) error {

	ctrl := 0xff
	switch way {
	case "close":
		ctrl = 0
	case "open":
		ctrl = 1
	case "alwaysOpen":
		ctrl = 2
	}
	err := netsdk.ControlDoor(h.uid, int32(doorid), uint32(ctrl))
	if err != nil {
		return err
	}
	return nil
}

// 门禁控制器获取历史记录
func (h *hk) GetDoorcontrollerRecord(stime, etime int64) (*HisRecord, error) {
	startYear := time.Unix(stime, 0).Year()
	startMonth := int(time.Unix(stime, 0).Month())
	startDay := time.Unix(stime, 0).Day()
	startHour := time.Unix(stime, 0).Hour()
	startMinute := time.Unix(stime, 0).Minute()
	startSecond := time.Unix(stime, 0).Second()
	endYear := time.Unix(etime, 0).Year()
	endMonth := int(time.Unix(etime, 0).Month())
	endDay := time.Unix(etime, 0).Day()
	endHour := time.Unix(etime, 0).Hour()
	endMinute := time.Unix(etime, 0).Minute()
	endSecond := time.Unix(etime, 0).Second()
	var struStartTime, struEndTime netsdk.NET_DVR_TIME
	struStartTime.SetTime(startYear, startMonth, startDay, startHour, startMinute, startSecond)
	struEndTime.SetTime(endYear, endMonth, endDay, endHour, endMinute, endSecond)
	// fmt.Println(struStartTime)
	// fmt.Println(struEndTime)
	err, his := netsdk.GetEventInfo(h.uid, struStartTime, struEndTime)
	if err != nil {
		return nil, err
	}
	var history = &HisRecord{
		Responsestatusstrg: "ok",
		InfoList:           []LogInfo{},
	}
	for i := range his {
		history.InfoList = append(history.InfoList, LogInfo{
			Info:   his[i].Info,
			Time:   his[i].Time,
			CardNo: his[i].Cardno,
			DoorId: his[i].Doorid,
		})
	}
	return history, nil
}

//type _CardList struct {
//	Cardlist []Cardlist `json:"cardlist"`
//}
//type Cardlist struct {
//	Cardno string `json:"cardNo"`
//	Door   []int  `json:"door"`
//}

// 门禁控制器获取卡列表
func (h *hk) GetCardList() (*DoorList, error) {

	clist := &DoorList{
		CardList: []CardDoorId{},
	}
	cardInfo, err := netsdk.GetCardInfo(h.uid)
	if err != nil {
		return nil, err
	}
	for k, v := range cardInfo {
		// fmt.Printf("CardNo: %s | UserName : %s \n", k, v)
		var p []int
		p = append(p, int(v[0]))
		p = append(p, int(v[1]))
		clist.CardList = append(clist.CardList, CardDoorId{k, p})
	}
	return clist, nil
}

// //门禁控制器添加卡
func (h *hk) AddCard(cardNo string, doorids []int) error {

	err := netsdk.SetOneCard(h.uid, cardNo, doorids)
	if err != nil {
		return err
	}
	return nil
}

// //门禁控制器删除卡
func (h *hk) DelCard(cardNo string) error {

	err := netsdk.DeleteAllCard(h.uid, cardNo)
	if err != nil {
		return err
	}
	return nil
}

// //获取门的状态
func (h *hk) GetDoorStatus() (*DoorStatus, error) {

	var status netsdk.NET_DVR_ACS_WORK_STATUS
	err := netsdk.GetDoorStatus(h.uid, unsafe.Pointer(&status))
	if err != nil {
		return nil, err
	}
	// fmt.Println(status.ST_byMagneticStatus)
	var s DoorStatus
	for i := 0; i < h.DoorNum; i++ {
		s.DoorStatus = append(s.DoorStatus, int(status.ST_byMagneticStatus[i]))
	}
	return &s, nil
}

func InitDoorCfg() {
	err := netsdk.NetInit("./hiknetllog") //日志路径
	if err != nil {
		panic("初始化海康门控器日志路径失败")
	}

}
func init() {
	InitDoorCfg()
}
