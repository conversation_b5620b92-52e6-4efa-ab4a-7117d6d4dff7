//go:build !amd64 && !loong64

package netsdk

/*
#cgo CFLAGS:  -I./include
#cgo LDFLAGS: -L./lib/arm_linux -lhcnetsdk
// #cgo LDFLAGS: -Wl,-rpath=./lib/Linux:./lib/Linux/HCNetSDK
#cgo LDFLAGS: -Wl,--allow-multiple-definition

#include "HCNetSDK.h"
#include <unistd.h>
#include <string.h>
#include <stdlib.h>
#include <stdio.h>
*/
import "C"
import (
	"errors"
	"fmt"
	"log"
	"time"
	"unsafe"
)

// IF_fMEssCallBack 回调函数结构体
type IF_fMEssCallBack interface {
	Invoke(lCommand int, ip string, pBuf unsafe.Pointer, dwBufLen int) bool
}

// NetInit 初始化设备和日志
func NetInit(sdkLog string) error {

	//初始化资源
	ret := C.NET_DVR_Init()
	if int(ret) != 1 {
		fmt.Printf("NET_DVR_Init failed,error code = %v\n", C.NET_DVR_GetLastError())
		return errors.New(fmt.Sprintf("NET_DVR_Init failed,error code = %v\n", C.NET_DVR_GetLastError()))
	}
	// C.NET_DVR_SetLogToFile(3, C.CString(sdkLog), 1)

	return nil
}

// NetLoginV40 登录
func NetLoginV40(deviceIp, username, password string) (int32, error) {
	var userLoginInfo C.NET_DVR_USER_LOGIN_INFO
	var deviceInfo C.NET_DVR_DEVICEINFO_V40

	userLoginInfo.wPort = 8000 // your device port,default 8000

	pUsername := C.CBytes([]byte(username))
	defer C.free(pUsername)
	// 使用memcpy函数进行拷贝
	C.memcpy(unsafe.Pointer(&userLoginInfo.sUserName), pUsername, C.ulong(len(username)))

	pPassword := C.CBytes([]byte(password))
	defer C.free(pPassword)
	C.memcpy(unsafe.Pointer(&userLoginInfo.sPassword), pPassword, C.ulong(len(password)))

	pDeviceIp := C.CBytes([]byte(deviceIp))
	defer C.free(pDeviceIp)
	C.memcpy(unsafe.Pointer(&userLoginInfo.sDeviceAddress), pDeviceIp, C.ulong(len(deviceIp)))

	// 调用登录接口
	uid := C.NET_DVR_Login_V40((C.LPNET_DVR_USER_LOGIN_INFO)(&userLoginInfo), (C.LPNET_DVR_DEVICEINFO_V40)(&deviceInfo))

	if int32(uid) < 0 {
		if err := isErr("Login"); err != nil {
			return -1, errors.New(fmt.Sprintf("ip: %s 登录失败,原因%v", deviceIp, err.Error()))
		}
		return -1, errors.New(fmt.Sprintf("ip: %s 登录失败", deviceIp))
	}
	return int32(uid), nil
}

// isErr  获取上一个发生的错误
func isErr(operation string) error {
	errno := int64(C.NET_DVR_GetLastError())
	if errno > 0 {
		reMsg := fmt.Sprintf("%s失败,失败代码号：%d", operation, errno)
		return errors.New(reMsg)
	}
	return nil
}

// NetLogout 退出登录
func NetLogout(uid int32) error {
	C.NET_DVR_Logout_V30(C.LONG(uid))
	if err := isErr("Logout"); err != nil {
		return err
	}
	return nil
}

// NetCleanup 释放SDK资源，在程序结束之前调用。
func NetCleanup() {
	C.NET_DVR_Cleanup()
}

// GetDoorStatus  获取门状态
func GetDoorStatus(uid int32, pBuf unsafe.Pointer) (err error) {
	defer func() {
		if e := recover(); e != nil {
			log.Println("GetDoorStatus panic : ", e)
		}
	}()
	var lpBytesReturned C.DWORD
	var PBuf = (C.LPVOID)(pBuf)
	ret := C.NET_DVR_GetDVRConfig(C.LONG(uid), C.NET_DVR_GET_ACS_WORK_STATUS, C.LONG(0xFFFFFFF), PBuf, (C.DWORD)(unsafe.Sizeof(NET_DVR_ACS_WORK_STATUS{})), (C.LPDWORD)(&lpBytesReturned))

	if int32(ret) == 0 {
		if err = isErr("Get door status"); err != nil {
			return errors.New(fmt.Sprintf("uid:[%d] 失败,原因%v", uid, err.Error()))
		}
		return errors.New(fmt.Sprintf("get door status error，uid :%d", uid))
	}

	return nil
}

// ControlDoor 控制门状态 0- 关闭，1- 打开），2- 常开），3- 常关，
func ControlDoor(uid int32, DoorIndex int32, ctrl uint32) error {
	defer func() {
		if e := recover(); e != nil {
			log.Println("ControlDoor panic : ", e)
		}
	}()
	ret := C.NET_DVR_ControlGateway(C.LONG(uid), C.LONG(DoorIndex), C.DWORD(ctrl))
	if int32(ret) == 0 {
		if err := isErr("Control door"); err != nil {
			return errors.New(fmt.Sprintf("uid:[%d] 失败,原因%v", uid, err.Error()))
		}
		return errors.New(fmt.Sprintf("Control door error，uid :%d", uid))
	}
	return nil
}

// GetCardInfo 获取门禁设备下的卡信息
func GetCardInfo(uid int32) (cardInfo map[string][256]byte, err error) {
	// defer func() {
	// 	if e := recover(); e != nil {
	// 		log.Println("GetCardInfo panic : ", e)
	// 	}
	// }()
	var getcard NET_DVR_CARD_COND
	getcard.ST_dwSize = DWORD(unsafe.Sizeof(getcard))
	getcard.ST_dwCardNum = 0xffffffff
	cardInfo = make(map[string][256]byte)

	var cardStruct NET_DVR_CARD_RECORD
	cardStruct.dwSize = DWORD(unsafe.Sizeof(cardStruct))
	//var userDate byte
	//建立长连接
	ret1 := C.NET_DVR_StartRemoteConfig(C.LONG(uid), C.NET_DVR_GET_CARD, (C.LPVOID)(unsafe.Pointer(&getcard)), C.DWORD(unsafe.Sizeof(getcard)), nil, nil)
	// fmt.Println("ret1=", ret1)
	if int32(ret1) < 0 {
		if err := isErr("StartRemoteConfig"); err != nil {
			return cardInfo, errors.New(fmt.Sprintf("uid:[%d] 失败,原因%v", uid, err.Error()))
		}
		return cardInfo, errors.New(fmt.Sprintf("GetCard Info error，uid :%d", uid))
	}

	//循环获取卡数据
	for {
		ret2 := int32(C.NET_DVR_GetNextRemoteConfig(ret1, unsafe.Pointer(&cardStruct), C.DWORD(unsafe.Sizeof(cardStruct))))
		if int64(ret2) < 0 {
			if err := isErr("GetNextRemoteConfig1"); err != nil {
				return cardInfo, errors.New(fmt.Sprintf("uid:[%d] 失败,原因%v", uid, err.Error()))
			}
			return cardInfo, errors.New(fmt.Sprintf("GetCard Info error，uid :%d", uid))
		}
		if ret2 == 1000 {
			for k, v := range cardStruct.byCardNo {
				if v == 0 {
					cardInfo[string(cardStruct.byCardNo[:k])] = cardStruct.byDoorRight
					break
				}
			}
			// fmt.Println("get card info :cardNo:", string(cardStruct.byCardNo[:]), "|name : ", string(cardStruct.byName[:]), "|dwEmployeeNo", cardStruct.dwEmployeeNo)
			continue
		}
		if ret2 == 1001 {
			time.Sleep(1 * time.Microsecond)
			continue
		}
		if ret2 == 1002 {
			ret3 := C.NET_DVR_StopRemoteConfig(ret1)
			if int32(ret3) == 0 {
				if err := isErr("GetNextRemoteConfig"); err != nil {
					return cardInfo, errors.New(fmt.Sprintf("uid:[%d] 失败,原因%v", uid, err.Error()))
				}
				return cardInfo, errors.New(fmt.Sprintf("Control door error，uid :%d", uid))
			}
			return cardInfo, nil
		}
		if ret2 == 1003 {
			ret3 := C.NET_DVR_StopRemoteConfig(ret1)
			if int32(ret3) == 0 {
				if err := isErr("GetNextRemoteConfig"); err != nil {
					return cardInfo, errors.New(fmt.Sprintf("uid:[%d] 失败,原因%v", uid, err.Error()))
				}
				return cardInfo, errors.New(fmt.Sprintf("Control door error，uid :%d", uid))
			}
			return cardInfo, nil
		}

	}

}

// SetOneCard 设置卡参数
func SetOneCard(uid int32, strCardNO string, door []int) error {
	var setcard NET_DVR_CARD_COND
	setcard.ST_dwSize = DWORD(unsafe.Sizeof(setcard))
	setcard.ST_dwCardNum = 0x1

	//建立长连接
	ret1 := C.NET_DVR_StartRemoteConfig(C.LONG(uid), C.NET_DVR_SET_CARD, (C.LPVOID)(unsafe.Pointer(&setcard)), C.DWORD(unsafe.Sizeof(setcard)), nil, nil)
	// fmt.Println("ret1=", ret1)
	if int32(ret1) < 0 {
		if err := isErr("StartRemoteConfig"); err != nil {
			return errors.New(fmt.Sprintf("uid:[%d] 失败,原因%v", uid, err.Error()))
		}
		return errors.New(fmt.Sprintf("SetCard Info error，uid :%d", uid))
	}
	var struCardRecord NET_DVR_CARD_RECORD
	struCardRecord.dwSize = DWORD(unsafe.Sizeof(struCardRecord))
	for i := 0; i < 32; i++ {
		struCardRecord.byCardNo[i] = 0
	}
	for i := 0; i < len(strCardNO); i++ {
		struCardRecord.byCardNo[i] = strCardNO[i]
	}
	struCardRecord.byCardType = 1   //普通卡
	struCardRecord.byLeaderCard = 0 //是否为首卡，0-否，1-�?
	struCardRecord.byUserType = 0
	for i, v := range door {
		struCardRecord.byDoorRight[i] = byte(v)
	}
	// struCardRecord.byDoorRight[0] = byte(door[0])
	// struCardRecord.byDoorRight[1] = byte(door[1])
	// struCardRecord.byDoorRight[0] = 3 //哪个门有权限
	struCardRecord.dwEmployeeNo = 1       //工号
	struCardRecord.struValid.byEnable = 1 //卡有效期使能，下面是卡有效期从2000-1-1 11:11:11到2030-1-1 11:11:11
	struCardRecord.struValid.byBeginTimeFlag = 1
	struCardRecord.struValid.byEnableTimeFlag = 1
	struCardRecord.struValid.struBeginTime.ST_wYear = 2020
	struCardRecord.struValid.struBeginTime.ST_byMonth = 1
	struCardRecord.struValid.struBeginTime.ST_byDay = 1

	struCardRecord.struValid.struEndTime.ST_wYear = 2037
	struCardRecord.struValid.struEndTime.ST_byMonth = 1
	struCardRecord.struValid.struEndTime.ST_byDay = 1
	struCardRecord.wCardRightPlan[0] = 1 //卡计划模板1有效
	struCardRecord.dwEmployeeNo = 0      //工号
	struCardRecord.byBelongGroup[0] = 1  //所属群组，按字节表示，1-属于，0-不属于，从低位到高位表示是否从属群组1~N
	var struCardStatus NET_DVR_CARD_STATUS
	struCardStatus.dwSize = DWORD(unsafe.Sizeof(struCardStatus))
	var pInt DWORD
	//添加卡
	for {
		ret2 := int32(C.NET_DVR_SendWithRecvRemoteConfig(ret1, unsafe.Pointer(&struCardRecord), C.DWORD(unsafe.Sizeof(struCardRecord)), unsafe.Pointer(&struCardStatus), C.DWORD(struCardStatus.dwSize), (*C.uint)(unsafe.Pointer(&pInt))))
		// fmt.Println("ret2=", ret2)
		if int64(ret2) < 0 {
			if err := isErr("SendWithRecvRemoteConfig"); err != nil {
				return errors.New(fmt.Sprintf("uid:[%d] 失败,原因%v", uid, err.Error()))
			}
			return errors.New(fmt.Sprintf("SetCard Info error，uid :%d", uid))
		}
		if ret2 == 1000 {
			time.Sleep(1 * time.Microsecond)
			continue
		}
		if ret2 == 1001 {
			time.Sleep(1 * time.Microsecond)
			continue
		}
		if ret2 == 1002 {
			ret3 := C.NET_DVR_StopRemoteConfig(ret1)
			if int32(ret3) == 0 {
				if err := isErr("SendWithRecvRemoteConfig"); err != nil {
					return errors.New(fmt.Sprintf("uid:[%d] 失败,原因%v", uid, err.Error()))
				}
				return errors.New(fmt.Sprintf("NET_DVR_StopRemoteConfig error，uid :%d", uid))
			}
			return nil
		}
		if ret2 == 1003 {
			ret3 := C.NET_DVR_StopRemoteConfig(ret1)
			if int32(ret3) == 0 {
				if err := isErr("SendWithRecvRemoteConfig"); err != nil {
					return errors.New(fmt.Sprintf("uid:[%d] 失败,原因%v", uid, err.Error()))
				}
				return errors.New(fmt.Sprintf("NET_DVR_StopRemoteConfig error，uid :%d", uid))
			}
			return nil
		}

	}
	return nil
}

// DeleteAllCard 删除卡参数
func DeleteAllCard(uid int32, strCardNO string) error {
	var deletecard NET_DVR_CARD_COND
	deletecard.ST_dwSize = DWORD(unsafe.Sizeof(deletecard))
	deletecard.ST_dwCardNum = 0x1

	//建立长连接
	ret1 := C.NET_DVR_StartRemoteConfig(C.LONG(uid), C.NET_DVR_DEL_CARD, (C.LPVOID)(unsafe.Pointer(&deletecard)), C.DWORD(unsafe.Sizeof(deletecard)), nil, nil)
	// fmt.Println("ret1=", ret1)
	if int32(ret1) < 0 {
		if err := isErr("StartRemoteConfig"); err != nil {
			return errors.New(fmt.Sprintf("uid:[%d] 失败,原因%v", uid, err.Error()))
		}
		return errors.New(fmt.Sprintf("SetCard Info error，uid :%d", uid))
	}
	var struCardRecord NET_DVR_CARD_SEND_DATA
	struCardRecord.dwSize = DWORD(unsafe.Sizeof(struCardRecord))
	for i := 0; i < 32; i++ {
		struCardRecord.byCardNo[i] = 0
	}
	for i := 0; i < len(strCardNO); i++ {
		struCardRecord.byCardNo[i] = strCardNO[i]
	}
	var struCardStatus NET_DVR_CARD_STATUS
	struCardStatus.dwSize = DWORD(unsafe.Sizeof(struCardStatus))
	var pInt DWORD
	//删除卡
	for {
		ret2 := int32(C.NET_DVR_SendWithRecvRemoteConfig(ret1, unsafe.Pointer(&struCardRecord), C.DWORD(unsafe.Sizeof(struCardRecord)), unsafe.Pointer(&struCardStatus), C.DWORD(struCardStatus.dwSize), (*C.uint)(unsafe.Pointer(&pInt))))
		// fmt.Println("ret2=", ret2)
		if int64(ret2) < 0 {
			if err := isErr("SendWithRecvRemoteConfig"); err != nil {
				return errors.New(fmt.Sprintf("uid:[%d] 失败,原因%v", uid, err.Error()))
			}
			return errors.New(fmt.Sprintf("DeleteCard Info error，uid :%d", uid))
		}
		if ret2 == 1000 {
			time.Sleep(1 * time.Microsecond)
			continue
		}
		if ret2 == 1001 {
			time.Sleep(1 * time.Microsecond)
			continue
		}
		if ret2 == 1002 {
			ret3 := C.NET_DVR_StopRemoteConfig(ret1)
			if int32(ret3) == 0 {
				if err := isErr("SendWithRecvRemoteConfig"); err != nil {
					return errors.New(fmt.Sprintf("uid:[%d] 失败,原因%v", uid, err.Error()))
				}
				return errors.New(fmt.Sprintf("NET_DVR_StopRemoteConfig error，uid :%d", uid))
			}
			return nil
		}
		if ret2 == 1003 {
			ret3 := C.NET_DVR_StopRemoteConfig(ret1)
			if int32(ret3) == 0 {
				if err := isErr("SendWithRecvRemoteConfig"); err != nil {
					return errors.New(fmt.Sprintf("uid:[%d] 失败,原因%v", uid, err.Error()))
				}
				return errors.New(fmt.Sprintf("NET_DVR_StopRemoteConfig error，uid :%d", uid))
			}
			return nil
		}

	}
	return nil
}

// GetEventInfo 主动获取报警信息
func GetEventInfo(uid int32, struStartTime, struEndTime NET_DVR_TIME) (error, []Infolist) {
	var history []Infolist
	var struEventCond NET_DVR_ACS_EVENT_COND
	struEventCond.dwSize = DWORD(unsafe.Sizeof(struEventCond))
	struEventCond.dwMajor = 0x0 //5表示查询事件，0表示查询全部
	struEventCond.dwMajor = 0   //查询全部
	//查询开始时间
	struEventCond.struStartTime.dwYear = struStartTime.dwYear
	struEventCond.struStartTime.dwMonth = struStartTime.dwMonth
	struEventCond.struStartTime.dwDay = struStartTime.dwDay
	struEventCond.struStartTime.dwHour = struStartTime.dwHour
	struEventCond.struStartTime.dwMinute = struStartTime.dwMinute
	struEventCond.struStartTime.dwSecond = struStartTime.dwSecond

	//查询结束时间
	struEventCond.struEndTime.dwYear = struEndTime.dwYear
	struEventCond.struEndTime.dwMonth = struEndTime.dwMonth
	struEventCond.struEndTime.dwDay = struEndTime.dwDay
	struEventCond.struEndTime.dwHour = struEndTime.dwHour
	struEventCond.struEndTime.dwMinute = struEndTime.dwMinute
	struEventCond.struEndTime.dwSecond = struEndTime.dwSecond
	struEventCond.byTimeType = 0 //时间类型：0-设备本地时间（默认），1-UTC时间（struStartTime和struEndTime的时间）

	struEventCond.byPicEnable = 0 //是否带图片，0-不带图片，1-带图片
	//建立长连接
	ret1 := C.NET_DVR_StartRemoteConfig(C.LONG(uid), C.NET_DVR_GET_ACS_EVENT, (C.LPVOID)(unsafe.Pointer(&struEventCond)), C.DWORD(unsafe.Sizeof(struEventCond)), nil, nil)
	// fmt.Println("ret1=", ret1)
	if int32(ret1) < 0 {
		if err := isErr("StartRemoteConfig"); err != nil {
			return errors.New(fmt.Sprintf("uid:[%d] 失败,原因%v", uid, err.Error())), nil
		}
		return errors.New(fmt.Sprintf("GetEventInfo Info error，uid :%d", uid)), nil
	}
	var struEventCfg NET_DVR_ACS_EVENT_CFG
	struEventCfg.dwSize = DWORD(unsafe.Sizeof(struEventCfg))
	//循环获取事件
	for {
		ret2 := int32(C.NET_DVR_GetNextRemoteConfig(ret1, unsafe.Pointer(&struEventCfg), C.DWORD(unsafe.Sizeof(struEventCfg))))
		// fmt.Println("ret2=", ret2)
		//fmt.Println(*(*NET_DVR_CARD_RECORD)(unsafe.Pointer(&CardStruct)))
		//fmt.Println(unsafe.Sizeof(NET_DVR_CARD_RECORD{}))
		//fmt.Println("NET_DVR_GetNextRemoteConfig 返回结果",ret2)
		if int64(ret2) < 0 {
			if err := isErr("GetNextRemoteConfig1"); err != nil {
				return errors.New(fmt.Sprintf("uid:[%d] 失败,原因%v", uid, err.Error())), nil
			}
			return errors.New(fmt.Sprintf("GetCard Info error，uid :%d", uid)), nil
		}
		if ret2 == 1000 {

			// fmt.Println(struEventCfg.struTime)
			// fmt.Println(minorString[struEventCfg.dwMajor][struEventCfg.dwMinor])
			// fmt.Println(string(struEventCfg.struAcsEventInfo.byCardNo[:]))
			// fmt.Println(struEventCfg.struAcsEventInfo.dwDoorNo)
			var his Infolist
			his.Info = minorString[struEventCfg.dwMajor][struEventCfg.dwMinor]

			his.Time = fmt.Sprintf("%d-%02d-%02d %02d:%02d:%02d", struEventCfg.struTime.dwYear, struEventCfg.struTime.dwMonth, struEventCfg.struTime.dwDay, struEventCfg.struTime.dwHour, struEventCfg.struTime.dwMinute, struEventCfg.struTime.dwSecond)
			for k, v := range struEventCfg.struAcsEventInfo.byCardNo {
				if v == 0 {
					his.Cardno = string(struEventCfg.struAcsEventInfo.byCardNo[:k])
					break
				}
			}

			his.Doorid = int(struEventCfg.struAcsEventInfo.dwDoorNo)
			history = append(history, his)
			time.Sleep(1 * time.Microsecond)
			continue
		}
		if ret2 == 1001 {
			time.Sleep(1 * time.Microsecond)
			continue
		}
		if ret2 == 1002 {
			ret3 := C.NET_DVR_StopRemoteConfig(ret1)
			if int32(ret3) == 0 {
				if err := isErr("GetNextRemoteConfig"); err != nil {
					return errors.New(fmt.Sprintf("uid:[%d] 失败,原因%v", uid, err.Error())), nil
				}
				return errors.New(fmt.Sprintf("Control door error，uid :%d", uid)), nil
			}
			return nil, history
		}
		if ret2 == 1003 {
			ret3 := C.NET_DVR_StopRemoteConfig(ret1)
			if int32(ret3) == 0 {
				if err := isErr("GetNextRemoteConfig"); err != nil {
					return errors.New(fmt.Sprintf("uid:[%d] 失败,原因%v", uid, err.Error())), nil
				}
				return errors.New(fmt.Sprintf("Control door error，uid :%d", uid)), nil
			}
			return nil, history
		}

	}
}

func (d *NET_DVR_TIME) SetTime(year, month, day, hour, minute, second int) {
	d.dwYear = DWORD(year)
	d.dwMonth = DWORD(month)
	d.dwDay = DWORD(day)
	d.dwHour = DWORD(hour)
	d.dwMinute = DWORD(minute)
	d.dwSecond = DWORD(second)
}
