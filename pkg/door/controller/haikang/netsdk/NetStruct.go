package netsdk

import "unsafe"

type LONG int32
type LLONG int64
type DWORD uint32
type WORD uint16
type BYTE byte

// type byte byte
type BOOL int32

const (
	ACS_CARD_NO_LEN                = 32
	NAME_LEN                       = 32
	NET_SDK_MONITOR_ID_LEN         = 64
	NET_SDK_EMPLOYEE_NO_LEN        = 32
	NET_DVR_DEV_ADDRESS_MAX_LEN    = 129
	NET_DVR_LOGIN_USERNAME_MAX_LEN = 64
	NET_DVR_LOGIN_PASSWD_MAX_LEN   = 64
	NET_DVR_GET_CARD               = 2560
	NET_DVR_SET_CARD               = 2561
	NET_DVR_DEL_CARD               = 2562
	NET_DVR_GET_ACS_WORK_STATUS    = 2123
	NET_DVR_GET_ACS_EVENT          = 2514
	MAX_NAMELEN                    = 16
	MACADDR_LEN                    = 6
)

type NET_DVR_CARD_SEND_DATA struct {
	dwSize   DWORD
	byCardNo [32]byte
	byRes    [16]byte
}

//校时结构参数
type NET_DVR_TIME struct {
	dwYear   DWORD //年
	dwMonth  DWORD //月
	dwDay    DWORD //日
	dwHour   DWORD //时
	dwMinute DWORD //分
	dwSecond DWORD //秒
}

type NET_DVR_ACS_EVENT_COND struct {
	dwSize              DWORD
	dwMajor             DWORD                                //报警主类型，参考事件上传宏定义，0-全部
	dwMinor             DWORD                                //报警次类型，参考事件上传宏定义，0-全部
	struStartTime       NET_DVR_TIME                         //开始时间
	struEndTime         NET_DVR_TIME                         //结束时间
	byCardNo            [ACS_CARD_NO_LEN]BYTE                //卡号
	byName              [NAME_LEN]BYTE                       //持卡人姓名
	byPicEnable         BYTE                                 //是否带图片，0-不带图片，1-带图片
	byTimeType          BYTE                                 //时间类型：0-设备本地时间（默认），1-UTC时间（struStartTime和struEndTime的时间）
	byRes2              [2]BYTE                              //保留
	dwBeginSerialNo     DWORD                                //起始流水号（为0时默认全部）
	dwEndSerialNo       DWORD                                //结束流水号（为0时默认全部）
	dwIOTChannelNo      DWORD                                //IOT通道号，0-无效
	wInductiveEventType WORD                                 //归纳事件类型，0-无效，其他值参见2.2章节，客户端判断该值为非0值后，报警类型通过归纳事件类型区分，否则通过原有报警主次类型（dwMajor、dwMinor）区分
	bySearchType        BYTE                                 //搜索方式：0-保留，1-按事件源搜索（此时通道号为非视频通道号），2-按监控点ID搜索
	byEventAttribute    BYTE                                 //事件属性：0-未定义，1-合法事件，2-其它
	szMonitorID         [NET_SDK_MONITOR_ID_LEN] /*64*/ BYTE //监控点ID（由设备序列号、通道类型、编号组成，例如门禁点：设备序列号+“DOOR”+门编号）
	byEmployeeNo        [NET_SDK_EMPLOYEE_NO_LEN]BYTE        //工号（人员ID）
	byRes               [140]BYTE                            //保留
}
type NET_DVR_IPADDR struct {
	sIpV4  [16]BYTE /* IPv4地址 */
	byIPv6 [128]BYTE
}
type NET_VCA_POINT struct {
	fx float32
	fy float32
}
type NET_DVR_ACS_EVENT_DETAIL struct {
	dwSize                         DWORD
	byCardNo                       [ACS_CARD_NO_LEN]byte //卡号（mac地址），为0无效
	byCardType                     BYTE                  //卡类型，1-普通卡，2-特殊群体卡，3-禁止名单卡，4-巡更卡，5-胁迫卡，6-超级卡，7-来宾卡，8-解除卡，为0无效
	byAllowListNo                  BYTE                  //允许名单单号,1-8，为0无效
	byReportChannel                BYTE                  //报告上传通道，1-布防上传，2-中心组1上传，3-中心组2上传，为0无效
	byCardReaderKind               BYTE                  //读卡器属于哪一类，0-无效，1-IC读卡器，2-身份证读卡器，3-二维码读卡器,4-指纹头
	dwCardReaderNo                 DWORD                 //读卡器编号，为0无效
	dwDoorNo                       DWORD                 //门编号（楼层编号），为0无效
	dwVerifyNo                     DWORD                 //多重卡认证序号，为0无效
	dwAlarmInNo                    DWORD                 //报警输入号，为0无效
	dwAlarmOutNo                   DWORD                 //报警输出号，为0无效
	dwCaseSensorNo                 DWORD                 //事件触发器编号
	dwRs485No                      DWORD                 //RS485通道号，为0无效
	dwMultiCardGroupNo             DWORD                 //群组编号
	wAccessChannel                 WORD                  //人员通道号
	byDeviceNo                     BYTE                  //设备编号，为0无效（有效范围1-255）
	byDistractControlNo            BYTE                  //分控器编号，为0无效
	dwEmployeeNo                   DWORD                 //工号，为0无效
	wLocalControllerID             WORD                  //就地控制器编号，0-门禁主机，1-64代表就地控制器
	byInternetAccess               BYTE                  //网口ID：（1-上行网口1,2-上行网口2,3-下行网口1）
	byType                         BYTE                  //防区类型，0:即时防区,1-24小时防区,2-延时防区 ,3-内部防区，4-钥匙防区 5-火警防区 6-周界防区 7-24小时无声防区  8-24小时辅助防区，9-24小时震动防区,10-门禁紧急开门防区，11-门禁紧急关门防区 0xff-无
	byMACAddr                      [MACADDR_LEN]BYTE     //物理地址，为0无效
	bySwipeCardType                BYTE                  //刷卡类型，0-无效，1-二维码
	byEventAttribute               BYTE                  //事件属性：0-未定义，1-合法认证，2-其它
	dwSerialNo                     DWORD                 //事件流水号，为0无效
	byChannelControllerID          BYTE                  //通道控制器ID，为0无效，1-主通道控制器，2-从通道控制器
	byChannelControllerLampID      BYTE                  //通道控制器灯板ID，为0无效（有效范围1-255）
	byChannelControllerIRAdaptorID BYTE                  //通道控制器红外转接板ID，为0无效（有效范围1-255）
	byChannelControllerIREmitterID BYTE                  //通道控制器红外对射ID，为0无效（有效范围1-255）
	dwRecordChannelNum             DWORD                 //录像通道数目
	pRecordChannelData             *BYTE                 //录像通道，大小为sizeof(DWORD)* dwRecordChannelNum
	byUserType                     BYTE                  //人员类型：0-无效，1-普通人（主人），2-来宾（访客），3-禁止名单人，4-管理员
	byCurrentVerifyMode            BYTE                  //读卡器当前验证方式：0-无效，1-休眠，2-刷卡+密码，3-刷卡，4-刷卡或密码，5-指纹，6-指纹+密码，7-指纹或刷卡，8-指纹+刷卡，9-指纹+刷卡+密码，10-人脸或指纹或刷卡或密码，11-人脸+指纹，12-人脸+密码，
	//13-人脸+刷卡，14-人脸，15-工号+密码，16-指纹或密码，17-工号+指纹，18-工号+指纹+密码，19-人脸+指纹+刷卡，20-人脸+密码+指纹，21-工号+人脸，22-人脸或人脸+刷卡，23-指纹或人脸，24-刷卡或人脸或密码，25-刷卡或人脸，26-刷卡或人脸或指纹，27-刷卡或指纹或密码，28-人脸或密码，29-工号+人脸+密码，30-刷卡或人脸或人脸+刷卡，31-人脸或指纹或密码，32-虹膜，33-人脸或指纹或刷卡或密码或虹膜，34-人脸或刷卡或密码或虹膜
	byAttendanceStatus     BYTE                          //考勤状态：0-未定义,1-上班，2-下班，3-开始休息，4-结束休息，5-开始加班，6-结束加班
	byStatusValue          BYTE                          //考勤状态值
	byEmployeeNo           [NET_SDK_EMPLOYEE_NO_LEN]BYTE //工号（人员ID）（对于设备来说，如果使用了工号（人员ID）字段，byEmployeeNo一定要传递，如果byEmployeeNo可转换为dwEmployeeNo，那么该字段也要传递；对于上层平台或客户端来说，优先解析byEmployeeNo字段，如该字段为空，再考虑解析dwEmployeeNo字段）
	byRes1                 BYTE                          //保留
	byMask                 BYTE                          //是否带口罩：0-保留，1-未知，2-不戴口罩，3-戴口罩
	byThermometryUnit      BYTE                          //测温单位（0-摄氏度（默认），1-华氏度，2-开尔文）
	byIsAbnomalTemperature BYTE                          //人脸抓拍测温是否温度异常：1-是，0-否
	fCurrTemperature       float32                       //人脸温度（精确到小数点后一位）
	struRegionCoordinates  NET_VCA_POINT                 //人脸温度坐标
	byRes                  [48]BYTE
}
type NET_DVR_ACS_EVENT_CFG struct {
	dwSize                DWORD
	dwMajor               DWORD                    //报警主类型，参考宏定义
	dwMinor               DWORD                    //报警次类型，参考宏定义
	struTime              NET_DVR_TIME             //时间
	sNetUser              [MAX_NAMELEN]BYTE        //网络操作的用户名
	struRemoteHostAddr    NET_DVR_IPADDR           //远程主机地址
	struAcsEventInfo      NET_DVR_ACS_EVENT_DETAIL //详细参数
	dwPicDataLen          DWORD                    //图片数据大小，不为0是表示后面带数据
	pPicData              *BYTE
	wInductiveEventType   WORD //归纳事件类型，0-无效，其他值参见2.2章节，客户端判断该值为非0值后，报警类型通过归纳事件类型区分，否则通过原有报警主次类型（dwMajor、dwMinor）区分
	byTimeType            BYTE //时间类型：0-设备本地时间（默认），1-UTC时间（struTime的时间）
	byRes1                BYTE
	dwQRCodeInfoLen       DWORD //二维码信息长度，不为0是表示后面带数据
	dwVisibleLightDataLen DWORD //热成像相机可见光图片长度，不为0是表示后面带数据
	dwThermalDataLen      DWORD //热成像图片长度，不为0是表示后面带数据
	pQRCodeInfo           *BYTE //二维码信息指针
	pVisibleLightData     *BYTE //热成像相机可见光图片指针
	pThermalData          *BYTE //热成像图片指针
	byRes                 [36]BYTE
}

//-----------返回门禁状态结构体-------------------------------//

// NET_DVR_ACS_WORK_STATUS  门禁主机工作状态结构体
type NET_DVR_ACS_WORK_STATUS struct {
	ST_dwSize                          DWORD     //结构体大小
	ST_byDoorLockStatus                [32]byte  //门锁状态：0- 关，1- 开
	ST_byDoorStatus                    [32]byte  //门状态：1- 休眠，2- 常开状态，3- 常闭状态，4- 普通状态
	ST_byMagneticStatus                [32]byte  //门磁状态：0- 闭合，1- 开启
	ST_byCaseStatus                    [8]byte   //事件报警输入状态：0- 无输入，1- 有输入
	ST_wBatteryVoltage                 WORD      //蓄电池电压值，实际值乘10，单位：伏特
	ST_byBatteryLowVoltage             byte      //蓄电池是否处于低压状态：0- 否，1- 是
	ST_byPowerSupplyStatus             byte      //设备供电状态：1- 交流电供电，2- 蓄电池供电
	ST_byMultiDoorInterlockStatus      byte      //多门互锁状态：0- 关闭，1- 开启
	ST_byAntiSneakStatus               byte      //反潜回状态：0-关闭，1-开启
	ST_byHostAntiDismantleStatus       byte      //主机防拆状态：0- 关闭，1- 开启
	ST_byIndicatorLightStatus          byte      //指示灯状态
	ST_byCardReaderOnlineStatus        [64]byte  //读卡器在线状态：0- 不在线，1- 在线
	ST_byCardReaderAntiDismantleStatus [64]byte  //读卡器防拆状态：0- 关闭，1- 开启
	ST_byCardReaderVerifyMode          [64]byte  //读卡器当前验证方式：0- 无效，1- 休眠，2- 刷卡+密码，3- 刷卡，4- 刷卡或密码，5- 指纹，6- 指纹加密码，7- 指纹或刷卡，8- 指纹加刷卡，9- 指纹加刷卡加密码
	ST_bySetupAlarmStatus              [512]byte //报警输入口布防状态：0- 对应报警输入口处于撤防状态，1- 对应报警输入口处于布防状态
	ST_byAlarmInStatus                 [512]byte //报警输入口报警状态：0- 对应报警输入口当前无报警，1- 对应报警输入口当前有报警
	ST_byAlarmOutStatus                [512]byte //报警输出口状态：0- 对应报警输出口无报警，1- 对应报警输出口有报警
	ST_dwCardNum                       DWORD     //已添加的卡数量
	ST_byRes2                          [32]byte  //保留，置为0
}

//-----------获取卡参数结构体-------------------------------//

// NET_DVR_CARD_COND 获取卡参数配置条件结构体。old
type NET_DVR_CARD_COND struct {
	ST_dwSize    DWORD
	ST_dwCardNum DWORD
	ST_byRes     [64]byte
}

//NET_DVR_VALID_PERIOD_CFG 有效期参数结构体
type NET_DVR_VALID_PERIOD_CFG struct {
	byEnable         byte            //使能有效期，0-不使能，1使能
	byBeginTimeFlag  byte            //是否限制起始时间的标志，0-不限制，1-限制
	byEnableTimeFlag byte            //是否限制终止时间的标志，0-不限制，1-限制
	byTimeDurationNo byte            //有效期索引,从0开始（时间段通过SDK设置给锁，后续在制卡时，只需要传递有效期索引即可，以减少数据量）
	struBeginTime    NET_DVR_TIME_EX //有效期起始时间
	struEndTime      NET_DVR_TIME_EX //有效期结束时间
	byTimeType       byte            //时间类型：0-设备本地时间（默认），1-UTC时间（对于struBeginTime，struEndTime字段有效）
	byRes2           [31]byte
}

// NET_DVR_TIME_EX 时间参数结构体扩展
type NET_DVR_TIME_EX struct {
	ST_wYear    WORD //年
	ST_byMonth  byte //月
	ST_byDay    byte //日
	ST_byHour   byte //时
	ST_byMinute byte //分
	ST_bySecond byte //秒
	byRes       byte
}

// NET_DVR_CARD_RECORD 返回卡信息结构体
type NET_DVR_CARD_RECORD struct {
	dwSize     DWORD    //结构体大小
	byCardNo   [32]byte //卡号
	byCardType byte     /*卡类型：1- 普通卡（默认），2- 残障人士卡，3- 禁止名单卡，4- 巡
	更卡，5- 胁迫卡，6- 超级卡，7- 来宾卡，8- 解除卡，9- 员工卡，10- 应急卡，11- 应急管理卡（用
	于授权临时卡权限，本身不能开门），默认普通卡*/
	byLeaderCard    byte /*是否为首卡：1- 是，0- 否*/
	byUserType      byte //用户类型：0 – 普通用户 1- 管理员用户
	byRes1          byte
	byDoorRight     [256]byte                /*门权限（梯控的楼层权限、锁权限），按字节表示，1-为有权限，0-为无权限，从低位到高位依次表示对门（或者梯控楼层、锁）1-N 是否有权限	*/
	struValid       NET_DVR_VALID_PERIOD_CFG /*有效期参数（有效时间跨度为 1970 年 1 月 1 日 0 点 0 分 0 秒~2037 年 12 月 31 日 23 点 59 分 59 秒）*/
	byBelongGroup   [128]byte                /*所属群组，按字节表示，1-属于，0-不属于，从低位到高位表示是否从属群组 1~N*/
	byCardPassword  [8]byte                  //卡密码
	wCardRightPlan  [256]WORD                /*卡权限计划，取值为计划模板编号，同个门（锁）不同计划模板采用权限或的方式处理 */
	dwMaxSwipeTimes DWORD                    //最大刷卡次数，0 为无次数限制
	dwSwipeTimes    DWORD                    //已刷卡次数
	dwEmployeeNo    DWORD                    //工号（用户 ID），1~99999999，不能以 0 开头且不能重复
	byName          [32]byte                 //姓名
	dwCardRight     DWORD                    //卡权限
	byRes           [256]byte
}
type NET_DVR_USER_LOGIN_INFO struct {
	sDeviceAddress [NET_DVR_DEV_ADDRESS_MAX_LEN]byte
	byUseTransport byte
	wPort          WORD
	sUserName      [NET_DVR_LOGIN_USERNAME_MAX_LEN]byte
	sPassword      [NET_DVR_LOGIN_PASSWD_MAX_LEN]byte
	cbLoginResult  unsafe.Pointer
	pUser          unsafe.Pointer
	bUseAsynLogin  int32
	byProxyType    byte
	byUseUTCTime   byte
	byLoginMode    byte
	byHttps        byte
	iProxyID       LONG
	byVerifyMode   byte
	byRes3         [119]byte
}

type NET_DVR_DEVICEINFO_V40 struct {
	struDeviceV30      *NET_DVR_DEVICEINFO_V30
	bySupportLock      BYTE  //设备支持锁定功能，该字段由SDK根据设备返回值来赋值的。bySupportLock为1时，dwSurplusLockTime和byRetryLoginTime有效
	byRetryLoginTime   BYTE  //剩余可尝试登陆的次数，用户名，密码错误时，此参数有效
	byPasswordLevel    BYTE  //admin密码安全等级0-无效，1-默认密码，2-有效密码,3-风险较高的密码。当用户的密码为出厂默认密码（12345）或者风险较高的密码时，上层客户端需要提示用户更改密码。
	byProxyType        BYTE  //代理类型，0-不使用代理, 1-使用socks5代理, 2-使用EHome代理
	dwSurplusLockTime  DWORD //剩余时间，单位秒，用户锁定时，此参数有效
	byCharEncodeType   BYTE  //字符编码类型
	bySupportDev5      BYTE  //支持v50版本的设备参数获取，设备名称和设备类型名称长度扩展为64字节
	bySupport          BYTE  //能力集扩展，位与结果：0- 不支持，1- 支持
	byLoginMode        BYTE  //登录模式 0-Private登录 1-ISAPI登录
	dwOEMCode          DWORD
	iResidualValidity  int32 //该用户密码剩余有效天数，单位：天，返回负值，表示密码已经超期使用，例如“-3表示密码已经超期使用3天”
	byResidualValidity BYTE  // iResidualValidity字段是否有效，0-无效，1-有效
	byRes2             [243]BYTE
}
type NET_DVR_DEVICEINFO_V30 struct {
	sSerialNumber        [48]BYTE //序列号
	byAlarmInPortNum     BYTE     //报警输入个数
	byAlarmOutPortNum    BYTE     //报警输出个数
	byDiskNum            BYTE     //硬盘个数
	byDVRType            BYTE     //设备类型, 1:DVR 2:ATM DVR 3:DVS ......
	byChanNum            BYTE     //模拟通道个数
	byStartChan          BYTE     //起始通道号,例如DVS-1,DVR - 1
	byAudioChanNum       BYTE     //语音通道数
	byIPChanNum          BYTE     //最大数字通道个数，低位
	byZeroChanNum        BYTE     //零通道编码个数 //2010-01-16
	byMainProto          BYTE     //主码流传输协议类型 0-private, 1-rtsp,2-同时支持private和rtsp
	bySubProto           BYTE     //子码流传输协议类型0-private, 1-rtsp,2-同时支持private和rtsp
	bySupport            BYTE
	bySupport1           BYTE
	bySupport2           BYTE
	wDevType             WORD
	bySupport3           BYTE
	byMultiStreamProto   BYTE //是否支持多码流,按位表示,0-不支持,1-支持,bit1-码流3,bit2-码流4,bit7-主码流，bit-8子码流
	byStartDChan         BYTE //起始数字通道号,0表示无效
	byStartDTalkChan     BYTE //起始数字对讲通道号，区别于模拟对讲通道号，0表示无效
	byHighDChanNum       BYTE //数字通道个数，高位
	bySupport4           BYTE
	byLanguageType       BYTE
	byVoiceInChanNum     BYTE //音频输入通道数
	byStartVoiceInChanNo BYTE //音频输入起始通道号 0表示无效
	bySupport5           BYTE
	bySupport6           BYTE
	byMirrorChanNum      BYTE //镜像通道个数，<录播主机中用于表示导播通道>
	wStartMirrorChanNo   WORD //起始镜像通道号
	bySupport7           BYTE
	byRes2               BYTE //保留
}

type DoorControllerHis struct {
	Responsestatusstrg string     `json:"responseStatusStrg"`
	Infolist           []Infolist `json:"infoList"`
}
type Infolist struct {
	Info   string `json:"info"`
	Time   string `json:"time"`
	Cardno string `json:"cardNo"`
	Doorid int    `json:"doorId"`
}

type NET_DVR_CARD_STATUS struct {
	dwSize      DWORD
	byCardNo    [32]BYTE
	dwErrorCode DWORD
	byStatus    BYTE
	byRes       [23]BYTE
}

var major = map[int]string{
	1: "报警类型",
	2: "异常类型",
	3: "操作类型",
	5: "事件",
}
var minorString = map[DWORD]map[DWORD]string{
	1: {
		0x400: "防区短路报警",
		0x401: "防区断路报警",
		0x042: "防区异常报警",
		0x403: "防区报警恢复",
		0x404: "防区防拆报警",
		0x405: "防区防拆恢复",
		0x406: "读卡器防拆报警",
		0x407: "读卡器防拆恢复",
		0x408: "事件输入报警",
		0x409: "事件输入恢复",
		0x40a: "胁迫报警",
		0x40b: "离线事件满90%报警",
		0x40c: "卡号认证失败超次报警",
		0x40d: "SD卡存储满报警",
		0x40e: "联动抓拍事件报警",
		0x40f: "门控安全模块防拆报警",
		0x410: "门控安全模块防拆恢复",
		0x411: "POS开启",
		0x412: "POS结束",
		0x413: "人脸图像画质低",
		0x414: "指纹图像画质低",
		0x415: "消防输入短路报警",
		0x416: "消防输入断路报警",
		0x417: "消防输入恢复",
		0x418: "消防按钮触发",
		0x419: "消防按钮恢复",
		0x41a: "维护按钮触发",
		0x41b: "维护按钮恢复",
		0x41c: "维护按钮恢复",
		0x41d: "紧急按钮恢复",
		0x41e: "分控制器防拆报警",
		0x41f: "分控制器防拆报警恢复",
		0x422: "通道控制器防拆报警",
		0x423: "通道控制器防拆报警恢复",
		0x424: "通道控制器消防输入报警",
		0x425: "通道控制器消防输入报警恢复",
		0x442: "合法事件满90%报警",
	},
	2: {
		0x27:  "网络断开",
		0x3a:  "RS485连接状态异常",
		0x3b:  "RS485链接状态异常恢复",
		0x400: "设备上电启动",
		0x401: "设备掉电关闭",
		0x402: "看门够复位",
		0x403: "蓄电池电压低",
		0x404: "蓄电池电压恢复正常",
		0x405: "交流电断电",
		0x406: "交流电恢复",
		0x407: "网络恢复",
		0x408: "FLASH读写异常",
		0x409: "读卡器掉线",
		0x40a: "读卡器掉线恢复",
		0x40b: "指示灯关闭",
		0x40c: "指示灯恢复",
		0x40d: "通道控制器掉线",
		0x40e: "通道控制器恢复",
		0x40f: "门控安全模块掉线",
		0x410: "门控安全模块掉线恢复",
		0x413: "就地控制器网络断开",
		0x414: "就地控制器网络恢复",
		0x415: "主控RS485环路节点断开",
		0x416: "主控RS485环路节点恢复",
		0x417: "就地控制器掉线",
		0x418: "就地控制器掉线恢复",
		0x419: "就地下行RS485环路恢复",
		0x41a: "就地下行RS485环路恢复",
		0x41b: "分控制器在线",
		0x41c: "分控制器离线",
		0x41d: "身份证阅读器未连接",
		0x41e: "身份控制器连接恢复",
		0x41f: "指纹模组未连接",
		0x420: "指纹模组连接恢复",
		0x421: "摄像头未连接",
		0x422: "摄像头连接恢复",
		0x423: "COM口未连接",
		0x424: "COM口连接恢复",
		0x425: "设备未授权",
		0x426: "人证设备在线",
		0x427: "人证设备离线",
		0x411: "电池电压低",
		0x412: "电池电压恢复正常",
		0x428: "本地登录锁定",
		0x429: "本地登录解锁",
		0x42a: "与反潜回服务器通信断开",
		0x42b: "与反潜回服务器通信恢复",
		0x42c: "电机或传感器异常",
		0x42d: "CAN 总线异常",
		0x42e: "CAN 总线恢复",
		0x42f: "闸机腔体温度超限",
		0x430: "红外对射异常",
		0x431: "红外对射恢复",
		0x432: "灯板通信异常",
		0x433: "灯板通信恢复",
		0x434: "红外转接板通信异常",
		0x435: "红外转接板通信恢复",
	},
	3: {
		0x50:  "本地登陆",
		0x51:  "本地注销登陆",
		0x5a:  "本地升级",
		0x70:  "远程登录",
		0x71:  "远程注销登录",
		0x79:  "远程布防",
		0x7a:  "远程撤防",
		0x7b:  "远程重启",
		0x7e:  "远程升级",
		0x86:  "远程导出配置文件",
		0x87:  "远程导入配置文件",
		0xd6:  "远程手动开启报警输出",
		0xd7:  "远程手动关闭报警输出",
		0x400: "远程开门",
		0x401: "远程关门",
		0x402: "远程常开",
		0x403: "远程常关",
		0x404: "远程手动校时",
		0x405: "NTP自动校时",
		0x406: "远程清空卡号",
		0x407: "远程恢复默认参数",
		0x408: "防区布防",
		0x409: "防区撤防",
		0x40a: "本地恢复默认参数",
		0x40b: "远程抓拍",
		0x40c: "修改网络中心参数配置",
		0x40d: "修改 GPRS 中心参数配置",
		0x40e: "修改中心组参数配置",
		0x40f: "解除码输入",
		0x410: "自动重新编号",
		0x411: "自动补充编号",
		0x412: "导入普通配置文件",
		0x413: "导出普通配置文件",
		0x414: "导入卡权限参数",
		0x415: "导出卡权限参数",
		0x416: "本地 U 盘升级",
		0x417: "访客呼梯",
		0x418: "住户呼梯",
		0x419: "远程实时布防",
		0x41a: "远程实时撤防",
		0x41b: "遥控器未对码操作失败",
		0x41c: "遥控器关门",
		0x41d: "遥控器开门",
		0x41e: "遥控器常开门",
	},
	5: {
		0x01: "合法卡认证通过",
		0x02: "刷卡加密码认证通过",
		0x03: "刷卡加密码认证失败",
		0x04: "数卡加密码认证超时",
		0x05: "刷卡加密码超次",
		0x06: "未分配权限",
		0x07: "无效时段",
		0x08: "卡号过期",
		0x09: "无此卡号",
		0x0a: "反潜回认证失败",
		0x0b: "互锁门未关闭",
		0x0c: "卡不属于多重认证群组",
		0x0d: "卡不在多重认证时间段内",
		0x0e: "多重认证模式超级权限认证失败",
		0x0f: "多重认证模式远程认证失败",
		0x10: "多重认证成功",
		0x11: "首卡开门开始",
		0x12: "首卡开门结束",
		0x13: "常开状态开始",
		0x14: "常开状态结束",
		0x15: "门锁打开",
		0x16: "门锁关闭",
		0x17: "开门按钮打开",
		0x18: "开门按钮放开",
		0x19: "正常开门(门磁)",
		0x1a: "正常关门(门磁)",
		0x1b: "门异常打开(门磁)",
		0x1d: "报警输出打开",
		0x1e: "报警输出关闭",
		0x1f: "常关状态开始",
		0x20: "常关状态结束",
		0x21: "多重多重认证需要远程开门",
		0x22: "多重认证超级密码认证成功事件",
		0x23: "多重认证重复认证事件",
		0x24: "多重认证超时",
		0x25: "门铃响",
		0x26: "指纹比对通过",
		0x27: "指纹比对失败",
		0x28: "刷卡加指纹认证通过",
		0x29: "刷卡加指纹认证失败",
		0x2a: "刷卡加指纹认证超时",
		0x2b: "刷卡加指纹加密码认证通过",
		0x2c: "刷卡加指纹加密码认证失败",
		0x2d: "刷卡加指纹加密码认证超时",
		0x2e: "指纹加密码认证通过",
		0x2f: "指纹加密码认证失败",
		0x30: "指纹加密码认证超时",
		0x31: "指纹不存在",
		0x32: "刷卡平台认证",
		0x33: "呼叫中心事件",
		0x34: "消防继电器导通触发门常开",
		0x35: "消防继电器恢复门恢复正常",
		0x36: "人脸加指纹认证通过",
		0x37: "人脸加指纹认证失败",
		0x38: "人脸加指纹认证超时",
		0x39: "人脸加密码认证通过",
		0x3a: "人脸加密码认证失败",
		0x3b: "人脸加密码认证超时",
		0x3c: "人脸加刷卡认证通过",
		0x3d: "人脸加刷卡认证失败",
		0x3e: "人脸加刷卡认证超时",
		0x3f: "人脸加密码加指纹认证通过",
		0x40: "人脸加密码加指纹认证失败",
		0x41: "人脸加密码加指纹认证超时",
		0x42: "人脸加刷卡加指纹认证通过",
		0x43: "人脸加刷卡加指纹认证失败",
		0x44: "人脸加刷卡加指纹认证超时",
		0x45: "工号加指纹认证通过",
		0x46: "工号加指纹认证失败",
		0x47: "工号加指纹认证超时",
		0x48: "工号加指纹加密码认证通过",
		0x49: "工号加指纹加密码认证失败",
		0x4a: "工号加指纹加密码认证超时",
		0x4b: "人脸认证通过",
		0x4c: "人脸认证失败",
		0x4d: "工号加人脸认证通过",
		0x4e: "工号加人脸认证失败",
		0x4f: "工号加人脸认证超时",
		0x50: "人脸识别失败",
		0x51: "首卡授权开始",
		0x52: "首卡授权结束",
		0x53: "门锁输入短路报警",
		0x54: "门锁输入断路报警",
		0x55: "门锁输入异常报警",
		0x56: "门磁输入短路报警",
		0x57: "门磁输入断路报警",
		0x58: "门磁输入异常报警",
		0x59: "开门按钮输入短路报警",
		0x5a: "开门按钮输入断路报警",
		0x5b: "开门按钮输入异常报警",
		0x5c: "门锁异常打开",
		0x5d: "门锁打开超时",
		0x5e: "首卡未授权开门失败",
		0x5f: "呼梯继电器断开",
		0x60: "呼梯继电器闭合",
		0x61: "自动按键继电器断开",
		0x62: "自动按键继电器闭合",
		0x63: "按键梯控继电器断开",
		0x64: "按键梯控继电器闭合",
		0x65: "工号加密码认证通过",
		0x66: "工号加密码认证失败",
		0x67: "工号加密码认证超时",
		0x68: "真人检测失败",
		0x69: "人证比对通过",
		0x70: "人证比对失败",
		0x71: "黑名单事件",
		0x72: "合法短信",
		0x73: "非法短信",
		0x74: "MAC 侦测",
		0x75: "门状态常闭或休眠状态认证失败",
		0x76: "认证计划休眠模式认证失败",
		0x77: "卡加密校验失败",
		0x78: "反潜回服务器应答失败",
		0x85: "尾随通行",
		0x86: "反向闯入",
		0x87: "外力冲撞",
		0x88: "翻越",
		0x89: "通行超时",
		0x8a: "误闯报警",
		0x8b: "闸机自由通行时未认证通过",
		0x8c: "摆臂被阻挡",
		0x8d: "摆臂阻挡消除",
		0x8e: "设备升级本地人脸建模失败",
		0x8f: "逗留事件",
		0x97: "密码不匹配",
		0x98: "工号不存在",
		0x99: "组合认证通过",
		0x9a: "组合认证超时",
		0x9b: "认证方式不匹配",
	},
}
