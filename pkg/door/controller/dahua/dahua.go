package controller

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"
	"tw_platform/pkg/door/controller/comm"
	"tw_platform/pkg/door/digest"
	"tw_platform/pkg/model/request"
)

const (
	jsonType              = "application/json"
	controlDoorUrl        = "/cgi-bin/accessControl.cgi?action=%s&&channel=%d"
	getDoorStatusUrl      = "/cgi-bin/accessControl.cgi?action=getDoorStatus&channel=%d"
	getAccessUserTokenUrl = "/cgi-bin/AccessUser.cgi?action=startFind"
	getAccessUserDataUrl  = "/cgi-bin/AccessUser.cgi?action=doFind&Token=%d&Offset=0&Count=%d"
	getAccessCardTokenUrl = "/cgi-bin/AccessCard.cgi?action=startFind&Condition.UserID=%s"
	getAccessCardDataUrl  = "/cgi-bin/AccessCard.cgi?action=doFind&Token=%d&Offset=0&Count=%d"
	insertUserUrl         = "/cgi-bin/AccessUser.cgi?action=insertMulti"
	removeUserUrl         = "/cgi-bin/AccessUser.cgi?action=removeMulti&UserIDList[0]=%s"
	insertCardUrl         = "/cgi-bin/AccessCard.cgi?action=insertMulti"
	removeCardUrl         = "/cgi-bin/AccessCard.cgi?action=removeMulti&CardNoList[0]=%s"
	getRecordUrl          = "/cgi-bin/recordFinder.cgi?action=find&name=AccessControlCardRec&StartTime=%s&EndTime=%s"
)

type DH struct {
	AdminName string
	AdminPwd  string
	Host      string
	SubCount  int
}

func (h *DH) GetDoorStatus() (*comm.DoorStatus, error) {
	t := digest.NewTransport(h.AdminName, h.AdminPwd)
	c, err := t.Client()
	c.Timeout = time.Second * 3
	if err != nil {
		return nil, err
	}
	var doorstatus comm.DoorStatus
	for i := 1; i <= h.SubCount; i++ {
		resp, err := c.Get(h.Host + fmt.Sprintf(getDoorStatusUrl, i))
		if err != nil {
			return nil, err
		}
		defer resp.Body.Close()
		if resp.StatusCode != http.StatusOK {
			return nil, fmt.Errorf("errorcode:%d", resp.StatusCode)
		}
		s, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, err
		}
		if strings.Contains(string(s), "Info.status=Open") {
			doorstatus.DoorStatus = append(doorstatus.DoorStatus, 1)
		} else {
			doorstatus.DoorStatus = append(doorstatus.DoorStatus, 0)
		}
	}
	return &doorstatus, nil
}
func (h *DH) CtrDoor(doorid int, way string) error {
	ctrl := ""
	switch way {
	case "close":
		ctrl = "closeDoor"
	case "open":
		ctrl = "openDoor"
	}
	t := digest.NewTransport(h.AdminName, h.AdminPwd)
	c, err := t.Client()
	if err != nil {
		return err
	}
	c.Timeout = time.Second * 3
	resp, err := c.Post(h.Host+fmt.Sprintf(controlDoorUrl, ctrl, doorid), jsonType, bytes.NewReader(nil))
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("errorcode:%d", resp.StatusCode)
	}
	return nil
}

type UserListData struct {
	UserList []UserList `json:"UserList"`
}
type UserList struct {
	UserID    string `json:"UserID"`
	UserName  string `json:"UserName"`
	Password  string `json:"Password"`
	Doors     []int  `json:"Doors"`
	ValidFrom string `json:"ValidFrom"`
	ValidTo   string `json:"ValidTo"`
}

func (h *DH) InsertUser(req request.InserUserDahuaReq) error {
	var (
		data UserListData
	)
	data.UserList = append(data.UserList,
		UserList{
			UserID:    req.UserID,
			UserName:  req.UserName,
			Doors:     req.Doors,
			Password:  req.Password,
			ValidFrom: "1970-01-01 00:00:00",
			ValidTo:   "2037-11-02 00:00:00",
		},
	)
	b, err := json.Marshal(data)
	if err != nil {
		return err
	}
	t := digest.NewTransport(h.AdminName, h.AdminPwd)
	c, err := t.Client()
	if err != nil {
		return err
	}
	c.Timeout = time.Second * 3
	resp, err := c.Post(h.Host+insertUserUrl, jsonType, bytes.NewReader(b))
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("errorcode:%d", resp.StatusCode)
	}
	return nil
}

type TokenData struct {
	Caps  int `json:"Caps"`
	Token int `json:"Token"`
	Total int `json:"Total"`
}
type AccessUserData struct {
	Info []UserInfo `json:"Info"`
}
type MultiTimeSections struct {
	Door                int   `json:"Door"`
	SpecialDaysSchedule []int `json:"SpecialDaysSchedule"`
	TimeSections        []int `json:"TimeSections"`
}
type UserInfo struct {
	Authority           int                 `json:"Authority"`
	CitizenIDNo         string              `json:"CitizenIDNo"`
	Doors               []int               `json:"Doors"`
	Email               string              `json:"Email"`
	FirstEnterDoors     []int               `json:"FirstEnterDoors"`
	IsFirstEnter        bool                `json:"IsFirstEnter"`
	MultiTimeSections   []MultiTimeSections `json:"MultiTimeSections"`
	Password            string              `json:"Password"`
	RoleID              int                 `json:"RoleID"`
	SpecialDaysSchedule []int               `json:"SpecialDaysSchedule"`
	TimeSections        []int               `json:"TimeSections"`
	UseTime             int                 `json:"UseTime"`
	UserID              string              `json:"UserID"`
	UserName            string              `json:"UserName"`
	UserStatus          int                 `json:"UserStatus"`
	UserType            int                 `json:"UserType"`
	VTOPosition         string              `json:"VTOPosition"`
	ValidFrom           string              `json:"ValidFrom"`
	ValidTo             string              `json:"ValidTo"`
}

func (h *DH) AccessUser() ([]UserInfo, error) {
	var (
		// info  []Info
		token TokenData
		user  AccessUserData
	)
	t := digest.NewTransport(h.AdminName, h.AdminPwd)
	c, err := t.Client()
	c.Timeout = time.Second * 3
	if err != nil {
		return nil, err
	}
	resp, err := c.Get(h.Host + getAccessUserTokenUrl)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("errorcode:%d", resp.StatusCode)
	}
	s, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(s, &token)
	if err != nil {
		return nil, err
	}
	resp2, err := c.Get(h.Host + fmt.Sprintf(getAccessUserDataUrl, token.Token, token.Total))
	if err != nil {
		return nil, err
	}
	defer resp2.Body.Close()
	if resp2.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("errorcode:%d", resp2.StatusCode)
	}
	s2, err := io.ReadAll(resp2.Body)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(s2, &user)
	if err != nil {
		return nil, err
	}
	return user.Info, nil
}
func (h *DH) RemoveUser(user_id string) error {
	t := digest.NewTransport(h.AdminName, h.AdminPwd)
	c, err := t.Client()
	c.Timeout = time.Second * 3
	if err != nil {
		return err
	}
	resp, err := c.Get(h.Host + fmt.Sprintf(removeUserUrl, user_id))
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("errorcode:%d", resp.StatusCode)
	}
	return nil
}

type CardListData struct {
	CardList []CardList `json:"CardList"`
}
type CardList struct {
	UserID     string `json:"UserID"`
	CardNo     string `json:"CardNo"`
	CardType   int    `json:"CardType"`
	CardName   string `json:"CardName"`
	CardStatus int    `json:"CardStatus"`
}

func (h *DH) InsertCard(req request.InserCardDahuaReq) error {
	var (
		data CardListData
	)
	data.CardList = append(data.CardList,
		CardList{
			UserID:     req.UserID,
			CardNo:     req.CardNo,
			CardType:   req.CardType,
			CardName:   req.CardName,
			CardStatus: 0,
		},
	)
	b, err := json.Marshal(data)
	if err != nil {
		return err
	}
	t := digest.NewTransport(h.AdminName, h.AdminPwd)
	c, err := t.Client()
	if err != nil {
		return err
	}
	c.Timeout = time.Second * 3
	resp, err := c.Post(h.Host+insertCardUrl, jsonType, bytes.NewReader(b))
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("errorcode:%d", resp.StatusCode)
	}
	return nil
}

type CardInfoData struct {
	Info []CardInfo `json:"Info"`
}
type CardInfo struct {
	CardID         string `json:"CardID"`
	CardName       string `json:"CardName"`
	CardNo         string `json:"CardNo"`
	CardStatus     int    `json:"CardStatus"`
	CardType       int    `json:"CardType"`
	CreateTime     int    `json:"CreateTime"`
	FacilityCode   string `json:"FacilityCode"`
	UserID         string `json:"UserID"`
	ValidDateEnd   string `json:"ValidDateEnd"`
	ValidDateStart string `json:"ValidDateStart"`
}

func (h *DH) AccessCard(user_id string) ([]CardInfo, error) {
	var (
		token TokenData
		user  CardInfoData
	)
	t := digest.NewTransport(h.AdminName, h.AdminPwd)
	c, err := t.Client()
	c.Timeout = time.Second * 3
	if err != nil {
		return nil, err
	}
	resp, err := c.Get(h.Host + fmt.Sprintf(getAccessCardTokenUrl, user_id))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("errorcode:%d", resp.StatusCode)
	}
	s, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(s, &token)
	if err != nil {
		return nil, err
	}
	resp2, err := c.Get(h.Host + fmt.Sprintf(getAccessCardDataUrl, token.Token, token.Total))
	if err != nil {
		return nil, err
	}
	defer resp2.Body.Close()
	if resp2.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("errorcode:%d", resp2.StatusCode)
	}
	s2, err := io.ReadAll(resp2.Body)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(s2, &user)
	if err != nil {
		return nil, err
	}
	return user.Info, nil
}
func (h *DH) RemoveCard(card_no string) error {
	t := digest.NewTransport(h.AdminName, h.AdminPwd)
	c, err := t.Client()
	c.Timeout = time.Second * 3
	if err != nil {
		return err
	}
	resp, err := c.Get(h.Host + fmt.Sprintf(removeCardUrl, card_no))
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("errorcode:%d", resp.StatusCode)
	}
	return nil
}

type Record struct {
	CardNo     string `json:"CardNo"`
	CreateTime int64  `json:"CreateTime"`
	Door       int    `json:"Door"`
	Method     int    `json:"Method"`   //开门方式 0: 管理员密码开锁 1: 刷卡开锁 2: 先刷卡后密码开锁  4  远程开门  5 按钮开门
	RecNo      int    `json:"RecNo"`    //记录编号
	Status     int    `json:"Status"`   //开门状态 0失败  1成功
	UserID     string `json:"UserID"`   //用户id
	CardName   string `json:"UserName"` //用户名 奇怪
}
type RecordData struct {
	Record []Record `json:"data"`
	Found  int      `json:"found"`
}

func recordFind(data string) RecordData {
	foundRe := regexp.MustCompile(`found=(\d+)`)
	matche := foundRe.FindStringSubmatch(data)
	var found int
	if len(matche) > 1 {
		found, _ = strconv.Atoi(matche[1])
	}
	recordRe := regexp.MustCompile(`records\[(\d+)\]\.(\w+)=([^\n]*)`)
	matches := recordRe.FindAllStringSubmatch(data, -1)

	records := make([]Record, found)
	var record_data RecordData
	record_data.Found = found
	for _, match := range matches {
		index, _ := strconv.Atoi(match[1])
		key := strings.TrimSpace(match[2])
		value := strings.TrimSpace(match[3])
		switch key {
		case "CardName":
			records[index].CardName = value
		case "CardNo":
			records[index].CardNo = value
		case "CreateTime":
			records[index].CreateTime, _ = strconv.ParseInt(value, 10, 64)
		case "Door":
			records[index].Door, _ = strconv.Atoi(value)
		case "Method":
			records[index].Method, _ = strconv.Atoi(value)
		case "RecNo":
			records[index].RecNo, _ = strconv.Atoi(value)
		case "Status":
			records[index].Status, _ = strconv.Atoi(value)
		case "UserID":
			records[index].UserID = value
		}
	}
	for i := 0; i < len(records); i++ {
		record_data.Record = append(record_data.Record, records[len(records)-1-i])
	}
	// fmt.Printf("Found records: %d\n", found)
	// for i, record := range records {
	// 	fmt.Printf("Record %d: %+v\n", i+1, record)
	// }
	return record_data
}
func (h *DH) GetRecord(StartTime, EndTime string) (RecordData, error) {
	var record_data RecordData
	t := digest.NewTransport(h.AdminName, h.AdminPwd)
	c, err := t.Client()
	c.Timeout = time.Second * 10
	if err != nil {
		return record_data, err
	}
	resp, err := c.Get(h.Host + fmt.Sprintf(getRecordUrl, StartTime, EndTime))
	if err != nil {
		return record_data, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return record_data, fmt.Errorf("errorcode:%d", resp.StatusCode)
	}
	s, err := io.ReadAll(resp.Body)
	if err != nil {
		return record_data, err
	}
	record_data = recordFind(string(s))
	return record_data, nil
}
