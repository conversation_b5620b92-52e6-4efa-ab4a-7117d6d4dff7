//公用的结构体定义等

package comm

import "errors"

const (
	DOOR_CLOSED = 0
	DOOR_OPEND  = 1
)

const CONTROLLER_TYPE = "控制器"

var ERR_INIT = errors.New("初始化门控器失败")
var ErrNotSupport = errors.New("平台不支持")

type DoorStatus struct {
	DoorStatus []int `json:"doorStatus"`
}
type LogInfo struct {
	Info   string `json:"info"`
	Time   string `json:"time"`
	CardNo string `json:"cardNo"`
	DoorId int    `json:"doorId"`
}
type DoorList struct {
	CardList []CardDoorId `json:"cardlist"`
}
type CardDoorId struct {
	CardNo string `json:"cardNo"`
	Door   []int  `json:"door"`
}
type HisRecord struct {
	Responsestatusstrg string    `json:"responseStatusStrg"`
	InfoList           []LogInfo `json:"infoList"`
}
