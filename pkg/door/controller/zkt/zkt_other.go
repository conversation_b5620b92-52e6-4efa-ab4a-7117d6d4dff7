//go:build !windows

package controller

import (
	. "tw_platform/pkg/door/controller/comm"
)

type zkt struct {
	DoorNum int
}

func Connect(ip string, port int, doorNum int) *zkt {
	return nil
}
func (z *zkt) CtrDoor(doorid int, way string) error { return nil }

// 获取门的状态
func (z *zkt) GetDoorStatus() (*DoorStatus, error) { return nil, ErrNotSupport }

// 获取历史记录
func (z *zkt) GetDoorcontrollerRecord(startTime, endTime int64) (*HisRecord, error) {
	return nil, ErrNotSupport
}

// 获取卡片列表
func (z *zkt) GetCardList() (*DoorList, error) {
	return nil, ErrNotSupport
}

// 添加卡
func (z *zkt) AddCard(cardNo string, doorids []int) error {
	return ErrNotSupport
}

// 删除卡
func (z *zkt) DelCard(cardNo string) error {
	return ErrNotSupport
}
