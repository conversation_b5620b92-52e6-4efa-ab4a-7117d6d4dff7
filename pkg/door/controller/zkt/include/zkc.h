#ifndef __TW_ZKC_SDK_H__
#define __TW_ZKC_SDK_H__

void * Connect(const char *Parameters); 
void Disconnect(void *handle);
int GetDeviceParam(void *handle, char *Buffer, int BufferSize, const char *Items);
int ControlDevice(void *handle, long OperationID, long Param1, long Param2, long Param3, long Param4, const char *Options);
int GetRTLog(void *handle,char *Buffer, int BufferSize);
int GetDeviceData(void *handle, char *Buffer, int BufferSize, const char *TableName, const char *FieldNames,const char *Filter, const char *Options);
int SetDeviceData(void *handle,const char *TableName,const char *Data,const char *Options);
int DeleteDeviceData(void *handle, const char *TableName,const char *Data,const char *Options);
#endif