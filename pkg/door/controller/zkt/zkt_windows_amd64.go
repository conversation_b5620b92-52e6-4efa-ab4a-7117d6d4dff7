//go:build windows && amd64

package controller

/*
#cgo CFLAGS:  -I./include
#cgo LDFLAGS: -L./ -lplcommpro
#include <stdlib.h>
#include "zkc.h"
*/
import "C"
import (
	"bytes"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"
	. "tw_platform/pkg/door/controller/comm"
	"unsafe"

	"github.com/pkg/errors"
)

type zkt struct {
	h       unsafe.Pointer
	DoorNum int
}

var logIdToText = map[int]string{
	0:   "正常刷卡开门",
	1:   "常开时间段内刷卡",
	2:   "首卡开门(刷卡)",
	3:   "多卡开门(刷卡)",
	4:   "紧急状态密码开门",
	5:   "常开时间段开门",
	6:   "触发联动事件",
	7:   "取消报警",
	8:   "远程开门",
	9:   "远程关门",
	10:  "禁用当天常开时间段",
	11:  "启用当天常开时间段",
	12:  "开启辅助输出",
	13:  "关闭辅助输出",
	14:  "正常按指纹开门",
	15:  "多卡开门(按指纹)",
	16:  "常开时间段内按指纹",
	17:  "卡加指纹开门",
	18:  "首卡开门(按指纹)",
	19:  "首卡开门(卡加指纹)",
	20:  "刷卡间隔太短",
	21:  "门非有效时间段(刷卡)",
	22:  "非法时间段",
	23:  "非法访问",
	24:  "反潜",
	25:  "互锁",
	26:  "多卡验证(刷卡)",
	27:  "卡未注册",
	28:  "门开超时",
	29:  "卡已过有效期",
	30:  "密码错误",
	31:  "按指纹间隔太短",
	32:  "多卡验证(按指纹)",
	33:  "指纹已过有效期",
	34:  "指纹未注册",
	35:  "门非有效时间段(按指纹)",
	36:  "门非有效时间段(按出门按钮)",
	37:  "常开时间段无法关门",
	38:  "卡已挂失",
	39:  "黑名单",
	40:  "多指纹验证失败",
	41:  "验证方式错误",
	42:  "韦根格式错误",
	43:  "后台验证",
	44:  "后台验证失败",
	45:  "后台验证超时",
	46:  "后台验证事件",
	47:  "发送命令失败",
	48:  "多卡开门失败",
	100: "防拆报警",
	101: "胁迫密码开门",
	102: "门被意外打开",
	103: "胁迫指纹开门",
	200: "门已打开",
	201: "门已关闭",
	202: "出门按钮开门",
	203: "多卡开门(卡加指纹)",
	204: "常开时间段结束",
	205: "远程开门常开",
	206: "设备启动",
	207: "密码开门",
	208: "超级用户开门",
	209: "门被锁定",
	210: "消防开启",
	211: "超级用户关门",
	220: "辅助输入点断开",
	221: "辅助输入点短路",
	222: "后台验证成功",
	223: "后台验证网络不稳开门",
	224: "后台重新启用反潜",
}
var zkcErrCode = map[int]string{
	-1:   "命令发送失败",
	-2:   "命令没有回应",
	-3:   "需要的缓存不足",
	-4:   "解压失败",
	-5:   "读取数据长度不对",
	-6:   "解压的长度和期望的长度不一致",
	-7:   "命令重复",
	-8:   "连接尚未授权",
	-9:   "数据错误,CRC校验失败",
	-10:  "数据错误,PullSDK无法解析",
	-11:  "数据参数错误",
	-12:  "命令执行错误",
	-13:  "命令错误，没有此命令",
	-14:  "通讯密码错误",
	-15:  "写文件失败",
	-16:  "读文件失败",
	-17:  "文件不存在",
	-18:  "设备空间不足",
	-19:  "校验和出错",
	-20:  "接受到的数据长度与给出的数据长度不一致",
	-21:  "设备中，没有设置平台参数",
	-22:  "固件升级，传来的固件的平台与本地的平台不一致",
	-23:  "升级的固件版本比设备中的固件版本老",
	-24:  "升级的文件标识出错",
	-25:  "固件升级,传来的文件名不对,即不是emfw.cfg",
	-26:  "传来的指纹模板长度为0",
	-27:  "传来的指纹pin号错误,找不到此用户",
	-28:  "常开时间段执行开门命令",
	-99:  "未知错误",
	-100: "	表结构不存在",
	-101: "	表结构中，条件字段不存在",
	-102: "	字段总数不一致",
	-103: "	字段排序不一致",
	-104: "	实时事件数据错误",
	-105: "	解析数据时，数据错误",
	-106: "	数据溢出,下发数据超出4M",
	-107: "	获取表结构失败",
	-108: "	无效OPTIONS选项",
	-112: "	PC传入的数据接收缓冲不足",
	-201: "	LoadLibrary失败",
	-202: "	调用接口失败",
	-203: "	通讯初始化失败",
	-206: "	串口代理程序启动失败，原因一般是串口不存在或串口被占用",
	-301: "	获取TCP/IP版本失败",
	-302: "	错误版本号",
	-303: "	获取协议类型失败",
	-304: "	无效SOCKET",
	-305: "	SOCKET错误",
	-306: "	HOST错误",
	-307: "	连接超时",
}

type pinCardInfo map[string]*CardDoorId

type pinAuthDid map[string][]int

const ZKC_UNKNOWN_ERR = "未知错误"

type zkParam struct {
	Ip      string
	Port    int
	doorNum int
}
type cachedZkinfo map[unsafe.Pointer]zkParam

var g_cachedZk = cachedZkinfo{}
var cacheLock sync.Mutex

func Connect(ip string, port int, doorNum int) *zkt {
	connectStr := fmt.Sprintf("protocol=TCP,ipaddress=%s,port=%d,timeout=4000,passwd=", ip, port)
	cCstr := C.CString(connectStr)
	defer C.free(unsafe.Pointer(cCstr))
	//查找缓存，看是否已经初始化
	cacheLock.Lock()
	var delz unsafe.Pointer = nil
	for z, param := range g_cachedZk {
		if param.Ip == ip && param.Port == port && param.doorNum == doorNum {
			cacheLock.Unlock()
			return &zkt{
				h: z,
			}
		} else if param.Ip == ip {
			//存在可能配置失败的情况，需要删除该项
			delz = z
			break
		}
	}
	if delz != nil {
		delete(g_cachedZk, delz)
		DisConnect(delz)
	}

	//没有初始化
	h := C.Connect(cCstr)
	if h == nil {
		cacheLock.Unlock()
		return nil
	}
	g_cachedZk[h] = zkParam{
		Ip:      ip,
		Port:    port,
		doorNum: doorNum,
	}
	cacheLock.Unlock()

	return &zkt{
		h: h,
	}
}
func DisConnect(handle unsafe.Pointer) {
	C.Disconnect(handle)
}
func getErrStr(ret int) error {
	if _, ok := zkcErrCode[int(ret)]; ok {
		return errors.New(zkcErrCode[int(ret)])
	} else {
		return errors.New("ZKC_UNKNOWN_ERR")
	}
}

// 控门
func (z *zkt) CtrDoor(id int, way string) error {
	//defer DisConnect(z.h)
	resv := make([]byte, 1)
	var ret C.int
	switch way {
	case "open":
		ret = C.ControlDevice(z.h, C.long(1), C.long(id), C.long(1), C.long(10), C.long(0), (*C.char)(unsafe.Pointer(&resv[0])))
	case "close":
		ret = C.ControlDevice(z.h, C.long(1), C.long(id), C.long(1), C.long(0), C.long(0), (*C.char)(unsafe.Pointer(&resv[0])))
	case "alwaysopen":
		ret = C.ControlDevice(z.h, C.long(1), C.long(id), C.long(1), C.long(255), C.long(0), (*C.char)(unsafe.Pointer(&resv[0])))
	}
	if ret < 0 {
		return getErrStr(int(ret))
	}
	return nil
}

// 获取门的状态
func (z *zkt) GetDoorStatus() (*DoorStatus, error) {
	//defer DisConnect(z.h)
	doorStatus := []int{}
	for {
		buffer := make([]byte, 256)

		ret := C.GetRTLog(z.h, (*C.char)(unsafe.Pointer(&buffer[0])), 256)
		if ret < 0 {
			return nil, getErrStr(int(ret))
		}
		str := string(buffer)
		strlist := strings.Split(str, "\r\n")
		if len(strlist) != 2 {
			continue
		}
		statusList := strings.Split(strlist[0], ",")
		doorStatusTotal, _ := strconv.Atoi(statusList[1])
		flag := statusList[4]
		if flag != "255" {
			continue
		}
		for i := 0; i < int(z.DoorNum); i++ {
			tstatus := (doorStatusTotal >> (i * 8)) & 0xff
			switch tstatus {
			case 0:
				return nil, errors.New("未设置门磁状态")
			case 1:
				doorStatus = append(doorStatus, 0)
			case 2:
				doorStatus = append(doorStatus, 1)
			default:
				return nil, errors.New(fmt.Sprintf("门状态未知代码:%d", tstatus))
			}
		}
		break
	}
	return &DoorStatus{
		DoorStatus: doorStatus,
	}, nil
}

// 获取历史记录
func (z *zkt) GetDoorcontrollerRecord(startTime, endTime int64) (*HisRecord, error) {
	//defer DisConnect(z.h)
	BUFSIZE := 10 * 1024 * 1024
	buf1 := make([]byte, BUFSIZE)
	logQuery := "DoorID\tEventType\tTime_second\tCardno"
	cStr1 := C.CString(logQuery)
	tbname := C.CString("transaction")
	defer C.free(unsafe.Pointer(cStr1))
	defer C.free(unsafe.Pointer(tbname))
	infolist := &HisRecord{
		Responsestatusstrg: "ok",
		InfoList:           []LogInfo{},
	}
	resv := make([]byte, 1)
	ret := C.GetDeviceData(z.h, (*C.char)(unsafe.Pointer(&buf1[0])), C.int(BUFSIZE), tbname, cStr1, (*C.char)(unsafe.Pointer(&resv[0])), (*C.char)(unsafe.Pointer(&resv[0])))
	if ret < 0 {
		return nil, getErrStr(int(ret))
	}
	validIndex := bytes.IndexByte(buf1, 0)
	step1Str := string(buf1[0:validIndex])
	ste1list := strings.Split(step1Str, "\r\n")

	for i := len(ste1list) - 1; i > 0; i-- {
		if ste1list[i] == "" {
			continue
		}
		detail := strings.Split(ste1list[i], ",")
		//获取实际参数
		doorid, err := strconv.Atoi(detail[0])
		if err != nil {
			return nil, errors.New("获取历史记录中的门索引错误:" + detail[0])
		}
		eventType, err := strconv.Atoi(detail[1])
		if err != nil {
			return nil, errors.New("获取历史记录中的事件类型错误:" + detail[1])
		}
		eventLog := ""
		if _, ok := logIdToText[eventType]; ok {
			eventLog = logIdToText[eventType]
		}
		//进行实际的日期转换
		ts := detail[2]
		timeStamp, err := strconv.Atoi(ts)
		if err != nil {
			continue
		}
		record_second := timeStamp % 60
		timeStamp = timeStamp / 60
		record_min := timeStamp % 60
		timeStamp = timeStamp / 60

		record_hour := timeStamp % 24
		timeStamp = timeStamp / 24
		record_day := timeStamp%31 + 1
		timeStamp = timeStamp / 31
		record_mounth := timeStamp%12 + 1
		timeStamp = timeStamp / 12
		record_year := timeStamp + 2000

		ts = fmt.Sprintf("%d-%02d-%02d %02d:%02d:%02d",
			record_year,
			record_mounth,
			record_day,
			record_hour,
			record_min,
			record_second)

		cardNo := ""
		if detail[3] != "0" {
			cardNo = detail[3]
		}

		loc, _ := time.LoadLocation("Local")
		tslayout := "2006-01-02 15:04:05"
		rectimeNum, _ := time.ParseInLocation(tslayout, ts, loc)
		unixts := rectimeNum.Unix()
		if unixts > endTime || unixts < startTime {
			continue
		}
		infolist.InfoList = append(infolist.InfoList, LogInfo{
			Info:   eventLog,
			Time:   ts,
			CardNo: cardNo,
			DoorId: doorid,
		})
	}
	return infolist, nil
}

// 获取卡片列表
func (z *zkt) GetUserAuthDoorId(h unsafe.Pointer) (pinAuthDid, error) {
	authDid := pinAuthDid{}
	BUFSIZE := 10 * 1024 * 1024
	buf1 := make([]byte, BUFSIZE)
	query1 := "Pin\tAuthorizeDoorId"
	cStr1 := C.CString(query1)
	tbname := C.CString("userauthorize")
	defer C.free(unsafe.Pointer(cStr1))
	defer C.free(unsafe.Pointer(tbname))

	resv := make([]byte, 1)
	ret := C.GetDeviceData(h, (*C.char)(unsafe.Pointer(&buf1[0])), C.int(BUFSIZE), tbname, cStr1, (*C.char)(unsafe.Pointer(&resv[0])), (*C.char)(unsafe.Pointer(&resv[0])))
	if ret < 0 {
		return nil, getErrStr(int(ret))
	}
	validIndex := bytes.IndexByte(buf1, 0)
	step1Str := string(buf1[0:validIndex])
	ste1list := strings.Split(step1Str, "\r\n")
	for i := 1; i < len(ste1list); i++ {
		if ste1list[i] == "" {
			break
		}
		pc := strings.Split(ste1list[i], ",")
		pin := pc[0]
		aid, err := strconv.Atoi(pc[1])
		if err != nil {
			authDid[pin] = []int{}
			continue
		}
		authDid[pin] = []int{}
		for i := 0; i < z.DoorNum; i++ {
			subStatus := aid >> i & 0x01
			authDid[pin] = append(authDid[pin], subStatus)
		}

		//if _, ok := code2IDS[aid]; ok {
		//	authDid[pin] = code2IDS[aid]
		//} else {
		//	authDid[pin] = []int{}
		//}
	}

	return authDid, nil
}
func (z *zkt) getCardList(h unsafe.Pointer) (pinCardInfo, error) {
	//获取pin cardNo
	BUFSIZE := 10 * 1024 * 1024
	buf1 := make([]byte, BUFSIZE)
	query1 := "Pin\tCardNo"
	cStr1 := C.CString(query1)
	tbname := C.CString("user")
	defer C.free(unsafe.Pointer(cStr1))
	defer C.free(unsafe.Pointer(tbname))
	retinfo := pinCardInfo{}
	resv := make([]byte, 1)
	ret := C.GetDeviceData(h, (*C.char)(unsafe.Pointer(&buf1[0])), C.int(BUFSIZE), tbname, cStr1, (*C.char)(unsafe.Pointer(&resv[0])), (*C.char)(unsafe.Pointer(&resv[0])))
	if ret < 0 {
		return nil, getErrStr(int(ret))
	}

	validIndex := bytes.IndexByte(buf1, 0)
	step1Str := string(buf1[0:validIndex])
	ste1list := strings.Split(step1Str, "\r\n")
	for i := 1; i < len(ste1list); i++ {
		if ste1list[i] == "" {
			break
		}
		pc := strings.Split(ste1list[i], ",")
		retinfo[pc[0]] = &CardDoorId{
			CardNo: pc[1],
			Door:   []int{},
		}
	}
	//获取卡片对应的门权限
	pinDoorId, err := z.GetUserAuthDoorId(h)
	if err != nil {
		return nil, err
	}

	for pin, doorids := range pinDoorId {
		if _, ok := retinfo[pin]; ok {
			for _, id := range doorids {
				retinfo[pin].Door = append(retinfo[pin].Door, id)
			}
		}
	}
	return retinfo, nil
}
func (z *zkt) GetCardList() (*DoorList, error) {
	//defer DisConnect(z.h)
	//返回卡号+门禁号数组
	clist := &DoorList{
		CardList: []CardDoorId{},
	}
	retinfo, err := z.getCardList(z.h)
	if err != nil {
		return nil, err
	}

	for _, cd := range retinfo {
		clist.CardList = append(clist.CardList, CardDoorId{
			CardNo: cd.CardNo,
			Door:   cd.Door,
		})
	}
	return clist, nil
}

// 添加卡
func (z *zkt) SetCardAuth(pin string, doorids []int) error {
	tbname := C.CString("userauthorize")
	defer C.free(unsafe.Pointer(tbname))
	setNum := 0

	for i := 0; i < len(doorids); i++ {
		setNum |= doorids[i] << i
	}

	//if len(doorids) == 2 {
	//	setNum = doorids[0] << 0 | doorids[1] << 1
	//} else if len(doorids) == 4 {
	//	setNum = doorids[0] <<
	//} else {
	//	return errors.New("门禁数量错误")
	//}

	query1 := fmt.Sprintf("Pin=%s\tAuthorizeDoorId=%d", pin, setNum)
	cStr1 := C.CString(query1)
	defer C.free(unsafe.Pointer(cStr1))
	resv := make([]byte, 1)
	ret := C.SetDeviceData(z.h, tbname, cStr1, (*C.char)(unsafe.Pointer(&resv[0])))
	if ret < 0 {
		return getErrStr(int(ret))
	}
	return nil
}
func (z *zkt) AddCard(cardNo string, doorids []int) error {
	//defer DisConnect(z.h)
	pin := ""
	tbname := C.CString("user")
	defer C.free(unsafe.Pointer(tbname))
	if len(cardNo) >= 9 {
		pin = cardNo[0:9]
	} else {
		pin = cardNo
	}
	query1 := fmt.Sprintf("Pin=%s\tCardNo=%s", pin, cardNo)
	cStr1 := C.CString(query1)
	defer C.free(unsafe.Pointer(cStr1))

	resv := make([]byte, 1)
	ret := C.SetDeviceData(z.h, tbname, cStr1, (*C.char)(unsafe.Pointer(&resv[0])))
	if ret < 0 {
		return getErrStr(int(ret))
	}

	//设置权限列表
	return z.SetCardAuth(pin, doorids)
}

// 删除卡
func (z *zkt) DelCard(cardNo string) error {
	//defer DisConnect(z.h)
	retinfo, err := z.getCardList(z.h)
	if err != nil {
		return err
	}
	delpin := ""
	for pin, cardoor := range retinfo {
		if cardoor.CardNo == cardNo {
			delpin = pin
			break
		}
	}
	if delpin == "" {
		return errors.New("没有找到匹配的卡号")
	}

	//删除卡号对应的pin
	tbname := C.CString("user")
	defer C.free(unsafe.Pointer(tbname))

	query1 := fmt.Sprintf("Pin=%s", delpin)
	cStr1 := C.CString(query1)
	defer C.free(unsafe.Pointer(cStr1))
	resv := make([]byte, 1)
	ret := C.DeleteDeviceData(z.h, tbname, cStr1, (*C.char)(unsafe.Pointer(&resv[0])))
	if ret < 0 {
		return getErrStr(int(ret))
	}
	return nil
}
