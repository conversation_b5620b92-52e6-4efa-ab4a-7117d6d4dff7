package device_capture

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"
	"tw_platform/pkg/model"
	"tw_platform/pkg/system/config"
)

const (
	statusURL = "%s/api/v1/internal/device/status"
)

type DeviceCapture struct {
	hc   *http.Client
	host string
}

func NewDeviceCapture(conf *config.Config) *DeviceCapture {
	return &DeviceCapture{
		host: fmt.Sprintf(
			"http://%s:%d",
			conf.DeviceCapture.Host,
			conf.DeviceCapture.Port,
		),
		hc: &http.Client{
			Timeout: 5 * time.Second,
		},
	}
}

func (s *DeviceCapture) Status() (model.GetDeviceCaptureStatusResp, error) {
	var result model.GetDeviceCaptureStatusResp

	resp, err := s.hc.Get(fmt.Sprintf(statusURL, s.host))
	if err != nil {
		return result, err
	}
	defer resp.Body.Close()

	err = json.NewDecoder(resp.Body).Decode(&result)
	return result, err
}

func (s *DeviceCapture) SetStatus(status model.SetDeviceCaptureStatusReq) error {
	var result model.SetDeviceCaptureStatusResp
	data, err := json.Marshal(status)
	if err != nil {
		return err
	}

	req, err := http.NewRequest(
		http.MethodPost,
		fmt.Sprintf(statusURL, s.host),
		bytes.NewBuffer(data),
	)
	if err != nil {
		return err
	}

	resp, err := s.hc.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	err = json.NewDecoder(resp.Body).Decode(&result)
	if err != nil {
		return err
	}
	if result.Code != "200" {
		return errors.New("set status failure")
	}
	return nil
}
