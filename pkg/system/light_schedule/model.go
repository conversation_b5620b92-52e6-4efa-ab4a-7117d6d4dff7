package light_schedule

import (
	"encoding/json"
	"time"
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/model/request"

	"go.uber.org/zap"
)

type LightScheduleStartup struct{}

type LightScheduleJob struct {
	id int

	dr *repository.DeviceRepo
	lr *repository.LightScheduleRepo

	l *zap.Logger
}

func NewLightScheduleJob(id int, dr *repository.DeviceRepo, lr *repository.LightScheduleRepo, l *zap.Logger) *LightScheduleJob {
	return &LightScheduleJob{
		id: id,
		dr: dr,
		lr: lr,
		l:  l.Named("light_schedule_job:"),
	}
}

func (l *LightScheduleJob) Run() {
	if l.id == 0 {
		return
	}

	var (
		now = time.Now()
	)

	schedule, err := l.lr.First(model.LightSchedule{ID: l.id})
	if err != nil {
		l.l.Warn("get schedule by id failure", zap.Error(err), zap.Int("id", l.id))
		return
	}

	if schedule.Status == model.LightScheduleStatusClosed {
		return
	}
	if schedule.BeginAt != nil && now.Before(*schedule.BeginAt) {
		return
	}
	if schedule.EndAt != nil && now.After(*schedule.EndAt) {
		return
	}

	actions, err := l.lr.ActionsByID(l.id)
	if err != nil {
		l.l.Warn("get actions by id failure", zap.Error(err), zap.Int("id", l.id))
		return
	}

	var (
		payload   = make([]request.DeviceControl, 0)
		detailIDs = make([]int, 0, len(actions))
		logs      = make([]model.LightScheduleLog, 0, len(actions))
	)

	for _, action := range actions {
		payload = append(payload, request.DeviceControl{
			DeviceID: action.DeviceID,
			UnitID:   action.UnitID,
			Value:    action.Value,
		})
		detailIDs = append(detailIDs, action.ID)
		logs = append(logs, model.LightScheduleLog{
			DetailID: action.ID,
			ExecedAt: &now,
		})
	}

	data, err := json.Marshal(payload)
	if err != nil {
		l.l.Warn("light shcedule json marshal failure", zap.Error(err))
		return
	}
	err = l.dr.BatchControl(data)
	if err != nil {
		l.l.Warn("light shcedule batch control device failure", zap.Error(err))
		return
	}

	err = l.lr.CronAfter(detailIDs, logs)
	if err != nil {
		l.l.Warn("light shcedule update execed at failure", zap.Error(err))
	}
}
