package permission

import (
	"fmt"
	"net/http"
	"sync"
	"tw_platform/pkg/model"
)

type Permissions struct {
	mu    sync.RWMutex
	cache map[string]int
}

func NewPermissions(cache map[string]int) *Permissions {
	return &Permissions{
		cache: cache,
	}
}

func (p *Permissions) ID(method, path string) int {
	var (
		m model.PermissionMethod
	)
	switch method {
	case http.MethodGet:
		m = model.PermissionMethodGet
	case http.MethodPost:
		m = model.PermissionMethodPost
	}
	p.mu.RLock()
	defer p.mu.RUnlock()
	return p.cache[fmt.Sprintf("%d_%s", m, path)]
}

func (p *Permissions) Add(id int, method model.PermissionMethod, path string) {
	p.mu.Lock()
	defer p.mu.Unlock()
	p.cache[fmt.Sprintf("%d_%s", method, path)] = id
}

func (p *Permissions) Delete(method model.PermissionMethod, path string) {
	p.mu.Lock()
	defer p.mu.Unlock()
	delete(p.cache, fmt.Sprintf("%d_%s", method, path))
}
