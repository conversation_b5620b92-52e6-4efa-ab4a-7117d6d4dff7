package mqtt

import (
	"encoding/json"
	"strings"
	"tw_platform/pkg/model"

	paho "github.com/eclipse/paho.mqtt.golang"
	"go.uber.org/zap"
)

const (
	projectName = iota
	fid
	eventType
	msgType
)

type MQTTHandler func(fid string, m paho.Message) error
type MQTTCustomHandler func(m paho.Message) error

type MqttModule struct {
	client      paho.Client
	l           *zap.Logger
	qos         byte
	retained    bool
	commonTopic string

	handlers       map[string]MQTTHandler
	customHandlers map[string]MQTTCustomHandler
}

func NewMqttModule(client paho.Client, l *zap.Logger) *MqttModule {
	return &MqttModule{
		client:      client,
		l:           l.Named("mqtt_module"),
		qos:         byte(1),
		retained:    false,
		commonTopic: "tw/+/+/pub",

		handlers:       make(map[string]MQTTHandler),
		customHandlers: make(map[string]MQTTCustomHandler),
	}
}

func (m *MqttModule) Publish(topic model.MqttTopic, payload interface{}) error {
	msg, err := json.Marshal(payload)
	if err != nil {
		m.l.Error("json marshal failed", zap.Error(err), zap.String("topic", topic))
		return err
	}

	token := m.client.Publish(topic, m.qos, m.retained, msg)

	if token.Wait() && token.Error() != nil {
		m.l.Error("publish failed", zap.Error(token.Error()), zap.String("topic", topic))
		return token.Error()
	}
	return nil
}

func (m *MqttModule) Subscribe(topic string, handler MQTTHandler) {
	m.handlers[topic] = handler
}

func (m *MqttModule) SubscribeCustom(topic string, handler MQTTCustomHandler) {
	m.customHandlers[topic] = handler
}

func (m *MqttModule) Run() error {
	if m.client == nil {
		return nil
	}
	token := m.client.Subscribe(m.commonTopic, m.qos, m.serverMux)
	if token.Wait() && token.Error() != nil {
		m.l.Error("subscribe failed", zap.Error(token.Error()), zap.String("topic", m.commonTopic))
		return token.Error()
	}

	for topic := range m.customHandlers {
		token = m.client.Subscribe(topic, m.qos, m.customServerMux)
		if token.Wait() && token.Error() != nil {
			m.l.Error("subscribe failed", zap.Error(token.Error()), zap.String("topic", topic))
			return token.Error()
		}
	}

	return nil
}

func (m *MqttModule) serverMux(client paho.Client, message paho.Message) {
	topicArr := strings.Split(message.Topic(), "/")
	f, exist := m.handlers[topicArr[eventType]]
	if !exist {
		m.l.Warn("invalid topic", zap.String("topic", message.Topic()))
		return
	}
	defer func() {
		if r := recover(); r != nil {
			m.l.Error("recover from a panic", zap.Any("recover", r), zap.String("topic", message.Topic()))
		}
	}()

	err := f(topicArr[fid], message)
	if err != nil {
		m.l.Error("handle message failed", zap.Error(err), zap.String("topic", message.Topic()))
		return
	}
	m.l.Debug("handle message success", zap.String("topic", message.Topic()))
}

func (m *MqttModule) customServerMux(client paho.Client, message paho.Message) {
	f, exist := m.customHandlers[message.Topic()]
	if !exist {
		m.l.Warn("invalid topic", zap.String("topic", message.Topic()))
		return
	}

	defer func() {
		if r := recover(); r != nil {
			m.l.Error("recover from a panic", zap.Any("recover", r), zap.String("topic", message.Topic()))
		}
	}()

	err := f(message)
	if err != nil {
		m.l.Error("handle message failed", zap.Error(err), zap.String("topic", message.Topic()))
	}
}

func (m *MqttModule) Close(quiesce uint) {
	if m.client == nil {
		return
	}
	m.client.Disconnect(quiesce)
}
