package dongle

/*
#cgo CFLAGS:  -I./inc
#cgo LDFLAGS: -L./lib/loong_linux -lViKey
#include <stdlib.h>
#include <stdio.h>
#include "vikey_linux_loong64.h"
*/
import "C"
import (
	"fmt"
	"unsafe"
)

// 获取加密狗数量
func getKeyNum() int {
	var keyNum C.DWORD
	if C.VikeyFind((*C.DWORD)(unsafe.Pointer(&keyNum))) != 0 {
		return -1
	}
	return int(keyNum)
}

// 获取硬件ID
func GetKeyHardId() string {
	var hid C.DWORD
	keyNum := getKeyNum()
	if keyNum == -1 {
		return ""
	}
	if C.VikeyGetHID(0, (*C.DWORD)(unsafe.Pointer(&hid))) != 0 {
		//save log
		return ""
	}
	keyid := uint32(hid)
	keystr := fmt.Sprintf("%d", keyid)
	//fmt.Printf("keystr: %v\n", keystr)
	return keystr
}
