package config

type Config struct {
	Mysql           Mysql           `yaml:"mysql"`
	Redis           Redis           `yaml:"redis"`
	WebServer       WebServer       `yaml:"web-server"`
	InfluxDB        InfluxDB        `yaml:"influxdb"`
	PostgreSQL      PostgreSQL      `yaml:"postgresql"`
	MQTT            MQTT            `yaml:"mqtt"`
	JWT             JWT             `yaml:"jwt"`
	DeviceCapture   DeviceCapture   `yaml:"device-capture"`
	VideoRtmp       VideoRtmp       `yaml:"video-rtmp"`
	VideoResolution VideoResolution `yaml:"video-resolution"`
	Qywx            Qywx            `yaml:"qywx"`
}

type Mysql struct {
	Name     string `yaml:"name"`
	Password string `yaml:"password"`
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	DB       string `yaml:"db"`
}

type PostgreSQL struct {
	Name     string `yaml:"name"`
	Password string `yaml:"password"`
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	DB       string `yaml:"db"`
}

type Redis struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
	DB       int    `yaml:"db"`
}

type WebServer struct {
	Listen string `yaml:"listen"`
	Port   int    `yaml:"port"`
}

type InfluxDB struct {
	Addr  string `yaml:"addr"`
	Token string `yaml:"token"`
}

type MQTT struct {
	URI      string `yaml:"uri"`
	Name     string `yaml:"name"`
	Password string `yaml:"password"`
	ClientID string `yaml:"client-id"`
}

type JWT struct {
	SecretKey   string `yaml:"secret_key"`
	ExpiredTime int    `yaml:"expired_time"`
}

type DeviceCapture struct {
	Host string `yaml:"host"`
	Port int    `yaml:"port"`
}

type VideoRtmp struct {
	Addr string `yaml:"addr"`
}

type VideoResolution struct {
	Resolution string `yaml:"resolution"`
}

type Qywx struct {
	CorpID string `yaml:"corp_id"`
	//通讯录同步
	Secret string `yaml:"secret"`
	//应用密钥
	AppSecret string `yaml:"app_secret"`
}
