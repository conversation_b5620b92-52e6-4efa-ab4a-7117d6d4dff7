package role_permission

import (
	"fmt"
	"sync"
	"tw_platform/pkg/model"

	"go.uber.org/zap"
)

type RolePermissions struct {
	mu              sync.RWMutex
	rolePermissions map[int]map[string]int

	l *zap.Logger
}

func NewRolePermissions(rps map[int]map[string]int, l *zap.Logger) *RolePermissions {
	return &RolePermissions{
		rolePermissions: rps,
		l:               l,
	}
}

func (gp *RolePermissions) Validate(rid int, path string, method model.PermissionMethod) bool {
	gp.mu.RLock()
	defer gp.mu.RUnlock()
	_, ok := gp.rolePermissions[rid][fmt.Sprintf("%d_%s", method, path)]
	if !ok {
		gp.l.Warn("forbidden", zap.Int("rid", rid), zap.String("path", path), zap.Uint8("method", uint8(method)))
	}
	return ok
}

func (gp *RolePermissions) Replace(rid int, ids []int) error {
	var (
		rolePermissions = make([]model.Permission, 0, len(ids))
	)

	gp.mu.Lock()
	defer gp.mu.Unlock()
	gp.rolePermissions[rid] = make(map[string]int)
	for _, permission := range rolePermissions {
		key := fmt.Sprintf("%d_%s", permission.Method, permission.Path)
		gp.rolePermissions[rid][key] = permission.ID
	}

	return nil
}
