package standby

import (
	"fmt"
	"sync"
	"time"

	"tw_platform/pkg/model"
	"tw_platform/pkg/system/device_capture"
	"tw_platform/pkg/utils"

	"gorm.io/gorm"
)

type StandbyStats struct {
	enabled bool
	role    model.StandbyType
	ip      string
	port    string
	isError bool
	sync.RWMutex

	dc *device_capture.DeviceCapture
}

func NewStandbyStats(db *gorm.DB, dc *device_capture.DeviceCapture) *StandbyStats {
	var (
		standby model.Standby
		err     error
	)
	err = db.Model(standby).
		Where("id = ?", 1).
		First(&standby).
		Error
	if err != nil {
		standby.Status = model.StandbyStatusDisabled
	}

	return &StandbyStats{
		enabled: standby.Status == model.StandbyStatusEnabled,
		role:    standby.Type,
		ip:      standby.IP,
		port:    standby.Port,

		dc: dc,
	}
}

func (ss *StandbyStats) Enabled() bool {
	ss.RLock()
	defer ss.RUnlock()
	return ss.enabled
}

func (ss *StandbyStats) Role() model.StandbyType {
	ss.RLock()
	defer ss.RUnlock()
	return ss.role
}

func (ss *StandbyStats) Update(standby model.Standby) error {
	ss.Lock()
	defer ss.Unlock()
	ss.enabled = standby.Status == model.StandbyStatusEnabled
	ss.role = standby.Type

	if !ss.enabled {
		return nil
	}

	switch standby.Type {
	case model.StandbyTypeMaster:
		ss.dc.SetStatus(model.SetDeviceCaptureStatusReq{
			Status: model.DeviceCaptureStatusOn,
		})
	case model.StandbyTypeSlave:
		ss.dc.SetStatus(model.SetDeviceCaptureStatusReq{
			Status: model.DeviceCaptureStatusOff,
		})
	default:
	}

	return nil
}

func (ss *StandbyStats) Start() {
	ticker := time.NewTicker(3 * time.Second)

	for {
		if !ss.Enabled() ||
			(ss.Role() == model.StandbyTypeMaster && !ss.isError) {
			<-ticker.C
			continue
		}

		if ss.masterStats() {
			//恢复
			if ss.isError {
				ss.Update(model.Standby{
					Type:   model.StandbyTypeSlave,
					Status: model.StandbyStatusEnabled,
				})

				ss.isError = false
			}
			<-ticker.C
			continue
		}
		//非首次
		if !ss.isError {
			ss.Update(model.Standby{
				Type:   model.StandbyTypeMaster,
				Status: model.StandbyStatusEnabled,
			})
			ss.isError = true
		}

		<-ticker.C
	}
}

func (ss *StandbyStats) masterStats() bool {
	for i := 0; i < 3; i++ {
		if utils.IsAccept(ss.ip, ss.port) {
			return true
		}
		time.Sleep(time.Second)
	}

	return false
}

func (ss *StandbyStats) Host() string {
	return fmt.Sprintf("http://%s:%s", ss.ip, ss.port)
}
