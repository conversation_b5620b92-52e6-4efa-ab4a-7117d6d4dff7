package initialize

import (
	"fmt"
	"tw_platform/pkg/system/config"

	extraClausePlugin "github.com/WinterYukky/gorm-extra-clause-plugin"
	"go.uber.org/zap"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func NewPgSQL(config *config.Config, logger *zap.Logger) *gorm.DB {
	gormLogger := newGormLogger(logger)
	dsn := fmt.Sprintf(
		"host=%s user=%s password=%s dbname=%s port=%d sslmode=disable TimeZone=Asia/Shanghai",
		config.PostgreSQL.Host,
		config.PostgreSQL.Name,
		config.PostgreSQL.Password,
		config.PostgreSQL.DB,
		config.PostgreSQL.Port,
	)
	db, err := gorm.Open(
		postgres.Open(dsn),
		&gorm.Config{
			Logger:         gormLogger,
			TranslateError: true,
		},
	)
	if err != nil {
		panic(err)
	}
	sqlDB, err := db.DB()
	if err != nil {
		panic(err)
	}

	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	db.Use(extraClausePlugin.New())

	return db
}
