package initialize

import (
	"fmt"
	"tw_platform/pkg/model"
	"tw_platform/pkg/system/permission"
	"tw_platform/pkg/system/table_name"

	"gorm.io/gorm"
)

func NewPermissions(
	db *gorm.DB,
	_ *table_name.MigrateInit,
) *permission.Permissions {
	var (
		caches = make(map[string]int)
	)
	var ps []model.Permission
	err := db.Model(model.Permission{}).Find(&ps).Error
	if err != nil {
		panic(err)
	}

	for _, p := range ps {
		key := fmt.Sprintf("%d_%s", p.Method, p.Path)
		caches[key] = p.ID
	}
	return permission.NewPermissions(caches)
}
