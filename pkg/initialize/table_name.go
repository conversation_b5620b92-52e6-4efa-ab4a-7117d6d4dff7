package initialize

import (
	"tw_platform/pkg/model"
	"tw_platform/pkg/system/table_name"
)

func NewTables() *table_name.Tables {
	return &table_name.Tables{
		model.Alarm{}.TableName():                  make([]model.Alarm, 0),
		model.AlarmGroup{}.TableName():             make([]model.AlarmGroup, 0),
		model.AlarmGroupDevice{}.TableName():       make([]model.AlarmGroupDevice, 0),
		model.AlarmGroupNotification{}.TableName(): make([]model.AlarmGroupNotification, 0),
		model.AlarmLog{}.TableName():               make([]model.AlarmLog, 0),
		new(model.AlarmTimeLine).TableName():       make([]model.AlarmTimeLine, 0),
		model.Area{}.TableName():                   make([]model.Area, 0),
		model.AreaDefaultSceneType{}.TableName():   make([]model.AreaDefaultSceneType, 0),
		model.AreaGateway{}.TableName():            make([]model.AreaGateway, 0),
		model.AreaRelation{}.TableName():           make([]model.AreaRelation, 0),
		model.AreaScene{}.TableName():              make([]model.AreaScene, 0),
		model.AreaTypeCount{}.TableName():          make([]model.AreaTypeCount, 0),
		model.Asset{}.TableName():                  make([]model.Asset, 0),
		model.BusinessType{}.TableName():           make([]model.BusinessType, 0),
		model.BusinessTypeRelation{}.TableName():   make([]model.BusinessTypeRelation, 0),
		model.Cabinet{}.TableName():                make([]model.Cabinet, 0),
		model.Config{}.TableName():                 make([]model.Config, 0),
		model.CronJob{}.TableName():                make([]model.CronJob, 0),
		model.CronTask{}.TableName():               make([]model.CronTask, 0),
		model.CronTaskConfig{}.TableName():         make([]model.CronTaskConfig, 0),
		model.Department{}.TableName():             make([]model.Department, 0),
		model.DepartmentDevice{}.TableName():       make([]model.DepartmentDevice, 0),
		model.DepartmentGateway{}.TableName():      make([]model.DepartmentGateway, 0),
		model.DepartmentRelation{}.TableName():     make([]model.DepartmentRelation, 0),
		model.DepartmentTypeCount{}.TableName():    make([]model.DepartmentTypeCount, 0),
		model.Device{}.TableName():                 make([]model.Device, 0),
		model.DeviceAlarmBinding{}.TableName():     make([]model.DeviceAlarmBinding, 0),
		model.Driver{}.TableName():                 make([]model.Driver, 0),
		model.EventTask{}.TableName():              make([]model.EventTask, 0),
		model.EventTaskConfig{}.TableName():        make([]model.EventTaskConfig, 0),
		model.ExportAlarm{}.TableName():            make([]model.ExportAlarm, 0),
		model.Function{}.TableName():               make([]model.Function, 0),
		model.Gateway{}.TableName():                make([]model.Gateway, 0),
		model.GatewayAlarmBinding{}.TableName():    make([]model.GatewayAlarmBinding, 0),
		model.GatewayPort{}.TableName():            make([]model.GatewayPort, 0),
		model.HistoryAlarm{}.TableName():           make([]model.HistoryAlarm, 0),
		model.HistoryAlarmTicket{}.TableName():     make([]model.HistoryAlarmTicket, 0),
		model.Inspect{}.TableName():                make([]model.Inspect, 0),
		model.InspectExecuteUser{}.TableName():     make([]model.InspectExecuteUser, 0),
		model.InspectLeadingUser{}.TableName():     make([]model.InspectLeadingUser, 0),
		model.InspectPlan{}.TableName():            make([]model.InspectPlan, 0),
		model.InspectPlanImage{}.TableName():       make([]model.InspectPlanImage, 0),
		model.LightSchedule{}.TableName():          make([]model.LightSchedule, 0),
		model.LightScheduleDetail{}.TableName():    make([]model.LightScheduleDetail, 0),
		model.LightScheduleLog{}.TableName():       make([]model.LightScheduleLog, 0),
		model.Linkage{}.TableName():                make([]model.Linkage, 0),
		model.LinkageCapture{}.TableName():         make([]model.LinkageCapture, 0),
		model.LinkageData{}.TableName():            make([]model.LinkageData, 0),
		model.Maintain{}.TableName():               make([]model.Maintain, 0),
		model.MaintainPlan{}.TableName():           make([]model.MaintainPlan, 0),
		model.Menu{}.TableName():                   make([]model.Menu, 0),
		model.NoticeSystemConfig{}.TableName():     make([]model.NoticeSystemConfig, 0),
		model.NotificationGroup{}.TableName():      make([]model.NotificationGroup, 0),
		model.NotificationUsers{}.TableName():      make([]model.NotificationUsers, 0),
		model.Opcua{}.TableName():                  make([]model.Opcua, 0),
		model.OperationLog{}.TableName():           make([]model.OperationLog, 0),
		model.Permission{}.TableName():             make([]model.Permission, 0),
		model.RegisterRepo{}.TableName():           make([]model.RegisterRepo, 0),
		new(model.Role).TableName():                make([]model.Role, 0),
		new(model.RoleMenu).TableName():            make([]model.RoleMenu, 0),
		model.RolePermission{}.TableName():         make([]model.RolePermission, 0),
		model.SnmpTrap{}.TableName():               make([]model.SnmpTrap, 0),
		model.Standby{}.TableName():                make([]model.Standby, 0),
		model.SystemInfo{}.TableName():             make([]model.SystemInfo, 0),
		model.Ticket{}.TableName():                 make([]model.Ticket, 0),
		model.TicketTimeLine{}.TableName():         make([]model.TicketTimeLine, 0),
		model.User{}.TableName():                   make([]model.User, 0),
		model.UserArea{}.TableName():               make([]model.UserArea, 0),
		model.UserBusinessType{}.TableName():       make([]model.UserBusinessType, 0),
		model.UserDepartment{}.TableName():         make([]model.UserDepartment, 0),
		model.Video{}.TableName():                  make([]model.Video, 0),
		model.VirtualDevice{}.TableName():          make([]model.VirtualDevice, 0),
	}
}
