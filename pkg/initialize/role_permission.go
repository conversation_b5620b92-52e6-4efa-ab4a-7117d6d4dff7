package initialize

import (
	"fmt"
	"tw_platform/pkg/model"
	"tw_platform/pkg/system/role_permission"
	"tw_platform/pkg/system/table_name"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

func NewRolePermissions(
	db *gorm.DB,
	l *zap.Logger,
	_ *table_name.MigrateInit,
) *role_permission.RolePermissions {
	var (
		rps             []model.RoleWithPermissions
		rolePermissions = make(map[int]map[string]int)
	)

	err := db.Model(model.Role{}).
		Preload("Permissions").
		Find(&rps).
		Error
	if err != nil {
		panic(err)
	}

	for _, rp := range rps {
		rolePermissions[rp.ID] = make(map[string]int)
		for _, permission := range rp.Permissions {
			key := fmt.Sprintf("%d_%s", permission.Method, permission.Path)
			rolePermissions[rp.ID][key] = permission.ID
		}
	}

	return role_permission.NewRolePermissions(rolePermissions, l)
}
