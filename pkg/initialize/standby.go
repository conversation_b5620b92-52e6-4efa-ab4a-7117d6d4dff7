package initialize

import (
	"tw_platform/pkg/system/device_capture"
	"tw_platform/pkg/system/standby"
	"tw_platform/pkg/system/table_name"
	"tw_platform/pkg/utils"

	"gorm.io/gorm"
)

func NewStandbyStats(
	db *gorm.DB,
	dc *device_capture.DeviceCapture,
	_ *table_name.MigrateInit,
) *standby.StandbyStats {
	standby := standby.NewStandbyStats(db, dc)

	go standby.Start()

	return standby
}

func getMasterStats(ip, port string) bool {
	for i := 0; i < 3; i++ {
		if utils.IsAccept(ip, port) {
			return true
		}
	}

	return false
}
