package initialize

import (
	"tw_platform/internal/repository"
	"tw_platform/pkg/model"
	"tw_platform/pkg/system/crontab"
	"tw_platform/pkg/system/light_schedule"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

func NewLightScheduleStartup(
	db *gorm.DB,
	c *crontab.Crontab,
	dr *repository.DeviceRepo,
	lr *repository.LightScheduleRepo,
	l *zap.Logger,
) *light_schedule.LightScheduleStartup {
	var (
		schedules = make([]model.LightSchedule, 0)
	)
	err := db.Model(model.LightSchedule{}).Find(&schedules).Error
	if err != nil {
		l.Warn("get light schedules failure", zap.Error(err))
		return nil
	}
	for _, schedule := range schedules {
		if schedule.Status != model.LightScheduleStatusActive {
			continue
		}
		job := light_schedule.NewLightScheduleJob(schedule.ID, dr, lr, l)
		enrtyID, err := c.<PERSON>ronWithSeconds.AddJob(schedule.Cron, job)
		if err != nil {
			l.Warn("add light schedule job failure", zap.Error(err), zap.Int("id", schedule.ID))
			continue
		}
		err = db.Model(model.LightSchedule{}).Where("id = ?", schedule.ID).Update("cron_id", int(enrtyID)).Error
		if err != nil {
			l.Warn("update light schedule cron id failure", zap.Error(err), zap.Int("id", schedule.ID))
		}
	}
	return &light_schedule.LightScheduleStartup{}
}
