package initialize

import (
	"context"
	"fmt"
	"time"
	"tw_platform/pkg/system/config"

	"github.com/redis/go-redis/v9"
)

func NewRedis(config *config.Config) *redis.Client {
	rdb := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", config.Redis.Host, config.Redis.Port),
		Username: config.Redis.Username,
		Password: config.Redis.Password,
		DB:       config.Redis.DB,
	})
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		panic(err)
	}
	rdb.Subscribe(ctx, "tw_platform").Channel()
	return rdb
}
