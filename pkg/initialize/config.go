package initialize

import (
	"flag"
	"os"
	"tw_platform/pkg/system/config"

	"gopkg.in/yaml.v3"
)

func NewConfig() *config.Config {
	var conf config.Config

	path := flag.String("conf", "./config.yaml", "yaml config file path")
	flag.Parse()

	fd, err := os.Open(*path)
	if err != nil {
		panic(err)
	}
	err = yaml.NewDecoder(fd).Decode(&conf)
	if err != nil {
		panic(err)
	}

	return &conf
}

func NewConfigWithPath(path string) *config.Config {
	var conf config.Config
	fd, err := os.Open(path)
	if err != nil {
		panic(err)
	}
	err = yaml.NewDecoder(fd).Decode(&conf)
	if err != nil {
		panic(err)
	}

	return &conf
}

func NewConfigCustom() *config.Config {
	return NewConfigWithPath("../../config.yaml")
}
