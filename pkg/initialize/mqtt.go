package initialize

import (
	"errors"
	"tw_platform/pkg/system/config"
	"tw_platform/pkg/system/mqtt"

	paho "github.com/eclipse/paho.mqtt.golang"
	"go.uber.org/zap"
)

func NewMQTT(config *config.Config, l *zap.Logger) *mqtt.MqttModule {
	client, err := newMQTT(config)
	// mqtt连接失败不打断启动流程
	if err != nil {
		l.Error("new mqtt client failed", zap.Error(err))
	}
	return mqtt.NewMqttModule(client, l)
}

func newMQTT(config *config.Config) (paho.Client, error) {
	conf := config.MQTT
	options := paho.NewClientOptions().
		AddBroker(conf.URI).
		SetAutoReconnect(true)

	if conf.Name != "" && conf.Password != "" {
		options = options.SetUsername(conf.Name).
			SetPassword(conf.Password)
	}

	if conf.ClientID != "" {
		options = options.SetClientID(conf.ClientID)
	}
	client := paho.NewClient(options)
	token := client.Connect()
	if token.Wait() && token.Error() != nil {
		return nil, token.Error()
	}

	if !client.IsConnected() {
		return nil, errors.New("mqtt is unconnected")
	}
	return client, nil
}
