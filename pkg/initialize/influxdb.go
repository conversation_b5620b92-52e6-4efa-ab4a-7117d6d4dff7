package initialize

import (
	"context"
	"time"
	"tw_platform/pkg/system/config"
	"tw_platform/pkg/system/influxdb"

	influxdb2 "github.com/influxdata/influxdb-client-go/v2"
)

const (
	org    = "taiwu"
	bucket = "tw_platform"
)

func NewInfluxDB(config *config.Config) *influxdb.Client {
	var (
		client = influxdb2.NewClient(
			config.InfluxDB.Addr,
			config.InfluxDB.Token,
		)
		ctx, cancel = context.WithTimeout(context.Background(), 5*time.Second)
	)
	defer cancel()
	ok, err := client.Ping(ctx)
	if err != nil {
		panic(err)
	}
	if !ok {
		panic("influxdb is unconnected")
	}

	return &influxdb.Client{
		Write:  client.WriteAPIBlocking(org, bucket),
		Query:  client.QueryAPI(org),
		Delete: client.DeleteAPI(),
	}
}
