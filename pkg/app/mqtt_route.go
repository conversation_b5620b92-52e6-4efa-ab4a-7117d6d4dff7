package app

import (
	"tw_platform/internal/controller"
	"tw_platform/pkg/system/mqtt"
)

func newMQTTRoute(
	m *mqtt.MqttModule,
	gc *controller.GatewayController,
	dc *controller.DeviceController,
) error {
	m.Subscribe("register_internal", gc.RegisterPceM)
	m.Subscribe("data_internal", dc.ReportDataInternal)
	m.Subscribe("resume", dc.ResumeReportData)
	m.Subscribe("replace", gc.Replace)
	m.Subscribe("ntp", gc.NTP)

	m.Subscribe("register", gc.Register)
	m.Subscribe("data", dc.ReportDeviceData)
	m.Subscribe("alarm", dc.ReportAlarm)

	// err := m.Run()
	// return err
	return nil
}
