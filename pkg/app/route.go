package app

import (
	"net/http"
	"tw_platform/internal/route"
	"tw_platform/pkg/middleware"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func newRoute(app *App) {
	app.engine.Use(
		gin.Recovery(),
		middleware.Cors(),
	)
	// 区域场景 静态资源
	app.engine.StaticFS("/static", http.Dir("./static"))

	api := app.engine.Group(
		"/api",
		middleware.HttpTraceLog(app.logger.Named("http")),
	)

	route.NewNoAuthRouter(
		api,
		app.userController,
		app.systemController,
	)

	auth := api.Group(
		"/",
		middleware.CheckToken(app.config.JWT, app.redis),
		middleware.Standby(app.ss, app.logger),
		//todo 演示暂时使用
		middleware.Visitor(),
		middleware.HttpOperationLog(app.db, app.logger, app.permissions),
	)

	outside := api.Group(
		"/",
		middleware.ExtraToken(),
		middleware.HttpOperationLog(app.db, app.logger, app.permissions),
	)

	// api.Use(middleware.CheckExpireMiddle(app.dc))
	route.NewUserRouter(auth, app.userController)
	route.NewVideoRouter(auth, app.videoController)
	route.NewDoorRouter(auth, app.doorController)
	route.NewAreaRouter(auth, app.areaController)
	route.NewDriverRouter(auth, app.driverController)
	route.NewDeviceRouter(auth, outside, app.deviceController, app.dc)
	route.NewMenuRouter(auth, app.menuController)
	route.NewBusinessTypeRouter(auth, app.businessTypeController)
	route.NewGatewayRouter(auth, app.gatewayController)
	route.NewDepartmentRouter(auth, app.departmentController)
	route.NewRoleRouter(auth, app.roleController)
	route.NewLinkageRouter(auth, app.linkageController)
	route.NewNotificationGroupRouter(auth, app.notificationGroupController)
	route.NewSnmpTrapRouter(auth, app.snmptrapController)
	route.NewCronTaskRouter(auth, app.crontaskController)
	route.NewAlarmGroupRouter(auth, app.alarmGroupServiceController)
	route.NewSystemRouter(auth, app.systemController)
	route.NewAssetRouter(auth, app.assetController)
	route.NewAlarmRouter(auth, api, outside, app.alarmController)
	route.NewRegisterRouter(auth, app.registerController)
	route.NewTicketRouter(auth, outside, app.ticketController)
	route.NewEventTaskRouter(auth, app.eventtaskController)
	route.NewVirtualVariableRouter(auth, app.virtualvariableController)
	route.NewInspectRouter(auth, app.inspectController)
	route.NewLogRouter(auth, app.logController)
	route.NewPermissionRouter(auth, app.permissionController)
	route.NewStandbyRouter(auth, api, app.standbyController)
	route.NewMaintainRouter(auth, app.maintainController)
	route.NewMediaMtxRouter(auth, app.mediamtxController)
	route.NewLinkageCaptureRouter(auth, app.linkageCaptureController)
	route.NewLightScheduleRouter(auth, app.lightScheduleController)
	route.NewCronjobRoute(api, app.cronjobController)

	// 路由调用在上方
	for _, v := range app.engine.Routes() {
		app.logger.Debug("route", zap.String("method", v.Method), zap.String("path", v.Path))
	}

}
