package app

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"
	"tw_platform/internal/controller"
	"tw_platform/internal/route"
	"tw_platform/internal/rpc"
	"tw_platform/internal/service"
	"tw_platform/pkg/system/config"
	"tw_platform/pkg/system/crontab"
	"tw_platform/pkg/system/device_capture"
	"tw_platform/pkg/system/influxdb"
	"tw_platform/pkg/system/light_schedule"
	"tw_platform/pkg/system/mqtt"
	"tw_platform/pkg/system/permission"
	"tw_platform/pkg/system/role_permission"
	"tw_platform/pkg/system/standby"
	"tw_platform/pkg/system/static_path"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type App struct {
	config     *config.Config
	engine     *gin.Engine
	logger     *zap.Logger
	redis      *redis.Client
	influxDB   *influxdb.Client
	crontab    *crontab.Crontab
	staticPath static_path.StaticPaths
	db         *gorm.DB

	mqttModule      *mqtt.MqttModule
	rolePermissions *role_permission.RolePermissions
	permissions     *permission.Permissions
	dc              *device_capture.DeviceCapture
	ss              *standby.StandbyStats

	userController              *controller.UserController
	areaController              *controller.AreaController
	driverController            *controller.DriverController
	deviceController            *controller.DeviceController
	menuController              *controller.MenuController
	roleController              *controller.RoleController
	businessTypeController      *controller.BusinessTypeController
	videoController             *controller.VideoController
	doorController              *controller.DoorController
	gatewayController           *controller.GatewayController
	departmentController        *controller.DepartmentController
	linkageController           *controller.LinkageController
	notificationGroupController *controller.NotifacationGroupController
	snmptrapController          *controller.SnmpTrapController
	crontaskController          *controller.CronTaskController
	cronService                 *service.CronService
	registerController          *controller.RegisterController
	alarmGroupServiceController *controller.AlarmGroupController
	cycleCheckRegService        *service.RegisterService
	systemController            *controller.SystemController
	assetController             *controller.AssetController
	alarmController             *controller.AlarmController
	ticketController            *controller.TicketController
	eventtaskController         *controller.EventTaskController
	virtualvariableController   *controller.VirtualVariableController
	inspectController           *controller.InspectController
	logController               *controller.LogController
	permissionController        *controller.PermissionController
	standbyController           *controller.StandbyController
	maintainController          *controller.MaintainController
	mediamtxController          *controller.MediaMtxController
	linkageCaptureController    *controller.LinkageCaptureController
	lightScheduleController     *controller.LightScheduleController
	cronjobController           *controller.CronjobController
	securityRpc                 *rpc.SecurityRpc
	energyRpc                   *rpc.EnergyRpc

	lightScheduleStartup *light_schedule.LightScheduleStartup
}

func NewApp() *App {
	gin.SetMode(gin.ReleaseMode)
	engine := gin.New()
	return InitializeApp(engine)
}

func (app *App) Run() error {
	//路由入口
	newRoute(app)

	newRPCRoute(app)

	var (
		chErr  = make(chan error)
		chSig  = make(chan os.Signal, 1)
		server = &http.Server{
			Addr:              fmt.Sprintf("%s:%d", app.config.WebServer.Listen, app.config.WebServer.Port),
			Handler:           app.engine.Handler(),
			ReadHeaderTimeout: 120 * time.Second,
			WriteTimeout:      120 * time.Second,
		}
	)
	signal.Notify(chSig, syscall.SIGTERM, syscall.SIGQUIT, syscall.SIGINT)

	go func() {
		chErr <- server.ListenAndServe()
	}()

	app.logger.Info("server start...")

	select {
	case err := <-chErr:
		app.logger.Error("error cause server stoping", zap.Error(err))
	case <-chSig:
		app.logger.Warn("get a signal to stop server")

		ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
		defer cancel()

		server.Shutdown(ctx)

		app.mqttModule.Close(5 * 1000)
	}
	return nil
}
func (app *App) RunMQTT() error {
	return newMQTTRoute(
		app.mqttModule,
		app.gatewayController,
		app.deviceController,
	)
}

func (app *App) RunCron() error {
	route.NewCronJobs(app.crontab, app.cronService)

	go app.crontab.Cron.Run()
	go app.crontab.CronWithSeconds.Run()
	return nil
}

func (app *App) RunCheckRegister() {
	app.cycleCheckRegService.CheckPermission()
}
