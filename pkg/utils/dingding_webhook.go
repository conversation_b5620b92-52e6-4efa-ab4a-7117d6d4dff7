package utils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"time"
)

/*
钉钉群机器人报警
*/
type DingDIngWebhookMsg struct {
	Msgtype string `json:"msgtype"`
	Text    struct {
		Content string `json:"content"`
	} `json:"text"`
}
type dingdingWebhookResp struct {
	ErrCode int    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
}

func SendDingDIngWebhookMsg(url, message string) error {
	var (
		response dingdingWebhookResp
		msg      DingDIngWebhookMsg
	)
	msg.Msgtype = "text"
	msg.Text.Content = message
	jc, err := json.Marshal(msg)
	if err != nil {
		return err
	}
	client := &http.Client{
		Timeout: 3 * time.Second,
	}
	reqest, err := http.NewRequest(
		"POST",
		url,
		bytes.NewReader(jc),
	)
	if err != nil {
		return err
	}
	reqest.Header.Set("Content-Type", "application/json;charset=utf-8")
	resp, err := client.Do(reqest)
	if err != nil {
		return err
	}
	err = json.NewDecoder(resp.Body).Decode(&response)
	if err != nil {
		return err
	}
	if response.ErrCode != 0 {
		return fmt.Errorf("dingding webhook send error, code %d, msg %s", response.ErrCode, response.ErrMsg)
	}
	return nil
}
