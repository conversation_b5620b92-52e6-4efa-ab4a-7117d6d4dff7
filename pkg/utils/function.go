package utils

import (
	"context"
	"crypto/md5"
	"crypto/rand"
	"encoding/hex"
	"errors"
	"fmt"
	"io"
	"math"
	"math/big"
	"net"
	"os"
	"regexp"
	"strings"
	"time"

	"github.com/google/uuid"
)

// ValidatePhoneNum 校验手机号 非法返回false
func ValidatePhoneNum(number string) bool {
	regular := `^((13[0-9])|(14[5,7])|(15[0-3,5-9])|(17[0,1,3,5-8])|(18[0-9])|166|198|199|(147))\d{8}$`
	reg := regexp.MustCompile(regular)
	return reg.MatchString(number)
}

func RedisCtx() (context.Context, context.CancelFunc) {
	return context.WithTimeout(context.Background(), 3*time.Second)
}

// IsAccept 检查是否tcp可达
func IsAccept(ip, port string) bool {
	conn, err := net.DialTimeout("tcp", net.JoinHostPort(ip, port), time.Second)
	if err != nil {
		return false
	}
	conn.Close()
	return true
}

func SaveFile(name, path string, data []byte) (string, error) {
	var (
		uuidName = fmt.Sprintf("%s.%s", uuid.NewString(), getExt(name))
	)
	return uuidName, os.WriteFile(
		fmt.Sprint(path, uuidName),
		data,
		0644,
	)
}

func getExt(name string) string {
	return name[strings.LastIndex(name, ".")+1:]
}

func DeleteFile(name string) error {
	return os.Remove(name)
}

// CopyFile 复制文件 重新命名
func CopyFile(src string) (string, error) {
	split := strings.Split(src, "/")
	//static/scene/xxx.jpg
	if len(split) != 3 {
		return "", errors.New("文件路径不正确")
	}
	var (
		ext  = getExt(split[len(split)-1])
		name = fmt.Sprintf("%s.%s", uuid.NewString(), ext)
		path = strings.Join(split[:len(split)-1], "/")
	)
	fd, err := os.Open(src)
	if err != nil {
		return "", err
	}

	data, err := io.ReadAll(fd)
	if err != nil {
		return "", err
	}

	err = os.WriteFile(fmt.Sprintf("%s/%s", path, name), data, 0644)

	return name, nil
}

func Contains(slice []string, item string) bool {
	for _, v := range slice {
		if v == item {
			return true
		}
	}
	return false
}
func ContainsIgnoreCase(slice []string, item string) bool {
	itemLower := strings.ToLower(item)
	for _, v := range slice {
		if strings.ToLower(v) == itemLower {
			return true
		}
	}
	return false
}
func GetTimeStr(ts int64) string {
	timeLayout := "2006-01-02 15:04:05"
	str := time.Unix(ts, 0).Format(timeLayout)
	return str
}

func GetRandomStr(size int) string {
	strandom := ""
	for i := 0; i < size; i++ {
		result, _ := rand.Int(rand.Reader, big.NewInt(255))
		s := fmt.Sprintf("%02x", result)
		strandom += s
	}
	return strandom
}

const (
	KB = 1 << 10 // 1024
	MB = 1 << 20 // 1024^2
	GB = 1 << 30 // 1024^3
	TB = 1 << 40 // 1024^4
)

func HumanizeUnit(bytes int64) string {
	switch {
	case bytes >= TB:
		return fmt.Sprintf("%.2f TB", float64(bytes)/float64(TB))
	case bytes >= GB:
		return fmt.Sprintf("%.2f GB", float64(bytes)/float64(GB))
	case bytes >= MB:
		return fmt.Sprintf("%.2f MB", float64(bytes)/float64(MB))
	case bytes >= KB:
		return fmt.Sprintf("%.2f kB", float64(bytes)/float64(KB))
	default:
		return fmt.Sprintf("%d B", bytes)
	}
}

func Round(f float64, n int) float64 {
	pow10_n := math.Pow10(n)
	return math.Trunc((f+0.5/pow10_n)*pow10_n) / pow10_n
}
func FileMD5(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()
	return sumMD5(file)
}

func FDMD5(fd io.Reader) (string, error) {
	return sumMD5(fd)
}

func sumMD5(reader io.Reader) (string, error) {
	hash := md5.New()
	_, _ = io.Copy(hash, reader)
	return hex.EncodeToString(hash.Sum(nil)), nil
}
