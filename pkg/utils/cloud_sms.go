package utils

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"
)

// 泰物云短信盒子的推送  短信 电话 按照模板组织
type CloudNoticePush struct {
	To         string `json:"To" binding:"required"`
	Type       string `json:"Type" binding:"required"`
	DeviceName string `json:"device_name"`
	UnitName   string `json:"unit_name"`
	Level      string `json:"level"`
	Message    string `json:"message"`
	Value      string `json:"value"`
	Time       string `json:"time"`
}

func SendCloudMessage(ip string, ap *CloudNoticePush) error {
	jc, err := json.Marshal(ap)
	if err != nil {
		return errors.New("SendCloudMessage to json fail")
	}
	client := &http.Client{
		Timeout: 3 * time.Second,
	}
	url := fmt.Sprintf("http://%s:80/cgi-bin/CloudNoticePush", ip)
	reqest, err := http.NewRequest(
		"POST",
		url,
		bytes.NewReader(jc),
	)
	if err != nil {
		return err
	}
	reqest.Header.Set("Content-Type", "application/json;charset=utf-8")
	_, err = client.Do(reqest)
	if err != nil {
		return err
	}
	return nil
}
func SendCloudCall(ip, to, device_name, unit_name, level, messgae, value, url string) error {
	noticePush := CloudNoticePush{
		Type:       "Call",
		To:         to,
		DeviceName: device_name,
		UnitName:   unit_name,
		Level:      level,
		Message:    messgae,
		Value:      value,
		Time:       time.Now().Format("2006-01-02 15:04:05"),
	}
	return SendCloudMessage(ip, &noticePush)
}
func SendCloudSms(ip, to, device_name, unit_name, level, message, value, url string) error {
	noticePush := CloudNoticePush{
		Type:       "SMS",
		To:         to,
		DeviceName: device_name,
		UnitName:   unit_name,
		Level:      level,
		Message:    message,
		Value:      value,
		Time:       time.Now().Format("2006-01-02 15:04:05"),
	}
	return SendCloudMessage(ip, &noticePush)
}
func SendCloudWeichat(ip, to, device_name, unit_name, level, message, value, url string) error {
	m := fmt.Sprintf(
		"项目通知,时间:%s,设备为：%s, 测点为：%s,当前值为：%s，%s！",
		time.Now().Format("2006-01-02 15:04:05"),
		device_name,
		unit_name,
		value,
		message,
	)
	noticePush := CloudNoticePush{
		Type:       "WeiChat",
		To:         to,
		DeviceName: device_name,
		UnitName:   unit_name,
		Level:      level,
		Message:    m,
		Value:      value,
	}
	return SendCloudMessage(ip, &noticePush)
}
func SendCloudDingDing(ip, to, device_name, unit_name, level, message, value, url string) error {
	m := fmt.Sprintf(
		"项目通知,时间:%s,设备为：%s, 测点为：%s,当前值为：%s，%s！",
		time.Now().Format("2006-01-02 15:04:05"),
		device_name,
		unit_name,
		value,
		message,
	)
	noticePush := CloudNoticePush{
		Type:       "Ding",
		To:         to,
		DeviceName: device_name,
		UnitName:   unit_name,
		Level:      level,
		Message:    m,
		Value:      value,
	}
	return SendCloudMessage(ip, &noticePush)
}
