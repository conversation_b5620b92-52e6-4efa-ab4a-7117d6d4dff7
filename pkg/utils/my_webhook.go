package utils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"time"
)

/*
用于第三方短信平台或者其他平台的对接
*/
type MyWebhookMsg struct {
	Msgtype string `json:"msgtype"`
	Text    struct {
		Content string `json:"content"`
	} `json:"text"`
}
type myWebhookResp struct {
	ErrCode int    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
}

func SendMyWebhookMsg(url, message string) error {
	var (
		response myWebhookResp
		msg      MyWebhookMsg
	)
	msg.Msgtype = "text"
	msg.Text.Content = message
	jc, err := json.Marshal(msg)
	if err != nil {
		return err
	}
	client := &http.Client{
		Timeout: 3 * time.Second,
	}
	reqest, err := http.NewRequest(
		"POST",
		url,
		bytes.NewReader(jc),
	)
	if err != nil {
		return err
	}
	reqest.Header.Set("Content-Type", "application/json;charset=utf-8")
	resp, err := client.Do(reqest)
	if err != nil {
		return err
	}
	err = json.NewDecoder(resp.Body).Decode(&response)
	if err != nil {
		return err
	}
	if response.ErrCode != 0 {
		return fmt.Errorf("dingding webhook send error, code %d, msg %s", response.ErrCode, response.ErrMsg)
	}
	return nil
}
