package utils

import (
	"gopkg.in/gomail.v2"
)

// send email

func SendEmailMessage(emailDialer *gomail.Dialer, fromEmail, message string, subject string, to ...string) error {
	var (
		messageSend *gomail.Message = gomail.NewMessage()
	)
	messageSend.SetHeader("From", fromEmail)
	messageSend.SetHeader("To", to...)
	messageSend.SetBody("text/html", message)
	messageSend.SetHeader("Subject", subject)
	return emailDialer.DialAndSend(messageSend)
}
