package utils

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"
)

// 4g 短信电话模块 泰物新版本4G
type NoticePush struct {
	To       string `json:"To" binding:"required"`
	Type     string `json:"Type" binding:"required"`
	Encoding string `json:"Encoding" binding:"required"`
	Sub      string `json:"Sub" `
	Text     string `json:"Text" binding:"required"`
}

const JSONTYPE = "application/json"

func Send4gMessage(ip string, ap *NoticePush) error {
	jc, err := json.Marshal(ap)
	if err != nil {
		return errors.New("Send4gMessage to json fail")
	}
	client := &http.Client{
		Timeout: 3 * time.Second,
	}
	url := fmt.Sprintf("http://%s:80/cgi-bin/NoticePush", ip)
	reqest, err := http.NewRequest(
		"POST",
		url,
		bytes.NewReader(jc),
	)
	if err != nil {
		return err
	}
	reqest.Header.Set("Content-Type", "application/json;charset=utf-8")
	_, err = client.Do(reqest)
	if err != nil {
		return err
	}
	return nil
}
func Send4gCall(ip, message string, to string) error {
	noticePush := NoticePush{
		Type:     "Call",
		Encoding: "UTF-8",
		Text:     message,
		To:       to,
	}
	return Send4gMessage(ip, &noticePush)
}
func Send4gSMS(ip, message string, to string) error {
	noticePush := NoticePush{
		Type:     "SMS",
		Encoding: "UTF-8",
		Text:     message,
		To:       to,
	}
	return Send4gMessage(ip, &noticePush)
}
func Send4gBroadcast(ip, message string) error {
	noticePush := NoticePush{
		Type:     "BroadCast",
		Encoding: "UTF-8",
		Text:     message,
		To:       "1",
	}
	return Send4gMessage(ip, &noticePush)
}
